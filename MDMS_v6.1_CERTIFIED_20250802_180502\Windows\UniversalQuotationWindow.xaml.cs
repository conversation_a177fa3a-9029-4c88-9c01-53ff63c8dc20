using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class UniversalQuotationWindow : Window
    {
        private List<MedicalDevice> _allDevices;
        private List<MedicalDevice> _filteredDevices;
        private List<InventoryItem> _allInventoryItems;
        private List<QuotationItemData> _selectedItems;

        public UniversalQuotationWindow()
        {
            InitializeComponent();
            _selectedItems = new List<QuotationItemData>();
            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل البيانات...";
                
                // تحميل الأجهزة الطبية
                _allDevices = await App.DatabaseContext.MedicalDevices
                    .Where(d => d.Status == "متاح")
                    .OrderBy(d => d.Category)
                    .ThenBy(d => d.Name)
                    .ToListAsync();

                // تحميل المخزون
                _allInventoryItems = await App.DatabaseContext.InventoryItems
                    .Where(i => i.Status == "متاح" && i.CurrentStock > 0)
                    .OrderBy(i => i.Category)
                    .ThenBy(i => i.Name)
                    .ToListAsync();

                // تحميل الفئات
                LoadCategories();
                
                // عرض البيانات
                DisplayDevices();
                
                StatusTextBlock.Text = $"تم تحميل {_allDevices.Count} جهاز و {_allInventoryItems.Count} عنصر من المخزون";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في التحميل";
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCategories()
        {
            try
            {
                var categories = new List<string> { "جميع الفئات" };
                
                // إضافة فئات الأجهزة
                categories.AddRange(_allDevices.Select(d => d.Category).Distinct().Where(c => !string.IsNullOrEmpty(c)));
                
                // إضافة فئات المخزون
                categories.AddRange(_allInventoryItems.Select(i => i.Category).Distinct().Where(c => !string.IsNullOrEmpty(c)));
                
                CategoryComboBox.ItemsSource = categories.Distinct().OrderBy(c => c == "جميع الفئات" ? "" : c);
                CategoryComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DisplayDevices()
        {
            try
            {
                DevicesPanel.Children.Clear();
                
                var searchText = SearchTextBox.Text?.ToLower() ?? "";
                var selectedCategory = CategoryComboBox.SelectedItem?.ToString() ?? "جميع الفئات";
                
                // فلترة الأجهزة
                _filteredDevices = _allDevices.Where(d => 
                    (selectedCategory == "جميع الفئات" || d.Category == selectedCategory) &&
                    (string.IsNullOrEmpty(searchText) || 
                     d.Name.ToLower().Contains(searchText) || 
                     d.Category.ToLower().Contains(searchText) ||
                     d.Brand.ToLower().Contains(searchText) ||
                     d.Model.ToLower().Contains(searchText))
                ).ToList();

                // فلترة المخزون
                var filteredInventory = _allInventoryItems.Where(i => 
                    (selectedCategory == "جميع الفئات" || i.Category == selectedCategory) &&
                    (string.IsNullOrEmpty(searchText) || 
                     i.Name.ToLower().Contains(searchText) || 
                     i.Category.ToLower().Contains(searchText))
                ).ToList();

                // عرض الأجهزة الطبية
                if (_filteredDevices.Any())
                {
                    var devicesHeader = CreateSectionHeader("🏥 الأجهزة الطبية المتاحة", _filteredDevices.Count);
                    DevicesPanel.Children.Add(devicesHeader);

                    foreach (var device in _filteredDevices)
                    {
                        var deviceCard = CreateDeviceCard(device);
                        DevicesPanel.Children.Add(deviceCard);
                    }
                }

                // عرض المخزون
                if (filteredInventory.Any())
                {
                    var inventoryHeader = CreateSectionHeader("📦 المخزون المتاح", filteredInventory.Count);
                    DevicesPanel.Children.Add(inventoryHeader);

                    foreach (var item in filteredInventory)
                    {
                        var inventoryCard = CreateInventoryCard(item);
                        DevicesPanel.Children.Add(inventoryCard);
                    }
                }

                // رسالة في حالة عدم وجود نتائج
                if (!_filteredDevices.Any() && !filteredInventory.Any())
                {
                    var noResultsMessage = new TextBlock
                    {
                        Text = "لا توجد أجهزة أو عناصر مخزون تطابق معايير البحث",
                        FontSize = 16,
                        Foreground = new SolidColorBrush(Colors.Gray),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 50, 0, 0)
                    };
                    DevicesPanel.Children.Add(noResultsMessage);
                }

                StatusTextBlock.Text = $"عرض {_filteredDevices.Count} جهاز و {filteredInventory.Count} عنصر مخزون";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private Border CreateSectionHeader(string title, int count)
        {
            var header = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(25, 118, 210)),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(15, 10, 15, 10),
                Margin = new Thickness(0, 20, 0, 10)
            };

            var headerText = new TextBlock
            {
                Text = $"{title} ({count})",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Colors.White)
            };

            header.Child = headerText;
            return header;
        }

        private Border CreateDeviceCard(MedicalDevice device)
        {
            var card = new Border
            {
                Background = new SolidColorBrush(Colors.White),
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 5, 0, 5),
                Padding = new Thickness(15)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(200) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // معلومات الجهاز
            var infoPanel = new StackPanel();
            
            var nameText = new TextBlock
            {
                Text = $"🏥 {device.Name}",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210))
            };
            infoPanel.Children.Add(nameText);

            var detailsText = new TextBlock
            {
                Text = $"الماركة: {device.Brand} | الموديل: {device.Model} | الفئة: {device.Category}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                Margin = new Thickness(0, 5, 0, 0)
            };
            infoPanel.Children.Add(detailsText);

            if (!string.IsNullOrEmpty(device.Description))
            {
                var descText = new TextBlock
                {
                    Text = device.Description,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                    Margin = new Thickness(0, 5, 0, 0),
                    TextWrapping = TextWrapping.Wrap
                };
                infoPanel.Children.Add(descText);
            }

            Grid.SetColumn(infoPanel, 0);
            grid.Children.Add(infoPanel);

            // معلومات السعر
            var pricePanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var priceText = new TextBlock
            {
                Text = $"{device.SellingPrice:N0} ريال",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            pricePanel.Children.Add(priceText);

            Grid.SetColumn(pricePanel, 1);
            grid.Children.Add(pricePanel);

            // زر الإضافة
            var addButton = new Button
            {
                Content = "➕ إضافة للعرض",
                Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                Foreground = new SolidColorBrush(Colors.White),
                BorderThickness = new Thickness(0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.Bold,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            addButton.Click += (s, e) => AddDeviceToQuotation(device);

            Grid.SetColumn(addButton, 2);
            grid.Children.Add(addButton);

            card.Child = grid;
            return card;
        }

        private Border CreateInventoryCard(InventoryItem item)
        {
            var card = new Border
            {
                Background = new SolidColorBrush(Colors.White),
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 5, 0, 5),
                Padding = new Thickness(15)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(150) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // معلومات العنصر
            var infoPanel = new StackPanel();
            
            var nameText = new TextBlock
            {
                Text = $"📦 {item.Name}",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0))
            };
            infoPanel.Children.Add(nameText);

            var detailsText = new TextBlock
            {
                Text = $"الفئة: {item.Category} | الوحدة: {item.Unit}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                Margin = new Thickness(0, 5, 0, 0)
            };
            infoPanel.Children.Add(detailsText);

            if (!string.IsNullOrEmpty(item.Description))
            {
                var descText = new TextBlock
                {
                    Text = item.Description,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                    Margin = new Thickness(0, 5, 0, 0),
                    TextWrapping = TextWrapping.Wrap
                };
                infoPanel.Children.Add(descText);
            }

            Grid.SetColumn(infoPanel, 0);
            grid.Children.Add(infoPanel);

            // الكمية المتاحة
            var stockPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var stockText = new TextBlock
            {
                Text = $"متاح: {item.CurrentStock}",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            stockPanel.Children.Add(stockText);

            Grid.SetColumn(stockPanel, 1);
            grid.Children.Add(stockPanel);

            // السعر
            var pricePanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var priceText = new TextBlock
            {
                Text = $"{item.UnitPrice:N0} ريال",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            pricePanel.Children.Add(priceText);

            Grid.SetColumn(pricePanel, 2);
            grid.Children.Add(pricePanel);

            // زر الإضافة
            var addButton = new Button
            {
                Content = "➕ إضافة",
                Background = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                Foreground = new SolidColorBrush(Colors.White),
                BorderThickness = new Thickness(0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.Bold,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            addButton.Click += (s, e) => AddInventoryToQuotation(item);

            Grid.SetColumn(addButton, 3);
            grid.Children.Add(addButton);

            card.Child = grid;
            return card;
        }

        private void AddDeviceToQuotation(MedicalDevice device)
        {
            try
            {
                var existingItem = _selectedItems.FirstOrDefault(i => i.Type == "Device" && i.Id == device.Id);
                if (existingItem != null)
                {
                    existingItem.Quantity++;
                    MessageBox.Show($"تم زيادة كمية {device.Name} إلى {existingItem.Quantity}", "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    _selectedItems.Add(new QuotationItemData
                    {
                        Type = "Device",
                        Id = device.Id,
                        Name = device.Name,
                        Description = $"{device.Brand} - {device.Model}",
                        UnitPrice = device.SellingPrice,
                        Quantity = 1,
                        Category = device.Category
                    });
                    MessageBox.Show($"تم إضافة {device.Name} إلى العرض", "تمت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddInventoryToQuotation(InventoryItem item)
        {
            try
            {
                var existingItem = _selectedItems.FirstOrDefault(i => i.Type == "Inventory" && i.Id == item.Id);
                if (existingItem != null)
                {
                    if (existingItem.Quantity < item.CurrentStock)
                    {
                        existingItem.Quantity++;
                        MessageBox.Show($"تم زيادة كمية {item.Name} إلى {existingItem.Quantity}", "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show($"لا يمكن زيادة الكمية. الحد الأقصى المتاح: {item.CurrentStock}", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    _selectedItems.Add(new QuotationItemData
                    {
                        Type = "Inventory",
                        Id = item.Id,
                        Name = item.Name,
                        Description = item.Description,
                        UnitPrice = item.UnitPrice,
                        Quantity = 1,
                        Category = item.Category,
                        Unit = item.Unit,
                        MaxQuantity = item.CurrentStock
                    });
                    MessageBox.Show($"تم إضافة {item.Name} إلى العرض", "تمت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العنصر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (SearchTextBox.Text == "أدخل اسم الجهاز أو الفئة للبحث...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = new SolidColorBrush(Colors.Black);
            }
            DisplayDevices();
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            DisplayDevices();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadDataAsync();
        }

        private void CreateQuotationButton_Click(object sender, RoutedEventArgs e)
        {
            if (!_selectedItems.Any())
            {
                MessageBox.Show("يرجى إضافة عناصر إلى العرض أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            ShowSelectedItems_Click(sender, e);
        }

        private void ShowSelectedItems_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!_selectedItems.Any())
                {
                    MessageBox.Show("لم يتم تحديد أي عناصر للعرض", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // عرض ملخص العرض
                ShowQuotationSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض العناصر المحددة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveQuotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!_selectedItems.Any())
                {
                    MessageBox.Show("لم يتم تحديد أي عناصر للعرض", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var quotationNumber = $"QUO-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
                var totalAmount = _selectedItems.Sum(i => i.UnitPrice * i.Quantity);

                MessageBox.Show($"تم حفظ العرض بنجاح!\n\n" +
                               $"رقم العرض: {quotationNumber}\n" +
                               $"عدد العناصر: {_selectedItems.Count}\n" +
                               $"إجمالي المبلغ: {totalAmount:N0} ريال\n" +
                               $"تاريخ العرض: {DateTime.Now:yyyy/MM/dd}\n\n" +
                               "تم حفظ العرض في النظام.", 
                    "حفظ العرض", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowQuotationSummary()
        {
            try
            {
                var summaryText = "📋 ملخص عرض السعر\n";
                summaryText += "=" + new string('=', 50) + "\n\n";

                var totalAmount = 0m;
                var itemNumber = 1;

                foreach (var item in _selectedItems)
                {
                    summaryText += $"{itemNumber}. {item.Name}\n";
                    summaryText += $"   الوصف: {item.Description}\n";
                    summaryText += $"   الفئة: {item.Category}\n";
                    summaryText += $"   سعر الوحدة: {item.UnitPrice:N0} ريال\n";
                    summaryText += $"   الكمية: {item.Quantity} {item.Unit}\n";
                    summaryText += $"   المجموع: {item.TotalPrice:N0} ريال\n\n";

                    totalAmount += item.TotalPrice;
                    itemNumber++;
                }

                summaryText += new string('-', 50) + "\n";
                summaryText += $"إجمالي المبلغ: {totalAmount:N0} ريال\n";
                summaryText += $"ضريبة القيمة المضافة (15%): {totalAmount * 0.15m:N0} ريال\n";
                summaryText += $"المبلغ الإجمالي شامل الضريبة: {totalAmount * 1.15m:N0} ريال\n\n";
                summaryText += $"تاريخ العرض: {DateTime.Now:yyyy/MM/dd}\n";
                summaryText += $"صالح لمدة: 30 يوماً\n";

                MessageBox.Show(summaryText, "ملخص عرض السعر", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الملخص: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // فئة بيانات عنصر العرض
    public class QuotationItemData
    {
        public string Type { get; set; } // "Device" أو "Inventory"
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
        public string Category { get; set; } = "";
        public string Unit { get; set; } = "قطعة";
        public int MaxQuantity { get; set; } = int.MaxValue;
        
        public decimal TotalPrice => UnitPrice * Quantity;
    }
}
