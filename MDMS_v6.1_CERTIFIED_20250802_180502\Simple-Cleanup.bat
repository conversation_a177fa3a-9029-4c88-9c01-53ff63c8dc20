@echo off
title Project Cleanup Tool

echo ========================================
echo    Project Cleanup Tool
echo    Medical Devices Management System
echo ========================================
echo.

echo Starting cleanup process...
echo.

REM Clean build artifacts
echo [1/4] Cleaning build artifacts...
if exist "bin" (
    echo   - Removing bin folder...
    rmdir /s /q "bin" 2>nul
    echo     Done
)

if exist "obj" (
    echo   - Removing obj folder...
    rmdir /s /q "obj" 2>nul
    echo     Done
)

echo.

REM Clean old documentation files
echo [2/4] Cleaning old documentation...
for %%f in (
    "BUILD_ERRORS_FIXED.md"
    "COMPREHENSIVE_TESTING_REPORT.md"
    "DATABASE_FIX_README.md"
    "DEVICES_ENHANCEMENT_README.md"
    "FINAL_SUCCESS_REPORT.md"
    "FINAL_TESTING_REPORT.md"
    "MAINTENANCE_SYSTEM_FIXED.md"
    "MAINTENANCE_SYSTEM_UPDATE_TEST.md"
    "MULTIPLE_DEVICES_FEATURE.md"
    "QUICK_TEST_GUIDE.md"
    "SERIAL_NUMBERS_MANAGEMENT_FIX.md"
    "SIMPLE_DEBUG_PLAN.md"
    "TEST_MULTIPLE_DEVICES.md"
    "TEST_SERIAL_NUMBERS.md"
    "UPDATED_SYSTEM_TEST.md"
) do (
    if exist %%f (
        echo   - Removing %%f
        del /q %%f 2>nul
    )
)

echo.

REM Clean old batch files
echo [3/4] Cleaning old batch files...
for %%f in (
    "CREATE_DESKTOP_SHORTCUT.bat"
    "CREATE_DESKTOP_SHORTCUT_v2.1.bat"
    "CreateShortcut.bat"
    "RESET_DATABASE.bat"
    "RUN_INTEGRATED_SYSTEM.bat"
    "RUN_v2.1.bat"
    "START.bat"
) do (
    if exist %%f (
        echo   - Removing %%f
        del /q %%f 2>nul
    )
)

echo.

REM Clean old PowerShell files
echo [4/4] Cleaning old PowerShell files...
for %%f in (
    "Create-Shortcut.ps1"
    "Create-Shortcut-v2.1.ps1"
) do (
    if exist %%f (
        echo   - Removing %%f
        del /q %%f 2>nul
    )
)

REM Clean old documentation
for %%f in (
    "DESKTOP_SHORTCUT_GUIDE.md"
    "DESKTOP_SHORTCUT_SUMMARY.md"
    "MANUAL_SHORTCUT_GUIDE.md"
) do (
    if exist %%f (
        echo   - Removing %%f
        del /q %%f 2>nul
    )
)

REM Clean old distribution folder
if exist "Distribution_Updated" (
    echo   - Removing Distribution_Updated folder...
    rmdir /s /q "Distribution_Updated" 2>nul
    echo     Done
)

REM Clean SQL files
if exist "AddDocumentsPathColumn.sql" (
    echo   - Removing AddDocumentsPathColumn.sql
    del /q "AddDocumentsPathColumn.sql" 2>nul
)

echo.

echo ========================================
echo    Cleanup Summary
echo ========================================
echo.
echo Cleanup completed successfully!
echo.
echo What was removed:
echo - Build artifacts (bin, obj folders)
echo - Old documentation files
echo - Duplicate batch files
echo - Old PowerShell scripts
echo - Temporary files
echo - Old distribution folder
echo.
echo What remains:
echo - Core application files
echo - Current database
echo - Distribution folder (v2.1)
echo - Essential documentation
echo - Working batch files
echo.
echo The project is now clean and optimized!
echo.

pause
