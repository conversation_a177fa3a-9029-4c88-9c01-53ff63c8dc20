﻿#pragma checksum "..\..\..\..\..\Windows\SmartSystemWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "15796F607BF4EE1BFD1F7438D48BFFF40396FABA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// SmartSystemWindow
    /// </summary>
    public partial class SmartSystemWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 34 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SmartSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SalesStatsGrid;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid InventoryStatsGrid;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DevicesStatsGrid;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CustomersStatsGrid;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid FinancialStatsGrid;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateAlertsButton;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AlertTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AlertPriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MarkAllReadButton;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid AlertsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AlertsCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HighPriorityCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ItemTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AdvancedSearchButton;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SearchResultsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SearchResultsCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WarrantyAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WarrantyDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MaintenanceAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaintenanceDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox InventoryAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExpiryAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpiryDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxSearchResultsTextBox;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoSuggestionsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RebuildIndexButton;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatsUpdateIntervalComboBox;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RealTimeStatsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 444 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RecalculateStatsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/smartsystemwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SmartSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 37 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.SmartSearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SmartSearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 38 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.SmartSearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SmartSearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 38 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.SmartSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SmartSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SalesStatsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.InventoryStatsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.DevicesStatsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.CustomersStatsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.FinancialStatsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.GenerateAlertsButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.GenerateAlertsButton.Click += new System.Windows.RoutedEventHandler(this.GenerateAlertsButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AlertTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 147 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.AlertTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AlertTypeFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.AlertPriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 156 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.AlertPriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AlertPriorityFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.MarkAllReadButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.MarkAllReadButton.Click += new System.Windows.RoutedEventHandler(this.MarkAllReadButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.AlertsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 17:
            this.AlertsCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.HighPriorityCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.OverdueCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ItemTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 21:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 24:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 25:
            this.AdvancedSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 303 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.AdvancedSearchButton.Click += new System.Windows.RoutedEventHandler(this.AdvancedSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.SearchResultsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 28:
            this.SearchResultsCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.WarrantyAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.WarrantyDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.MaintenanceAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.MaintenanceDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 33:
            this.InventoryAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 34:
            this.ExpiryAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.ExpiryDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            this.MaxSearchResultsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 37:
            this.AutoSuggestionsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 38:
            this.RebuildIndexButton = ((System.Windows.Controls.Button)(target));
            
            #line 414 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.RebuildIndexButton.Click += new System.Windows.RoutedEventHandler(this.RebuildIndexButton_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.StatsUpdateIntervalComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 40:
            this.RealTimeStatsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 41:
            this.RecalculateStatsButton = ((System.Windows.Controls.Button)(target));
            
            #line 447 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            this.RecalculateStatsButton.Click += new System.Windows.RoutedEventHandler(this.RecalculateStatsButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 14:
            
            #line 216 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MarkReadButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 219 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SnoozeButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 222 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CompleteButton_Click);
            
            #line default
            #line hidden
            break;
            case 27:
            
            #line 326 "..\..\..\..\..\Windows\SmartSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewSearchResultButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

