<Window x:Class="MedicalDevicesManager.Windows.UniversalQuotationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام عروض الأسعار الشامل" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">
    
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel>
                <TextBlock Text="🏥 نظام عروض الأسعار الشامل" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="عروض أسعار لجميع الأجهزة الطبية المتاحة في المخزون" 
                          FontSize="16" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Search and Filter Panel -->
        <Border Grid.Row="1" Background="White" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🔍 البحث:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                    <TextBox x:Name="SearchTextBox" Width="300" Padding="8" 
                             TextChanged="SearchTextBox_TextChanged"
                             Text="أدخل اسم الجهاز أو الفئة للبحث..."/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0,0,0">
                    <TextBlock Text="📂 الفئة:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                    <ComboBox x:Name="CategoryComboBox" Width="200" Padding="8"
                              SelectionChanged="CategoryComboBox_SelectionChanged"/>
                </StackPanel>
                
                <Button Grid.Column="2" Content="🔄 تحديث" Padding="15,8" Margin="20,0,0,0"
                        Background="#4CAF50" Foreground="White" BorderThickness="0"
                        FontWeight="Bold" Click="RefreshButton_Click"/>
                        
                <Button Grid.Column="3" Content="📊 إنشاء عرض سعر" Padding="15,8" Margin="10,0,0,0"
                        Background="#FF9800" Foreground="White" BorderThickness="0"
                        FontWeight="Bold" Click="CreateQuotationButton_Click"/>
            </Grid>
        </Border>

        <!-- Content Area -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel x:Name="DevicesPanel">
                <!-- سيتم ملء هذا القسم ديناميكياً -->
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="3" Background="#1976D2" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="StatusTextBlock" Text="جاري التحميل..." 
                              Foreground="White" VerticalAlignment="Center" FontWeight="Bold"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="📋 عرض العناصر المحددة" Padding="15,8" Margin="10,0"
                            Background="#4CAF50" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="ShowSelectedItems_Click"/>
                    <Button Content="💾 حفظ العرض" Padding="15,8" Margin="10,0"
                            Background="#2196F3" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="SaveQuotation_Click"/>
                    <Button Content="❌ إغلاق" Padding="15,8" Margin="10,0"
                            Background="#F44336" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="Close_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
