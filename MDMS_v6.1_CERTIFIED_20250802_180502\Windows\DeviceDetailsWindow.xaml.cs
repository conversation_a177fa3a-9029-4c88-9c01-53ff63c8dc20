using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class DeviceDetailsWindow : Window
    {
        private MedicalDevice _device;
        private List<DeviceSerialNumber> _serialNumbers;
        private List<Sale> _deviceSales;
        private List<MaintenanceRecord> _deviceMaintenance;
        private List<DeviceInstallation> _deviceInstallations;
        private DeviceSerialNumber _selectedSerialNumber;
        private bool _showAllData = true;

        public DeviceDetailsWindow(MedicalDevice device)
        {
            InitializeComponent();
            _device = device;
            LoadDeviceDetailsAsync();
        }
        
        private async void LoadDeviceDetailsAsync()
        {
            try
            {
                // تحميل المعلومات الأساسية
                LoadBasicInfo();

                // تحميل الأرقام التسلسلية
                await LoadSerialNumbersAsync();

                // تحميل معلومات المستندات
                LoadDocumentsInfo();

                // تحميل معلومات الملفات في التبويب الجديد
                LoadFilesInfo();

                // تحميل البيانات المرتبطة
                await LoadRelatedDataAsync();

                // تحديث واجهة الفلترة
                UpdateFilterDisplay();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الجهاز: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void LoadBasicInfo()
        {
            try
            {
                if (_device == null) return;

                // العنوان
                if (DeviceNameTextBlock != null)
                    DeviceNameTextBlock.Text = _device.Name ?? "غير محدد";
                if (SerialNumberTextBlock != null)
                    SerialNumberTextBlock.Text = _device.SerialNumber ?? "غير محدد";
                Title = $"تفاصيل الجهاز - {_device.Name ?? "غير محدد"}";

                // المعلومات الأساسية
                if (BrandTextBlock != null)
                    BrandTextBlock.Text = _device.Brand ?? "غير محدد";
                if (ModelTextBlock != null)
                    ModelTextBlock.Text = _device.Model ?? "غير محدد";
                if (CategoryTextBlock != null)
                    CategoryTextBlock.Text = _device.Category ?? "غير محدد";
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = _device.Status ?? "غير محدد";
                if (DescriptionTextBlock != null)
                    DescriptionTextBlock.Text = string.IsNullOrEmpty(_device.Description) ? "لا يوجد وصف" : _device.Description;

                // المعلومات المالية
                if (PurchasePriceTextBlock != null)
                    PurchasePriceTextBlock.Text = $"{_device.PurchasePrice:N0} د.ع";
                if (SellingPriceTextBlock != null)
                    SellingPriceTextBlock.Text = $"{_device.SellingPrice:N0} د.ع";
                if (SupplierTextBlock != null)
                    SupplierTextBlock.Text = string.IsNullOrEmpty(_device.Supplier) ? "غير محدد" : _device.Supplier;
                if (LocationTextBlock != null)
                    LocationTextBlock.Text = string.IsNullOrEmpty(_device.Location) ? "غير محدد" : _device.Location;

                // التواريخ
                if (PurchaseDateTextBlock != null)
                    PurchaseDateTextBlock.Text = _device.PurchaseDate.ToString("dd/MM/yyyy");
                if (CreatedDateTextBlock != null)
                    CreatedDateTextBlock.Text = _device.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                if (WarrantyStartTextBlock != null)
                    WarrantyStartTextBlock.Text = _device.WarrantyStartDate.ToString("dd/MM/yyyy");
                if (WarrantyEndTextBlock != null)
                    WarrantyEndTextBlock.Text = _device.WarrantyEndDate.ToString("dd/MM/yyyy");

                // معلومات إضافية
                if (NotesTextBlock != null)
                    NotesTextBlock.Text = "لا توجد ملاحظات إضافية";
                if (SpecificationsTextBlock != null)
                    SpecificationsTextBlock.Text = string.IsNullOrEmpty(_device.Description) ? "لا توجد مواصفات محددة" : _device.Description;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعلومات الأساسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async System.Threading.Tasks.Task LoadSerialNumbersAsync()
        {
            try
            {
                if (_device?.Id > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"تحميل الأرقام التسلسلية للجهاز ID: {_device.Id}");

                    // تحميل الأرقام التسلسلية بشكل صريح لتجنب أخطاء الأعمدة المفقودة
                    var serialNumbersQuery = App.DatabaseContext.DeviceSerialNumbers
                        .Where(s => s.DeviceId == _device.Id && s.IsActive)
                        .Select(s => new DeviceSerialNumber
                        {
                            Id = s.Id,
                            DeviceId = s.DeviceId,
                            SerialNumber = s.SerialNumber,
                            ComponentName = s.ComponentName,
                            ComponentType = s.ComponentType,
                            Status = s.Status,
                            IsActive = s.IsActive,
                            CreatedDate = s.CreatedDate,
                            LastUpdated = s.LastUpdated
                        })
                        .OrderBy(s => s.ComponentName);

                    var deviceSerialNumbers = await serialNumbersQuery.ToListAsync();

                    // إنشاء قائمة موحدة للأرقام التسلسلية
                    _serialNumbers = new List<DeviceSerialNumber>();

                    // إضافة الأرقام التسلسلية من الجدول المنفصل
                    _serialNumbers.AddRange(deviceSerialNumbers);

                    // إذا لم توجد أرقام في الجدول المنفصل وكان للجهاز رقم تسلسلي أساسي
                    if (_serialNumbers.Count == 0 && !string.IsNullOrEmpty(_device.SerialNumber))
                    {
                        // إضافة الرقم التسلسلي الأساسي للجهاز
                        _serialNumbers.Add(new DeviceSerialNumber
                        {
                            Id = 0, // رقم وهمي للعرض فقط
                            DeviceId = _device.Id,
                            SerialNumber = _device.SerialNumber,
                            ComponentName = "الجهاز الأساسي",
                            ComponentType = "رئيسي",
                            Status = "نشط",
                            IsActive = true,
                            CreatedDate = _device.CreatedDate,
                            LastUpdated = _device.LastUpdated
                        });
                    }

                    System.Diagnostics.Debug.WriteLine($"تم العثور على {_serialNumbers.Count} أرقام تسلسلية");

                    // تحديث واجهة المستخدم
                    if (SerialNumbersDataGrid != null)
                    {
                        SerialNumbersDataGrid.ItemsSource = null; // مسح أولاً
                        SerialNumbersDataGrid.ItemsSource = _serialNumbers;
                        SerialNumbersDataGrid.Items.Refresh(); // إجبار التحديث
                    }

                    // تحديث عداد الأرقام التسلسلية
                    var activeCount = _serialNumbers.Count(s => s.Status == "نشط");
                    var totalCount = _serialNumbers.Count;

                    if (SerialNumbersCountTextBlock != null)
                    {
                        SerialNumbersCountTextBlock.Text = $"إجمالي المكونات: {totalCount} | النشطة: {activeCount}";
                    }

                    System.Diagnostics.Debug.WriteLine($"تم تحديث الواجهة - إجمالي: {totalCount}, نشطة: {activeCount}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("معرف الجهاز غير صالح");
                    _serialNumbers = new List<DeviceSerialNumber>();
                    if (SerialNumbersDataGrid != null)
                    {
                        SerialNumbersDataGrid.ItemsSource = _serialNumbers;
                    }
                    if (SerialNumbersCountTextBlock != null)
                    {
                        SerialNumbersCountTextBlock.Text = "لا توجد أرقام تسلسلية";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                // تعيين قيم افتراضية في حالة الخطأ
                _serialNumbers = new List<DeviceSerialNumber>();
                if (SerialNumbersDataGrid != null)
                {
                    SerialNumbersDataGrid.ItemsSource = _serialNumbers;
                }
                if (SerialNumbersCountTextBlock != null)
                {
                    SerialNumbersCountTextBlock.Text = "خطأ في التحميل";
                }
            }
        }
        
        private void LoadDocumentsInfo()
        {
            // كتيب المستخدم
            if (!string.IsNullOrEmpty(_device.UserManualPath) && File.Exists(_device.UserManualPath))
            {
                UserManualTextBlock.Text = Path.GetFileName(_device.UserManualPath);
                UserManualTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenUserManualBtn.IsEnabled = true;
            }
            
            // كتيب الصيانة
            if (!string.IsNullOrEmpty(_device.MaintenanceManualPath) && File.Exists(_device.MaintenanceManualPath))
            {
                MaintenanceManualTextBlock.Text = Path.GetFileName(_device.MaintenanceManualPath);
                MaintenanceManualTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenMaintenanceManualBtn.IsEnabled = true;
            }
            
            // شهادة المنشأ
            if (!string.IsNullOrEmpty(_device.OriginCertificatePath) && File.Exists(_device.OriginCertificatePath))
            {
                OriginCertificateTextBlock.Text = Path.GetFileName(_device.OriginCertificatePath);
                OriginCertificateTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenOriginCertificateBtn.IsEnabled = true;
            }

            // أوراق شهادة الجودة
            if (!string.IsNullOrEmpty(_device.QualityCertificatePath) && File.Exists(_device.QualityCertificatePath))
            {
                QualityCertificateTextBlock.Text = Path.GetFileName(_device.QualityCertificatePath);
                QualityCertificateTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenQualityCertificateBtn.IsEnabled = true;
            }

            // المصادقات الرسمية
            if (!string.IsNullOrEmpty(_device.OfficialCertificationsPath) && File.Exists(_device.OfficialCertificationsPath))
            {
                OfficialCertificationsTextBlock.Text = Path.GetFileName(_device.OfficialCertificationsPath);
                OfficialCertificationsTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenOfficialCertificationsBtn.IsEnabled = true;
            }

            // كتيب المعلومات التقنية
            if (!string.IsNullOrEmpty(_device.TechnicalInfoBookletPath) && File.Exists(_device.TechnicalInfoBookletPath))
            {
                TechnicalInfoBookletTextBlock.Text = Path.GetFileName(_device.TechnicalInfoBookletPath);
                TechnicalInfoBookletTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenTechnicalInfoBookletBtn.IsEnabled = true;
            }

            // أوراق الاستيراد
            if (!string.IsNullOrEmpty(_device.ImportPapersPath) && File.Exists(_device.ImportPapersPath))
            {
                ImportPapersTextBlock.Text = Path.GetFileName(_device.ImportPapersPath);
                ImportPapersTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenImportPapersBtn.IsEnabled = true;
            }

            // أوراق العقود
            if (!string.IsNullOrEmpty(_device.ContractPapersPath) && File.Exists(_device.ContractPapersPath))
            {
                ContractPapersTextBlock.Text = Path.GetFileName(_device.ContractPapersPath);
                ContractPapersTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenContractPapersBtn.IsEnabled = true;
            }
        }

        private void LoadFilesInfo()
        {
            try
            {
                int filesCount = 0;

                // كتيب المستخدم
                if (!string.IsNullOrEmpty(_device.UserManualPath) && File.Exists(_device.UserManualPath))
                {
                    UserManualFileTextBlock.Text = Path.GetFileName(_device.UserManualPath);
                    UserManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenUserManualFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    UserManualFileTextBlock.Text = "لم يتم رفع ملف";
                    UserManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenUserManualFileBtn.IsEnabled = false;
                }

                // كتيب الصيانة
                if (!string.IsNullOrEmpty(_device.MaintenanceManualPath) && File.Exists(_device.MaintenanceManualPath))
                {
                    MaintenanceManualFileTextBlock.Text = Path.GetFileName(_device.MaintenanceManualPath);
                    MaintenanceManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenMaintenanceManualFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    MaintenanceManualFileTextBlock.Text = "لم يتم رفع ملف";
                    MaintenanceManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenMaintenanceManualFileBtn.IsEnabled = false;
                }

                // شهادة المنشأ
                if (!string.IsNullOrEmpty(_device.OriginCertificatePath) && File.Exists(_device.OriginCertificatePath))
                {
                    OriginCertificateFileTextBlock.Text = Path.GetFileName(_device.OriginCertificatePath);
                    OriginCertificateFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenOriginCertificateFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    OriginCertificateFileTextBlock.Text = "لم يتم رفع ملف";
                    OriginCertificateFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenOriginCertificateFileBtn.IsEnabled = false;
                }

                // أوراق شهادة الجودة
                if (!string.IsNullOrEmpty(_device.QualityCertificatePath) && File.Exists(_device.QualityCertificatePath))
                {
                    QualityCertificateFileTextBlock.Text = Path.GetFileName(_device.QualityCertificatePath);
                    QualityCertificateFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenQualityCertificateFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    QualityCertificateFileTextBlock.Text = "لم يتم رفع ملف";
                    QualityCertificateFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenQualityCertificateFileBtn.IsEnabled = false;
                }

                // المصادقات الرسمية
                if (!string.IsNullOrEmpty(_device.OfficialCertificationsPath) && File.Exists(_device.OfficialCertificationsPath))
                {
                    OfficialCertificationsFileTextBlock.Text = Path.GetFileName(_device.OfficialCertificationsPath);
                    OfficialCertificationsFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenOfficialCertificationsFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    OfficialCertificationsFileTextBlock.Text = "لم يتم رفع ملف";
                    OfficialCertificationsFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenOfficialCertificationsFileBtn.IsEnabled = false;
                }

                // كتيب المعلومات التقنية
                if (!string.IsNullOrEmpty(_device.TechnicalInfoBookletPath) && File.Exists(_device.TechnicalInfoBookletPath))
                {
                    TechnicalInfoBookletFileTextBlock.Text = Path.GetFileName(_device.TechnicalInfoBookletPath);
                    TechnicalInfoBookletFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenTechnicalInfoBookletFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    TechnicalInfoBookletFileTextBlock.Text = "لم يتم رفع ملف";
                    TechnicalInfoBookletFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenTechnicalInfoBookletFileBtn.IsEnabled = false;
                }

                // أوراق الاستيراد
                if (!string.IsNullOrEmpty(_device.ImportPapersPath) && File.Exists(_device.ImportPapersPath))
                {
                    ImportPapersFileTextBlock.Text = Path.GetFileName(_device.ImportPapersPath);
                    ImportPapersFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenImportPapersFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    ImportPapersFileTextBlock.Text = "لم يتم رفع ملف";
                    ImportPapersFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenImportPapersFileBtn.IsEnabled = false;
                }

                // أوراق العقود
                if (!string.IsNullOrEmpty(_device.ContractPapersPath) && File.Exists(_device.ContractPapersPath))
                {
                    ContractPapersFileTextBlock.Text = Path.GetFileName(_device.ContractPapersPath);
                    ContractPapersFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenContractPapersFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    ContractPapersFileTextBlock.Text = "لم يتم رفع ملف";
                    ContractPapersFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenContractPapersFileBtn.IsEnabled = false;
                }

                // تحديث عداد الملفات
                FilesCountTextBlock.Text = $"إجمالي الملفات المرفقة: {filesCount} من 7";

                // تحديث الملخص السريع
                FilesCountSummary.Text = $"{filesCount} ملفات";

                // تحميل كتيبات التقارير
                LoadReportBooklets();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الملفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadReportBooklets()
        {
            try
            {
                ReportBookletsPanel.Children.Clear();

                // تحميل كتيبات تقارير الصيانة
                var maintenanceReports = await App.DatabaseContext.MaintenanceRecords
                    .Where(m => (m.DeviceId == _device.Id || m.MedicalDeviceId == _device.Id) &&
                               !string.IsNullOrEmpty(m.ReportBookletPath))
                    .ToListAsync();

                foreach (var report in maintenanceReports)
                {
                    if (File.Exists(report.ReportBookletPath))
                    {
                        var reportPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

                        var reportIcon = new TextBlock { Text = "📋", FontSize = 16, Margin = new Thickness(0, 0, 10, 0) };
                        var reportText = new TextBlock
                        {
                            Text = $"تقرير صيانة - {report.MaintenanceDate:dd/MM/yyyy} - {Path.GetFileName(report.ReportBookletPath)}",
                            VerticalAlignment = VerticalAlignment.Center,
                            Margin = new Thickness(0, 0, 10, 0)
                        };
                        var openButton = new Button
                        {
                            Content = "فتح",
                            Width = 60,
                            Height = 25,
                            Background = System.Windows.Media.Brushes.LightBlue,
                            Tag = report.ReportBookletPath
                        };
                        openButton.Click += (s, e) => OpenDocument(openButton.Tag.ToString());

                        reportPanel.Children.Add(reportIcon);
                        reportPanel.Children.Add(reportText);
                        reportPanel.Children.Add(openButton);

                        ReportBookletsPanel.Children.Add(reportPanel);
                    }
                }

                // تحميل كتيبات تقارير التنصيب
                var installationReports = await App.DatabaseContext.DeviceInstallations
                    .Where(i => i.MedicalDeviceId == _device.Id &&
                               !string.IsNullOrEmpty(i.InstallationReportBookletPath))
                    .ToListAsync();

                foreach (var report in installationReports)
                {
                    if (File.Exists(report.InstallationReportBookletPath))
                    {
                        var reportPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

                        var reportIcon = new TextBlock { Text = "⚙️", FontSize = 16, Margin = new Thickness(0, 0, 10, 0) };
                        var reportText = new TextBlock
                        {
                            Text = $"تقرير تنصيب - {report.InstallationDate:dd/MM/yyyy} - {Path.GetFileName(report.InstallationReportBookletPath)}",
                            VerticalAlignment = VerticalAlignment.Center,
                            Margin = new Thickness(0, 0, 10, 0)
                        };
                        var openButton = new Button
                        {
                            Content = "فتح",
                            Width = 60,
                            Height = 25,
                            Background = System.Windows.Media.Brushes.LightGreen,
                            Tag = report.InstallationReportBookletPath
                        };
                        openButton.Click += (s, e) => OpenDocument(openButton.Tag.ToString());

                        reportPanel.Children.Add(reportIcon);
                        reportPanel.Children.Add(reportText);
                        reportPanel.Children.Add(openButton);

                        ReportBookletsPanel.Children.Add(reportPanel);
                    }
                }

                if (ReportBookletsPanel.Children.Count == 0)
                {
                    ReportBookletsPanel.Children.Add(new TextBlock
                    {
                        Text = "لا توجد كتيبات تقارير مرفقة حالياً",
                        Foreground = System.Windows.Media.Brushes.Gray,
                        FontStyle = FontStyles.Italic
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل كتيبات التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadRelatedDataAsync()
        {
            try
            {
                // تحميل الأرقام التسلسلية
                await LoadSerialNumbersAsync();

                // تحميل المبيعات المرتبطة بهذا الجهاز
                await LoadDeviceSalesAsync();

                // تحميل سجلات الصيانة
                await LoadDeviceMaintenanceAsync();

                // تحميل سجلات التنصيبات
                await LoadDeviceInstallationsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات المرتبطة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceSalesAsync()
        {
            try
            {
                if (_showAllData)
                {
                    // تأكد من تحميل الأرقام التسلسلية أولاً
                    if (_serialNumbers == null)
                    {
                        await LoadSerialNumbersAsync();
                    }

                    // عرض جميع المبيعات للجهاز
                    var serialNumbersList = _serialNumbers?.Select(sn => sn.SerialNumber).ToList() ?? new List<string>();

                    // محاولة البحث بالرقم التسلسلي مع التعامل مع عدم وجود العمود
                    try
                    {
                        _deviceSales = await App.DatabaseContext.Sales
                            .Where(s => s.DeviceName.Contains(_device.Name) ||
                                       (!string.IsNullOrEmpty(_device.SerialNumber) && s.DeviceName.Contains(_device.SerialNumber)) ||
                                       (serialNumbersList.Count > 0 && serialNumbersList.Contains(s.SerialNumber)))
                            .OrderByDescending(s => s.SaleDate)
                            .ToListAsync();
                    }
                    catch
                    {
                        // في حالة عدم وجود عمود SerialNumber، ابحث بالاسم فقط
                        _deviceSales = await App.DatabaseContext.Sales
                            .Where(s => s.DeviceName.Contains(_device.Name) ||
                                       (!string.IsNullOrEmpty(_device.SerialNumber) && s.DeviceName.Contains(_device.SerialNumber)))
                            .OrderByDescending(s => s.SaleDate)
                            .ToListAsync();
                    }
                }
                else if (_selectedSerialNumber != null)
                {
                    // عرض المبيعات للرقم التسلسلي المحدد فقط
                    try
                    {
                        _deviceSales = await App.DatabaseContext.Sales
                            .Where(s => s.SerialNumber == _selectedSerialNumber.SerialNumber)
                            .OrderByDescending(s => s.SaleDate)
                            .ToListAsync();
                    }
                    catch
                    {
                        // في حالة عدم وجود عمود SerialNumber، لا توجد مبيعات محددة
                        _deviceSales = new List<Sale>();
                    }
                }
                else
                {
                    _deviceSales = new List<Sale>();
                }

                SalesDataGrid.ItemsSource = _deviceSales;

                // تحديث الإحصائيات
                var totalSales = _deviceSales.Count;
                var totalRevenue = _deviceSales.Sum(s => s.TotalAmount);
                SalesCountTextBlock.Text = $"إجمالي المبيعات: {totalSales} | إجمالي الإيرادات: {totalRevenue:N0} د.ع";

                // تحديث الملخص السريع
                SalesCountSummary.Text = $"{totalSales} مبيعات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceMaintenanceAsync()
        {
            try
            {
                if (_showAllData)
                {
                    // تأكد من تحميل الأرقام التسلسلية أولاً
                    if (_serialNumbers == null)
                    {
                        await LoadSerialNumbersAsync();
                    }

                    // عرض جميع سجلات الصيانة للجهاز
                    var serialNumbersList = _serialNumbers?.Select(sn => sn.SerialNumber).ToList() ?? new List<string>();

                    // إضافة الرقم التسلسلي الرئيسي للجهاز
                    if (!string.IsNullOrEmpty(_device.SerialNumber))
                    {
                        serialNumbersList.Add(_device.SerialNumber);
                    }

                    _deviceMaintenance = await App.DatabaseContext.MaintenanceRecords
                        .Where(m => (m.MedicalDeviceId == _device.Id) ||
                                   (serialNumbersList.Count > 0 && serialNumbersList.Contains(m.SerialNumber)))
                        .OrderByDescending(m => m.MaintenanceDate)
                        .ToListAsync();
                }
                else if (_selectedSerialNumber != null)
                {
                    // عرض سجلات الصيانة للرقم التسلسلي المحدد فقط
                    _deviceMaintenance = await App.DatabaseContext.MaintenanceRecords
                        .Where(m => m.SerialNumber == _selectedSerialNumber.SerialNumber)
                        .OrderByDescending(m => m.MaintenanceDate)
                        .ToListAsync();
                }
                else
                {
                    _deviceMaintenance = new List<MaintenanceRecord>();
                }

                MaintenanceDataGrid.ItemsSource = _deviceMaintenance;

                // تحديث الإحصائيات
                var totalMaintenance = _deviceMaintenance.Count;
                var totalCost = _deviceMaintenance.Sum(m => m.Cost);
                var lastMaintenance = _deviceMaintenance.FirstOrDefault()?.MaintenanceDate.ToString("dd/MM/yyyy") ?? "لا يوجد";
                MaintenanceCountTextBlock.Text = $"إجمالي الصيانات: {totalMaintenance} | إجمالي التكلفة: {totalCost:N0} د.ع | آخر صيانة: {lastMaintenance}";

                // تحديث الملخص السريع
                MaintenanceCountSummary.Text = $"{totalMaintenance} صيانات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل سجلات الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceInstallationsAsync()
        {
            try
            {
                if (_showAllData)
                {
                    // تأكد من تحميل الأرقام التسلسلية أولاً
                    if (_serialNumbers == null)
                    {
                        await LoadSerialNumbersAsync();
                    }

                    // عرض جميع التنصيبات للجهاز
                    var serialNumbersList = _serialNumbers?.Select(sn => sn.SerialNumber).ToList() ?? new List<string>();

                    _deviceInstallations = await App.DatabaseContext.DeviceInstallations
                        .Where(i => i.MedicalDeviceId == _device.Id ||
                                   (serialNumbersList.Count > 0 && serialNumbersList.Contains(i.SerialNumber)))
                        .OrderByDescending(i => i.InstallationDate)
                        .ToListAsync();
                }
                else if (_selectedSerialNumber != null)
                {
                    // عرض التنصيبات للرقم التسلسلي المحدد فقط
                    _deviceInstallations = await App.DatabaseContext.DeviceInstallations
                        .Where(i => i.SerialNumber == _selectedSerialNumber.SerialNumber)
                        .OrderByDescending(i => i.InstallationDate)
                        .ToListAsync();
                }
                else
                {
                    _deviceInstallations = new List<DeviceInstallation>();
                }

                InstallationsDataGrid.ItemsSource = _deviceInstallations;

                // تحديث الإحصائيات
                var totalInstallations = _deviceInstallations.Count;
                var totalCost = _deviceInstallations.Sum(i => i.InstallationCost);
                var activeInstallations = _deviceInstallations.Count(i => i.InstallationStatus == "مكتمل");
                InstallationsCountTextBlock.Text = $"إجمالي التنصيبات: {totalInstallations} | النشطة: {activeInstallations} | إجمالي التكلفة: {totalCost:N0} د.ع";

                // تحديث الملخص السريع
                InstallationsCountSummary.Text = $"{totalInstallations} تنصيبات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل سجلات التنصيبات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageSerialNumbersBtn_Click(object sender, RoutedEventArgs e)
        {
            var serialNumbersWindow = new DeviceSerialNumbersWindow(_device.Id, _device.Name);
            serialNumbersWindow.ShowDialog();

            // تحديث الأرقام التسلسلية بعد الإغلاق
            _ = LoadSerialNumbersAsync();
        }
        
        private void EditDeviceBtn_Click(object sender, RoutedEventArgs e)
        {
            var editWindow = new AddEditDeviceWindow(_device);
            if (editWindow.ShowDialog() == true)
            {
                // إعادة تحميل البيانات بعد التعديل
                LoadDeviceDetailsAsync();
            }
        }

        private async void RefreshSerialNumbersBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadSerialNumbersAsync();
                MessageBox.Show("تم تحديث قائمة الأرقام التسلسلية بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الأرقام التسلسلية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintDetailsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreatePrintDocument();
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, $"تفاصيل الجهاز - {_device.Name}");
                    MessageBox.Show("تم طباعة التفاصيل بنجاح", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FlowDocument CreatePrintDocument()
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);
            document.FontFamily = new FontFamily("Arial");
            document.FontSize = 12;

            // العنوان الرئيسي
            var title = new Paragraph(new Run($"تقرير تفاصيل الجهاز الطبي - {_device.Name}"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            // تاريخ التقرير
            var dateInfo = new Paragraph(new Run($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}"))
            {
                FontSize = 10,
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(dateInfo);

            // المعلومات الأساسية
            AddSectionToDocument(document, "المعلومات الأساسية", new[]
            {
                $"اسم الجهاز: {_device.Name}",
                $"الماركة: {_device.Brand}",
                $"الموديل: {_device.Model}",
                $"الرقم التسلسلي: {_device.SerialNumber}",
                $"الفئة: {_device.Category}",
                $"الحالة: {_device.Status}",
                $"الوصف: {_device.Description}"
            });

            // المعلومات المالية
            AddSectionToDocument(document, "المعلومات المالية", new[]
            {
                $"سعر الشراء: {_device.PurchasePrice:N0} د.ع",
                $"سعر البيع: {_device.SellingPrice:N0} د.ع",
                $"المورد: {_device.Supplier}",
                $"الموقع: {_device.Location}"
            });

            // التواريخ المهمة
            AddSectionToDocument(document, "التواريخ المهمة", new[]
            {
                $"تاريخ الشراء: {_device.PurchaseDate:dd/MM/yyyy}",
                $"تاريخ الإنشاء: {_device.CreatedDate:dd/MM/yyyy}",
                $"بداية الضمان: {_device.WarrantyStartDate:dd/MM/yyyy}",
                $"نهاية الضمان: {_device.WarrantyEndDate:dd/MM/yyyy}"
            });

            // إحصائيات الأرقام التسلسلية
            if (_serialNumbers?.Any() == true)
            {
                var activeCount = _serialNumbers.Count(s => s.Status == "نشط");
                AddSectionToDocument(document, "الأرقام التسلسلية", new[]
                {
                    $"إجمالي المكونات: {_serialNumbers.Count}",
                    $"المكونات النشطة: {activeCount}",
                    $"المكونات المعطلة: {_serialNumbers.Count - activeCount}"
                });
            }

            // إحصائيات المبيعات
            if (_deviceSales?.Any() == true)
            {
                var totalRevenue = _deviceSales.Sum(s => s.TotalAmount);
                AddSectionToDocument(document, "إحصائيات المبيعات", new[]
                {
                    $"إجمالي المبيعات: {_deviceSales.Count}",
                    $"إجمالي الإيرادات: {totalRevenue:N0} د.ع",
                    $"متوسط سعر البيع: {(totalRevenue / _deviceSales.Count):N0} د.ع"
                });
            }

            // إحصائيات الصيانة
            if (_deviceMaintenance?.Any() == true)
            {
                var totalCost = _deviceMaintenance.Sum(m => m.Cost);
                var lastMaintenance = _deviceMaintenance.FirstOrDefault()?.MaintenanceDate.ToString("dd/MM/yyyy") ?? "لا يوجد";
                AddSectionToDocument(document, "إحصائيات الصيانة", new[]
                {
                    $"إجمالي الصيانات: {_deviceMaintenance.Count}",
                    $"إجمالي تكلفة الصيانة: {totalCost:N0} د.ع",
                    $"آخر صيانة: {lastMaintenance}"
                });
            }

            // إحصائيات التنصيبات
            if (_deviceInstallations?.Any() == true)
            {
                var totalCost = _deviceInstallations.Sum(i => i.InstallationCost);
                var activeInstallations = _deviceInstallations.Count(i => i.InstallationStatus == "مكتمل");
                AddSectionToDocument(document, "إحصائيات التنصيبات", new[]
                {
                    $"إجمالي التنصيبات: {_deviceInstallations.Count}",
                    $"التنصيبات النشطة: {activeInstallations}",
                    $"إجمالي تكلفة التنصيب: {totalCost:N0} د.ع"
                });
            }

            return document;
        }

        private void AddSectionToDocument(FlowDocument document, string sectionTitle, string[] items)
        {
            // عنوان القسم
            var sectionHeader = new Paragraph(new Run(sectionTitle))
            {
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 15, 0, 5),
                Foreground = System.Windows.Media.Brushes.DarkBlue
            };
            document.Blocks.Add(sectionHeader);

            // عناصر القسم
            foreach (var item in items)
            {
                if (!string.IsNullOrEmpty(item))
                {
                    var paragraph = new Paragraph(new Run($"• {item}"))
                    {
                        Margin = new Thickness(20, 2, 0, 2)
                    };
                    document.Blocks.Add(paragraph);
                }
            }
        }
        
        private void OpenUserManualBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.UserManualPath);
        }
        
        private void OpenMaintenanceManualBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.MaintenanceManualPath);
        }
        
        private void OpenOriginCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.OriginCertificatePath);
        }

        private void OpenQualityCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.QualityCertificatePath);
        }

        private void OpenOfficialCertificationsBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.OfficialCertificationsPath);
        }

        private void OpenTechnicalInfoBookletBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.TechnicalInfoBookletPath);
        }

        private void OpenImportPapersBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.ImportPapersPath);
        }

        private void OpenContractPapersBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.ContractPapersPath);
        }
        
        private void OpenDocument(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه", "ملف غير موجود", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        // وظائف اختيار الرقم التسلسلي
        private async void SelectSerialNumber_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectWindow = new SelectSerialNumberWindow(_device);
                if (selectWindow.ShowDialog() == true)
                {
                    if (selectWindow.ShowAllData)
                    {
                        _showAllData = true;
                        _selectedSerialNumber = null;
                    }
                    else
                    {
                        _showAllData = false;
                        _selectedSerialNumber = selectWindow.SelectedSerialNumber;
                    }

                    UpdateFilterDisplay();
                    await LoadRelatedDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الرقم التسلسلي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ShowAllData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _showAllData = true;
                _selectedSerialNumber = null;
                UpdateFilterDisplay();
                await LoadRelatedDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض جميع البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateFilterDisplay()
        {
            try
            {
                if (_showAllData)
                {
                    CurrentFilterText.Text = "عرض: جميع البيانات (جميع الأرقام التسلسلية)";
                    FilterDescriptionText.Text = "يتم عرض جميع سجلات الصيانة والمبيعات والتنصيبات لجميع الأرقام التسلسلية";
                }
                else if (_selectedSerialNumber != null)
                {
                    CurrentFilterText.Text = $"عرض: الرقم التسلسلي {_selectedSerialNumber.SerialNumber}";
                    FilterDescriptionText.Text = $"يتم عرض البيانات الخاصة بالرقم التسلسلي {_selectedSerialNumber.SerialNumber} - {_selectedSerialNumber.ComponentName} فقط";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث واجهة الفلترة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
