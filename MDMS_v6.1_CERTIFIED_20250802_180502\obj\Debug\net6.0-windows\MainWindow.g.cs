﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "87928B47FD2901804059714589AB81933A677BCA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 113 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DateTimeBlock;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardBtn;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DevicesBtn;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryBtn;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalesBtn;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuotationsBtn;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomersBtn;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SuppliersBtn;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShipmentsBtn;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaintenanceBtn;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InstallationsBtn;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SparePartsBtn;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsBtn;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ContentArea;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DateTimeBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DashboardBtn = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\MainWindow.xaml"
            this.DashboardBtn.Click += new System.Windows.RoutedEventHandler(this.DashboardBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DevicesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\MainWindow.xaml"
            this.DevicesBtn.Click += new System.Windows.RoutedEventHandler(this.DevicesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.InventoryBtn = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\MainWindow.xaml"
            this.InventoryBtn.Click += new System.Windows.RoutedEventHandler(this.InventoryBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SalesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 154 "..\..\..\MainWindow.xaml"
            this.SalesBtn.Click += new System.Windows.RoutedEventHandler(this.SalesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.QuotationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\MainWindow.xaml"
            this.QuotationsBtn.Click += new System.Windows.RoutedEventHandler(this.QuotationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CustomersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\MainWindow.xaml"
            this.CustomersBtn.Click += new System.Windows.RoutedEventHandler(this.CustomersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SuppliersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\MainWindow.xaml"
            this.SuppliersBtn.Click += new System.Windows.RoutedEventHandler(this.SuppliersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ShipmentsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\MainWindow.xaml"
            this.ShipmentsBtn.Click += new System.Windows.RoutedEventHandler(this.ShipmentsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.MaintenanceBtn = ((System.Windows.Controls.Button)(target));
            
            #line 183 "..\..\..\MainWindow.xaml"
            this.MaintenanceBtn.Click += new System.Windows.RoutedEventHandler(this.MaintenanceBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.InstallationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\MainWindow.xaml"
            this.InstallationsBtn.Click += new System.Windows.RoutedEventHandler(this.InstallationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SparePartsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\MainWindow.xaml"
            this.SparePartsBtn.Click += new System.Windows.RoutedEventHandler(this.SparePartsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ReportsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 204 "..\..\..\MainWindow.xaml"
            this.ReportsBtn.Click += new System.Windows.RoutedEventHandler(this.ReportsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.SettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\MainWindow.xaml"
            this.SettingsBtn.Click += new System.Windows.RoutedEventHandler(this.SettingsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ContentArea = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

