using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageTechniciansWindow : Window
    {
        private TechnicianName _editingTechnician = null;
        
        public ManageTechniciansWindow()
        {
            InitializeComponent();
            LoadTechniciansAsync();
        }
        
        private async void LoadTechniciansAsync()
        {
            try
            {
                var technicians = await App.DatabaseContext.TechnicianNames
                    .OrderBy(t => t.Name)
                    .ToListAsync();
                    
                TechniciansDataGrid.ItemsSource = technicians;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أسماء الفنيين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddTechnicianBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _editingTechnician = null;
            TechnicianNameTextBox.Focus();
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadTechniciansAsync();
            ClearForm();
        }
        
        private void EditTechnicianBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var technician = button?.DataContext as TechnicianName;
            
            if (technician != null)
            {
                _editingTechnician = technician;
                LoadTechnicianToForm(technician);
            }
        }
        
        private async void DeleteTechnicianBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var technician = button?.DataContext as TechnicianName;
            
            if (technician != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفني '{technician.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.TechnicianNames.Remove(technician);
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف الفني بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadTechniciansAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفني: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private async void SaveTechnicianBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
                
            try
            {
                if (_editingTechnician == null)
                {
                    // إضافة فني جديد
                    var newTechnician = new TechnicianName
                    {
                        Name = TechnicianNameTextBox.Text.Trim(),
                        Specialization = SpecializationComboBox.Text.Trim(),
                        Phone = PhoneTextBox.Text.Trim(),
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.TechnicianNames.Add(newTechnician);
                }
                else
                {
                    // تعديل فني موجود
                    _editingTechnician.Name = TechnicianNameTextBox.Text.Trim();
                    _editingTechnician.Specialization = SpecializationComboBox.Text.Trim();
                    _editingTechnician.Phone = PhoneTextBox.Text.Trim();
                    _editingTechnician.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    _editingTechnician.LastUpdated = DateTime.Now;
                    
                    App.DatabaseContext.TechnicianNames.Update(_editingTechnician);
                }
                
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _editingTechnician == null ? "تم إضافة الفني بنجاح!" : "تم تحديث الفني بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                LoadTechniciansAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفني: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void LoadTechnicianToForm(TechnicianName technician)
        {
            TechnicianNameTextBox.Text = technician.Name;
            SpecializationComboBox.Text = technician.Specialization;
            PhoneTextBox.Text = technician.Phone;
            IsActiveCheckBox.IsChecked = technician.IsActive;
        }
        
        private void ClearForm()
        {
            _editingTechnician = null;
            TechnicianNameTextBox.Text = "";
            SpecializationComboBox.SelectedIndex = 0;
            PhoneTextBox.Text = "";
            IsActiveCheckBox.IsChecked = true;
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(TechnicianNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الفني", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TechnicianNameTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(SpecializationComboBox.Text))
            {
                MessageBox.Show("يرجى إدخال التخصص", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SpecializationComboBox.Focus();
                return false;
            }
            
            return true;
        }
    }
}
