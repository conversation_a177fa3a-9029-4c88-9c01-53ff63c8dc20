<Window x:Class="MedicalDevicesManager.Windows.InventorySelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار عنصر من المخزون" Height="600" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="📦 اختيار عنصر من المخزون" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- البحث والفلترة -->
        <Border Grid.Row="1" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="15" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="البحث:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox Grid.Column="1" x:Name="SearchTextBox" Height="30" Padding="8" 
                         TextChanged="SearchTextBox_TextChanged" Margin="0,0,20,0"/>
                
                <TextBlock Grid.Column="2" Text="الفئة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="3" x:Name="CategoryFilterComboBox" Height="30" 
                          SelectionChanged="CategoryFilterComboBox_SelectionChanged" Margin="0,0,20,0"/>
                
                <Button Grid.Column="4" x:Name="ClearFiltersBtn" Content="🔄 مسح الفلاتر" Width="100" Height="30" 
                        Background="#6C757D" Foreground="White" BorderThickness="0" 
                        Click="ClearFiltersBtn_Click"/>
            </Grid>
        </Border>
        
        <!-- جدول المخزون -->
        <DataGrid Grid.Row="2" x:Name="InventoryDataGrid"
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14"
                  SelectionMode="Single" MouseDoubleClick="InventoryDataGrid_MouseDoubleClick"
                  SelectionChanged="InventoryDataGrid_SelectionChanged">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="150"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="250"/>
                <DataGridTextColumn Header="المخزون الحالي" Binding="{Binding CurrentStock}" Width="100"/>
                <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, StringFormat=C}" Width="100"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                
                <!-- عمود الاختيار -->
                <DataGridTemplateColumn Header="اختيار" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="✅" Width="30" Height="25" 
                                    Background="#28A745" Foreground="White" BorderThickness="0" 
                                    Cursor="Hand" ToolTip="اختيار هذا العنصر"
                                    Click="SelectItemBtn_Click"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SelectBtn" Content="✅ اختيار العنصر المحدد" Width="180" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" 
                    Click="SelectBtn_Click" IsEnabled="False"/>
            
            <Button x:Name="CancelBtn" Content="❌ إلغاء" Width="100" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelBtn_Click"/>
        </StackPanel>
        
        <!-- معلومات العنصر المحدد -->
        <Border Grid.Row="3" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                CornerRadius="5" Padding="15" Margin="0,60,0,0" x:Name="SelectedItemPanel" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="📋 معلومات العنصر المحدد" FontWeight="Bold" Margin="0,0,0,10" Foreground="#1976D2"/>
                <TextBlock x:Name="SelectedItemInfoTextBlock" Text="" FontSize="12" TextWrapping="Wrap"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
