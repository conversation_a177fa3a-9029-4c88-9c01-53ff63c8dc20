<Window x:Class="MedicalDevicesManager.Windows.ManageRecipientsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة أسماء المستلمين" Height="600" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="📦 إدارة أسماء المستلمين" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button x:Name="AddRecipientBtn" Content="➕ إضافة مستلم جديد" Width="150" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="AddRecipientBtn_Click"/>
            
            <Button x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="35" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="RefreshBtn_Click"/>
        </StackPanel>
        
        <!-- جدول المستلمين -->
        <DataGrid Grid.Row="2" x:Name="RecipientsDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="اسم المستلم" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="250"/>
                <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="80"/>
                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="✏️" Width="30" Height="25" 
                                        Background="#FFC107" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="تعديل"
                                        Click="EditRecipientBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteRecipientBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- نموذج إضافة/تعديل المستلم -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0">
            
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="اسم المستلم:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1" x:Name="RecipientNameTextBox" Height="30" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="2" Text="رقم الهاتف:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="3" x:Name="PhoneTextBox" Height="30" Margin="0,0,20,0"/>
                    
                    <StackPanel Grid.Column="4" Orientation="Horizontal">
                        <Button x:Name="SaveRecipientBtn" Content="💾 حفظ" Width="80" Height="30" 
                                Background="#28A745" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="SaveRecipientBtn_Click"/>
                        <Button x:Name="CancelBtn" Content="❌ إلغاء" Width="80" Height="30" 
                                Background="#DC3545" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Click="CancelBtn_Click"/>
                    </StackPanel>
                </Grid>
                
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="العنوان:" VerticalAlignment="Top" Margin="0,5,10,0"/>
                    <TextBox Grid.Column="1" x:Name="AddressTextBox" Height="60" Margin="0,0,20,0" 
                             TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                    
                    <CheckBox Grid.Column="2" x:Name="IsActiveCheckBox" Content="نشط" 
                              VerticalAlignment="Center" IsChecked="True"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</Window>
