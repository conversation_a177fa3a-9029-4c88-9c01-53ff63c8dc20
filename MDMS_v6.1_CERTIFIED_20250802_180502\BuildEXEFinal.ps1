# Build Medical Devices Management System EXE

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Building Medical Devices System EXE" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    # Get current directory
    $currentDir = (Get-Location).Path
    $outputDir = Join-Path $currentDir "Release"
    $publishDir = Join-Path $outputDir "Publish"
    
    Write-Host "Directories:" -ForegroundColor Cyan
    Write-Host "  Source: $currentDir" -ForegroundColor White
    Write-Host "  Output: $outputDir" -ForegroundColor White
    Write-Host "  Publish: $publishDir" -ForegroundColor White
    Write-Host ""
    
    # Clean previous builds
    Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
    if (Test-Path $outputDir) {
        Remove-Item $outputDir -Recurse -Force
        Write-Host "  Cleaned output directory" -ForegroundColor Green
    }
    
    # Create output directory
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    New-Item -ItemType Directory -Path $publishDir -Force | Out-Null
    Write-Host "  Created output directories" -ForegroundColor Green
    Write-Host ""
    
    # Restore packages
    Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
    $restoreResult = & dotnet restore --verbosity quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Packages restored successfully" -ForegroundColor Green
    } else {
        throw "Package restore failed"
    }
    Write-Host ""
    
    # Build Release version
    Write-Host "Building Release version..." -ForegroundColor Yellow
    $buildResult = & dotnet build --configuration Release --verbosity quiet --no-restore
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Build completed successfully" -ForegroundColor Green
    } else {
        throw "Build failed"
    }
    Write-Host ""
    
    # Publish self-contained executable
    Write-Host "Publishing self-contained executable..." -ForegroundColor Yellow
    Write-Host "  Target: Windows x64" -ForegroundColor Gray
    Write-Host "  Mode: Self-contained" -ForegroundColor Gray
    Write-Host "  Single file: Yes" -ForegroundColor Gray
    Write-Host ""
    
    $publishArgs = @(
        "publish"
        "--configuration", "Release"
        "--runtime", "win-x64"
        "--self-contained", "true"
        "--output", $publishDir
        "/p:PublishSingleFile=true"
        "/p:PublishTrimmed=true"
        "/p:IncludeNativeLibrariesForSelfExtract=true"
        "--verbosity", "minimal"
    )
    
    $publishResult = & dotnet @publishArgs
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Publish completed successfully" -ForegroundColor Green
    } else {
        throw "Publish failed"
    }
    Write-Host ""
    
    # Find the executable
    $exeFile = Get-ChildItem -Path $publishDir -Filter "*.exe" | Select-Object -First 1
    if ($exeFile) {
        $exePath = $exeFile.FullName
        $exeSize = [math]::Round($exeFile.Length / 1MB, 2)
        
        Write-Host "Executable created successfully!" -ForegroundColor Green
        Write-Host "  File: $($exeFile.Name)" -ForegroundColor White
        Write-Host "  Size: $exeSize MB" -ForegroundColor White
        Write-Host "  Path: $exePath" -ForegroundColor White
        Write-Host ""
        
        # Copy database and resources
        Write-Host "Copying additional files..." -ForegroundColor Yellow
        
        # Copy database if exists
        $dbFiles = @("MedicalDevices.db", "MedicalDevicesIntegrated.db")
        foreach ($dbFile in $dbFiles) {
            if (Test-Path $dbFile) {
                Copy-Item $dbFile $publishDir -Force
                Write-Host "  Copied $dbFile" -ForegroundColor Green
            }
        }
        
        # Copy resources if exists
        if (Test-Path "Resources") {
            Copy-Item "Resources" $publishDir -Recurse -Force
            Write-Host "  Copied Resources folder" -ForegroundColor Green
        }
        
        # Create README file
        $buildDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $readmeContent = "Medical Devices Management System`r`n"
        $readmeContent += "Version: 6.1 Certified`r`n"
        $readmeContent += "Build Date: $buildDate`r`n"
        $readmeContent += "Target: Windows x64`r`n"
        $readmeContent += "Type: Self-contained executable`r`n`r`n"
        $readmeContent += "How to Run:`r`n"
        $readmeContent += "1. Double-click on MedicalDevicesManager.exe`r`n"
        $readmeContent += "2. The system will start automatically`r`n"
        $readmeContent += "3. No additional software installation required`r`n`r`n"
        $readmeContent += "System Requirements:`r`n"
        $readmeContent += "- Windows 10 or later (x64)`r`n"
        $readmeContent += "- Minimum 4GB RAM`r`n"
        $readmeContent += "- 500MB free disk space`r`n`r`n"
        $readmeContent += "Features:`r`n"
        $readmeContent += "- Complete Arabic interface with RTL support`r`n"
        $readmeContent += "- Medical devices management`r`n"
        $readmeContent += "- Sales and invoicing system`r`n"
        $readmeContent += "- Inventory and parts management`r`n"
        $readmeContent += "- Maintenance and installation tracking`r`n"
        $readmeContent += "- Automatic backup system`r`n"
        $readmeContent += "- Comprehensive reports and statistics`r`n`r`n"
        $readmeContent += "Enjoy using the Medical Devices Management System!"
        
        $readmeContent | Out-File -FilePath (Join-Path $publishDir "README.txt") -Encoding UTF8
        Write-Host "  Created README.txt" -ForegroundColor Green
        Write-Host ""
        
        # Create distribution package
        Write-Host "Creating distribution package..." -ForegroundColor Yellow
        $packageName = "MedicalDevicesManager_v6.1_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        $packageDir = Join-Path $outputDir $packageName
        
        # Create package directory
        New-Item -ItemType Directory -Path $packageDir -Force | Out-Null
        
        # Copy all files to package
        Copy-Item "$publishDir\*" $packageDir -Recurse -Force
        
        # Create ZIP package
        $zipPath = "$packageDir.zip"
        Compress-Archive -Path $packageDir -DestinationPath $zipPath -Force
        
        $zipSize = [math]::Round((Get-Item $zipPath).Length / 1MB, 2)
        
        Write-Host "  Distribution package created" -ForegroundColor Green
        Write-Host "  Package: $packageName" -ForegroundColor White
        Write-Host "  ZIP file: $zipPath" -ForegroundColor White
        Write-Host "  ZIP size: $zipSize MB" -ForegroundColor White
        Write-Host ""
        
        # Test executable
        Write-Host "Testing executable..." -ForegroundColor Yellow
        if (Test-Path $exePath) {
            Write-Host "  Executable file exists" -ForegroundColor Green
            Write-Host "  Ready for distribution" -ForegroundColor Green
        } else {
            Write-Host "  Executable file not found" -ForegroundColor Red
        }
        Write-Host ""
        
        # Summary
        Write-Host "Build Summary:" -ForegroundColor Cyan
        Write-Host "  Executable: $($exeFile.Name) ($exeSize MB)" -ForegroundColor White
        Write-Host "  Package: $packageName.zip ($zipSize MB)" -ForegroundColor White
        Write-Host "  Location: $outputDir" -ForegroundColor White
        Write-Host "  Status: Ready for distribution" -ForegroundColor Green
        Write-Host ""
        
        Write-Host "Distribution Options:" -ForegroundColor Magenta
        Write-Host "  1. Share the ZIP file for complete package" -ForegroundColor White
        Write-Host "  2. Share just the EXE file for simple distribution" -ForegroundColor White
        Write-Host "  3. Copy the package folder to target computers" -ForegroundColor White
        Write-Host ""
        
    } else {
        throw "Executable file not found after publish"
    }
    
} catch {
    Write-Host "Build failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "  Ensure .NET 6.0 SDK is installed" -ForegroundColor White
    Write-Host "  Check for build errors in the output" -ForegroundColor White
    Write-Host "  Verify all project files are present" -ForegroundColor White
    Write-Host "  Try running 'dotnet clean' first" -ForegroundColor White
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "EXE Build Process Completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
