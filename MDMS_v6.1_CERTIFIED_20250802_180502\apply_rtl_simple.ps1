# Script to apply FlowDirection="RightToLeft" to all XAML windows

Write-Host "Starting Arabic improvements on windows..." -ForegroundColor Green

# List of windows that need improvement
$windowFiles = @(
    "Windows\AddEditInventoryWindow.xaml",
    "Windows\AddEditMaintenanceWindow.xaml", 
    "Windows\AddEditShipmentWindow.xaml",
    "Windows\AddEditSparePartWindow.xaml",
    "Windows\AddEditSupplierWindow.xaml",
    "Windows\AddEditInstallationWindow.xaml",
    "Windows\BackupWindow.xaml",
    "Windows\CreateBackupWindow.xaml",
    "Windows\DeviceDetailsWindow.xaml",
    "Windows\DeviceSerialNumbersWindow.xaml",
    "Windows\ExportWindow.xaml",
    "Windows\InventoryItemReportWindow.xaml",
    "Windows\InventorySelectionWindow.xaml",
    "Windows\MaintenanceDetailsWindow.xaml",
    "Windows\ManageCategoriesWindow.xaml",
    "Windows\ManageCitiesWindow.xaml",
    "Windows\ManageCustomerTypesWindow.xaml",
    "Windows\ManageInventoryCategoriesWindow.xaml",
    "Windows\ManageRecipientsWindow.xaml",
    "Windows\ManageSerialNumbersWindow.xaml",
    "Windows\ManageTechniciansWindow.xaml",
    "Windows\NotificationsWindow.xaml",
    "Windows\ReportsWindow.xaml",
    "Windows\SelectDeviceWindow.xaml",
    "Windows\SmartSystemWindow.xaml",
    "Windows\AdvancedInventorySettingsWindow.xaml"
)

$processedCount = 0
$errorCount = 0

foreach ($file in $windowFiles) {
    try {
        if (Test-Path $file) {
            Write-Host "Processing: $file" -ForegroundColor Yellow
            
            # Read file content
            $content = Get-Content $file -Raw -Encoding UTF8
            
            # Check if FlowDirection already exists
            if ($content -match 'FlowDirection="RightToLeft"') {
                Write-Host "File already has FlowDirection" -ForegroundColor Green
                continue
            }
            
            # Find Window tag pattern and add FlowDirection
            if ($content -match '(<Window[^>]*?)(\s*>)') {
                $newContent = $content -replace '(<Window[^>]*?)(\s*>)', '$1 FlowDirection="RightToLeft"$2'
                
                # Save updated file
                Set-Content $file -Value $newContent -Encoding UTF8
                Write-Host "FlowDirection applied successfully" -ForegroundColor Green
                $processedCount++
            } else {
                Write-Host "Window tag not found" -ForegroundColor Yellow
            }
        } else {
            Write-Host "File not found: $file" -ForegroundColor Red
            $errorCount++
        }
    }
    catch {
        Write-Host "Error processing $file : $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host ""
Write-Host "Results Summary:" -ForegroundColor Cyan
Write-Host "Processed: $processedCount files" -ForegroundColor Green
Write-Host "Errors: $errorCount files" -ForegroundColor Red
Write-Host ""
Write-Host "Arabic improvements completed!" -ForegroundColor Green
