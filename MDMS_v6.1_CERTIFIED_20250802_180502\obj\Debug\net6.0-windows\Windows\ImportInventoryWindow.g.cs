﻿#pragma checksum "..\..\..\..\Windows\ImportInventoryWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "65DFC6685153913E76FE77DD76060E6433241EA8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// ImportInventoryWindow
    /// </summary>
    public partial class ImportInventoryWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FilePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewButton;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OverwriteExistingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ValidateDataCheckBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ImportModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportButton;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileTypeText;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeText;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalRowsText;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidRowsText;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorRowsText;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DuplicateRowsText;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EstimatedTimeText;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PreviewDataGrid;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoScrollCheckBox;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer LogScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextBox;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ImportProgressBar;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/importinventorywindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FilePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            
            #line 46 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.PreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
            this.PreviewButton.Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.OverwriteExistingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.ValidateDataCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.ImportModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.ImportButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
            this.ImportButton.Click += new System.Windows.RoutedEventHandler(this.ImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.FileTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.FileSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalRowsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ValidRowsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ErrorRowsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.DuplicateRowsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.EstimatedTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.PreviewDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 17:
            
            #line 152 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 155 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.AutoScrollCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.LogScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 21:
            this.LogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.ImportProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 24:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            
            #line 287 "..\..\..\..\Windows\ImportInventoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

