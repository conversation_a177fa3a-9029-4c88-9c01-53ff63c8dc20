using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.ComponentModel;
using System.Collections.ObjectModel;

namespace MedicalDevicesManager.Controls
{
    /// <summary>
    /// لوحة البحث المتقدم
    /// </summary>
    public class AdvancedSearchPanel : UserControl
    {
        #region Properties

        // البحث العام
        private TextBox _globalSearchBox;
        private Button _clearSearchButton;
        private Button _advancedSearchButton;

        // الفلاتر السريعة
        private ComboBox _quickFilterComboBox;
        private DatePicker _fromDatePicker;
        private DatePicker _toDatePicker;
        private ComboBox _statusFilterComboBox;

        // البحث المحفوظ
        private ComboBox _savedSearchesComboBox;
        private Button _saveSearchButton;
        private Button _deleteSavedSearchButton;

        // الفلاتر النشطة
        private StackPanel _activeFiltersPanel;
        private Dictionary<string, object> _activeFilters;

        // إعدادات البحث
        public bool EnableGlobalSearch { get; set; } = true;
        public bool EnableQuickFilters { get; set; } = true;
        public bool EnableSavedSearches { get; set; } = true;
        public bool EnableActiveFiltersDisplay { get; set; } = true;
        public bool EnableRealTimeSearch { get; set; } = true;

        // البيانات
        private List<SearchField> _searchFields;
        private List<SavedSearch> _savedSearches;

        #endregion

        #region Events

        public event EventHandler<SearchEventArgs> SearchRequested;
        public event EventHandler<FilterEventArgs> FilterChanged;
        public event EventHandler SearchCleared;

        #endregion

        #region Constructor

        public AdvancedSearchPanel()
        {
            _activeFilters = new Dictionary<string, object>();
            _searchFields = new List<SearchField>();
            _savedSearches = new List<SavedSearch>();
            
            InitializeComponent();
            LoadSavedSearches();
        }

        #endregion

        #region Initialization

        private void InitializeComponent()
        {
            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // البحث العام
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // الفلاتر السريعة
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // البحث المحفوظ
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // الفلاتر النشطة

            // البحث العام
            if (EnableGlobalSearch)
            {
                var globalSearchPanel = CreateGlobalSearchPanel();
                Grid.SetRow(globalSearchPanel, 0);
                mainGrid.Children.Add(globalSearchPanel);
            }

            // الفلاتر السريعة
            if (EnableQuickFilters)
            {
                var quickFiltersPanel = CreateQuickFiltersPanel();
                Grid.SetRow(quickFiltersPanel, 1);
                mainGrid.Children.Add(quickFiltersPanel);
            }

            // البحث المحفوظ
            if (EnableSavedSearches)
            {
                var savedSearchPanel = CreateSavedSearchPanel();
                Grid.SetRow(savedSearchPanel, 2);
                mainGrid.Children.Add(savedSearchPanel);
            }

            // الفلاتر النشطة
            if (EnableActiveFiltersDisplay)
            {
                var activeFiltersContainer = CreateActiveFiltersPanel();
                Grid.SetRow(activeFiltersContainer, 3);
                mainGrid.Children.Add(activeFiltersContainer);
            }

            Content = mainGrid;
        }

        private StackPanel CreateGlobalSearchPanel()
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(Color.FromRgb(255, 255, 255)),
                Margin = new Thickness(0, 5, 0, 5),
                Height = 50
            };

            // أيقونة البحث
            var searchIcon = new TextBlock
            {
                Text = "🔍",
                FontSize = 16,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0)
            };
            panel.Children.Add(searchIcon);

            // مربع البحث العام
            _globalSearchBox = new TextBox
            {
                Width = 350,
                Height = 35,
                Padding = new Thickness(10, 5, 10, 5),
                FontSize = 14,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            // إضافة placeholder text
            var placeholderText = "البحث في جميع الحقول...";
            _globalSearchBox.GotFocus += (s, e) =>
            {
                if (_globalSearchBox.Text == placeholderText)
                {
                    _globalSearchBox.Text = "";
                    _globalSearchBox.Foreground = Brushes.Black;
                }
            };

            _globalSearchBox.LostFocus += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(_globalSearchBox.Text))
                {
                    _globalSearchBox.Text = placeholderText;
                    _globalSearchBox.Foreground = Brushes.Gray;
                }
            };

            _globalSearchBox.Text = placeholderText;
            _globalSearchBox.Foreground = Brushes.Gray;

            if (EnableRealTimeSearch)
            {
                _globalSearchBox.TextChanged += GlobalSearchBox_TextChanged;
            }
            else
            {
                _globalSearchBox.KeyDown += GlobalSearchBox_KeyDown;
            }

            panel.Children.Add(_globalSearchBox);

            // زر مسح البحث
            _clearSearchButton = new Button
            {
                Content = "❌",
                Width = 35,
                Height = 35,
                Background = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                ToolTip = "مسح البحث",
                Margin = new Thickness(5, 0, 5, 0),
                Cursor = Cursors.Hand
            };
            _clearSearchButton.Click += ClearSearchButton_Click;
            panel.Children.Add(_clearSearchButton);

            // زر البحث المتقدم
            _advancedSearchButton = new Button
            {
                Content = "🔧",
                Width = 35,
                Height = 35,
                Background = new SolidColorBrush(Color.FromRgb(23, 162, 184)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                ToolTip = "بحث متقدم",
                Margin = new Thickness(5, 0, 5, 0),
                Cursor = Cursors.Hand
            };
            _advancedSearchButton.Click += AdvancedSearchButton_Click;
            panel.Children.Add(_advancedSearchButton);

            return panel;
        }

        private StackPanel CreateQuickFiltersPanel()
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                Margin = new Thickness(0, 5, 0, 5),
                Height = 45
            };

            // فلتر سريع
            var quickFilterLabel = new TextBlock
            {
                Text = "فلتر سريع:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0),
                FontWeight = FontWeights.SemiBold
            };
            panel.Children.Add(quickFilterLabel);

            _quickFilterComboBox = new ComboBox
            {
                Width = 150,
                Height = 30,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 15, 0)
            };
            _quickFilterComboBox.Items.Add("الكل");
            _quickFilterComboBox.Items.Add("نشط");
            _quickFilterComboBox.Items.Add("غير نشط");
            _quickFilterComboBox.Items.Add("متاح");
            _quickFilterComboBox.Items.Add("غير متاح");
            _quickFilterComboBox.SelectedIndex = 0;
            _quickFilterComboBox.SelectionChanged += QuickFilterComboBox_SelectionChanged;
            panel.Children.Add(_quickFilterComboBox);

            // فلتر التاريخ من
            var fromDateLabel = new TextBlock
            {
                Text = "من:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0)
            };
            panel.Children.Add(fromDateLabel);

            _fromDatePicker = new DatePicker
            {
                Width = 120,
                Height = 30,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 15, 0)
            };
            _fromDatePicker.SelectedDateChanged += DatePicker_SelectedDateChanged;
            panel.Children.Add(_fromDatePicker);

            // فلتر التاريخ إلى
            var toDateLabel = new TextBlock
            {
                Text = "إلى:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0)
            };
            panel.Children.Add(toDateLabel);

            _toDatePicker = new DatePicker
            {
                Width = 120,
                Height = 30,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 15, 0)
            };
            _toDatePicker.SelectedDateChanged += DatePicker_SelectedDateChanged;
            panel.Children.Add(_toDatePicker);

            // فلتر الحالة
            var statusLabel = new TextBlock
            {
                Text = "الحالة:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0)
            };
            panel.Children.Add(statusLabel);

            _statusFilterComboBox = new ComboBox
            {
                Width = 120,
                Height = 30,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 10, 0)
            };
            _statusFilterComboBox.Items.Add("الكل");
            _statusFilterComboBox.Items.Add("مكتمل");
            _statusFilterComboBox.Items.Add("معلق");
            _statusFilterComboBox.Items.Add("ملغي");
            _statusFilterComboBox.SelectedIndex = 0;
            _statusFilterComboBox.SelectionChanged += StatusFilterComboBox_SelectionChanged;
            panel.Children.Add(_statusFilterComboBox);

            return panel;
        }

        private StackPanel CreateSavedSearchPanel()
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(Color.FromRgb(255, 255, 255)),
                Margin = new Thickness(0, 5, 0, 5),
                Height = 40
            };

            // البحثات المحفوظة
            var savedSearchLabel = new TextBlock
            {
                Text = "البحثات المحفوظة:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0),
                FontWeight = FontWeights.SemiBold
            };
            panel.Children.Add(savedSearchLabel);

            _savedSearchesComboBox = new ComboBox
            {
                Width = 200,
                Height = 30,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 10, 0)
            };
            _savedSearchesComboBox.SelectionChanged += SavedSearchesComboBox_SelectionChanged;
            panel.Children.Add(_savedSearchesComboBox);

            // زر حفظ البحث
            _saveSearchButton = new Button
            {
                Content = "💾",
                Width = 30,
                Height = 30,
                Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                ToolTip = "حفظ البحث الحالي",
                Margin = new Thickness(5, 0, 5, 0),
                Cursor = Cursors.Hand
            };
            _saveSearchButton.Click += SaveSearchButton_Click;
            panel.Children.Add(_saveSearchButton);

            // زر حذف البحث المحفوظ
            _deleteSavedSearchButton = new Button
            {
                Content = "🗑️",
                Width = 30,
                Height = 30,
                Background = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                ToolTip = "حذف البحث المحفوظ",
                Margin = new Thickness(5, 0, 5, 0),
                Cursor = Cursors.Hand
            };
            _deleteSavedSearchButton.Click += DeleteSavedSearchButton_Click;
            panel.Children.Add(_deleteSavedSearchButton);

            return panel;
        }

        private Border CreateActiveFiltersPanel()
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(233, 236, 239)),
                Margin = new Thickness(0, 5, 0, 5),
                Padding = new Thickness(10, 5, 10, 5),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                Visibility = Visibility.Collapsed
            };

            var panel = new StackPanel();

            var title = new TextBlock
            {
                Text = "الفلاتر النشطة:",
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 5)
            };
            panel.Children.Add(title);

            _activeFiltersPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };
            panel.Children.Add(_activeFiltersPanel);

            border.Child = panel;
            return border;
        }

        #endregion

        #region Event Handlers

        private void GlobalSearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_globalSearchBox.Text != "البحث في جميع الحقول..." && !string.IsNullOrWhiteSpace(_globalSearchBox.Text))
            {
                OnSearchRequested(new SearchEventArgs { SearchTerm = _globalSearchBox.Text, SearchType = SearchType.Global });
            }
            else if (string.IsNullOrWhiteSpace(_globalSearchBox.Text))
            {
                OnSearchCleared();
            }
        }

        private void GlobalSearchBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (_globalSearchBox.Text != "البحث في جميع الحقول..." && !string.IsNullOrWhiteSpace(_globalSearchBox.Text))
                {
                    OnSearchRequested(new SearchEventArgs { SearchTerm = _globalSearchBox.Text, SearchType = SearchType.Global });
                }
            }
        }

        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            ClearAllFilters();
            OnSearchCleared();
        }

        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            ShowAdvancedSearchDialog();
        }

        private void QuickFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_quickFilterComboBox.SelectedItem != null)
            {
                var filterValue = _quickFilterComboBox.SelectedItem.ToString();
                if (filterValue != "الكل")
                {
                    AddActiveFilter("QuickFilter", filterValue);
                }
                else
                {
                    RemoveActiveFilter("QuickFilter");
                }
                OnFilterChanged(new FilterEventArgs { FilterName = "QuickFilter", FilterValue = filterValue });
            }
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_fromDatePicker.SelectedDate.HasValue)
            {
                AddActiveFilter("FromDate", _fromDatePicker.SelectedDate.Value);
            }
            else
            {
                RemoveActiveFilter("FromDate");
            }

            if (_toDatePicker.SelectedDate.HasValue)
            {
                AddActiveFilter("ToDate", _toDatePicker.SelectedDate.Value);
            }
            else
            {
                RemoveActiveFilter("ToDate");
            }

            OnFilterChanged(new FilterEventArgs 
            { 
                FilterName = "DateRange", 
                FilterValue = new { From = _fromDatePicker.SelectedDate, To = _toDatePicker.SelectedDate } 
            });
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_statusFilterComboBox.SelectedItem != null)
            {
                var filterValue = _statusFilterComboBox.SelectedItem.ToString();
                if (filterValue != "الكل")
                {
                    AddActiveFilter("Status", filterValue);
                }
                else
                {
                    RemoveActiveFilter("Status");
                }
                OnFilterChanged(new FilterEventArgs { FilterName = "Status", FilterValue = filterValue });
            }
        }

        private void SavedSearchesComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_savedSearchesComboBox.SelectedItem is SavedSearch savedSearch)
            {
                LoadSavedSearch(savedSearch);
            }
        }

        private void SaveSearchButton_Click(object sender, RoutedEventArgs e)
        {
            ShowSaveSearchDialog();
        }

        private void DeleteSavedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            if (_savedSearchesComboBox.SelectedItem is SavedSearch savedSearch)
            {
                DeleteSavedSearch(savedSearch);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// إضافة حقل بحث
        /// </summary>
        public void AddSearchField(SearchField field)
        {
            _searchFields.Add(field);
        }

        /// <summary>
        /// مسح جميع الفلاتر
        /// </summary>
        public void ClearAllFilters()
        {
            _globalSearchBox.Text = "البحث في جميع الحقول...";
            _globalSearchBox.Foreground = Brushes.Gray;
            _quickFilterComboBox.SelectedIndex = 0;
            _fromDatePicker.SelectedDate = null;
            _toDatePicker.SelectedDate = null;
            _statusFilterComboBox.SelectedIndex = 0;
            _activeFilters.Clear();
            UpdateActiveFiltersDisplay();
        }

        /// <summary>
        /// الحصول على الفلاتر النشطة
        /// </summary>
        public Dictionary<string, object> GetActiveFilters()
        {
            return new Dictionary<string, object>(_activeFilters);
        }

        /// <summary>
        /// تعيين فلتر
        /// </summary>
        public void SetFilter(string filterName, object filterValue)
        {
            AddActiveFilter(filterName, filterValue);
        }

        #endregion

        #region Private Methods

        private void AddActiveFilter(string filterName, object filterValue)
        {
            _activeFilters[filterName] = filterValue;
            UpdateActiveFiltersDisplay();
        }

        private void RemoveActiveFilter(string filterName)
        {
            _activeFilters.Remove(filterName);
            UpdateActiveFiltersDisplay();
        }

        private void UpdateActiveFiltersDisplay()
        {
            if (!EnableActiveFiltersDisplay) return;

            _activeFiltersPanel.Children.Clear();

            foreach (var filter in _activeFilters)
            {
                var filterTag = CreateFilterTag(filter.Key, filter.Value);
                _activeFiltersPanel.Children.Add(filterTag);
            }

            var container = _activeFiltersPanel.Parent as StackPanel;
            var border = container?.Parent as Border;
            if (border != null)
            {
                border.Visibility = _activeFilters.Any() ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        private Border CreateFilterTag(string filterName, object filterValue)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
                CornerRadius = new CornerRadius(15),
                Padding = new Thickness(10, 3, 10, 3),
                Margin = new Thickness(0, 0, 5, 0)
            };

            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var text = new TextBlock
            {
                Text = $"{GetFilterDisplayName(filterName)}: {filterValue}",
                Foreground = Brushes.White,
                FontSize = 12,
                VerticalAlignment = VerticalAlignment.Center
            };
            panel.Children.Add(text);

            var removeButton = new Button
            {
                Content = "×",
                Width = 16,
                Height = 16,
                Background = Brushes.Transparent,
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(5, 0, 0, 0),
                Cursor = Cursors.Hand,
                FontSize = 12,
                Tag = filterName
            };
            removeButton.Click += (s, e) =>
            {
                RemoveActiveFilter(filterName);
                OnFilterChanged(new FilterEventArgs { FilterName = filterName, FilterValue = null });
            };
            panel.Children.Add(removeButton);

            border.Child = panel;
            return border;
        }

        private string GetFilterDisplayName(string filterName)
        {
            return filterName switch
            {
                "QuickFilter" => "فلتر سريع",
                "FromDate" => "من تاريخ",
                "ToDate" => "إلى تاريخ",
                "Status" => "الحالة",
                _ => filterName
            };
        }

        private void ShowAdvancedSearchDialog()
        {
            var dialog = new AdvancedSearchDialog(_searchFields, _activeFilters);
            if (dialog.ShowDialog() == true)
            {
                var filters = dialog.GetFilters();
                foreach (var filter in filters)
                {
                    AddActiveFilter(filter.Key, filter.Value);
                }
                OnSearchRequested(new SearchEventArgs { SearchType = SearchType.Advanced, Filters = filters });
            }
        }

        private void ShowSaveSearchDialog()
        {
            var dialog = new SaveSearchDialog();
            if (dialog.ShowDialog() == true)
            {
                var searchName = dialog.SearchName;
                var savedSearch = new SavedSearch
                {
                    Name = searchName,
                    Filters = new Dictionary<string, object>(_activeFilters),
                    GlobalSearchTerm = _globalSearchBox.Text != "البحث في جميع الحقول..." ? _globalSearchBox.Text : "",
                    CreatedDate = DateTime.Now
                };

                _savedSearches.Add(savedSearch);
                RefreshSavedSearchesComboBox();
                SaveSearchesToStorage();
            }
        }

        private void LoadSavedSearch(SavedSearch savedSearch)
        {
            ClearAllFilters();

            if (!string.IsNullOrEmpty(savedSearch.GlobalSearchTerm))
            {
                _globalSearchBox.Text = savedSearch.GlobalSearchTerm;
                _globalSearchBox.Foreground = Brushes.Black;
            }

            foreach (var filter in savedSearch.Filters)
            {
                AddActiveFilter(filter.Key, filter.Value);
                
                // تطبيق الفلتر على عناصر التحكم
                ApplyFilterToControls(filter.Key, filter.Value);
            }

            OnSearchRequested(new SearchEventArgs 
            { 
                SearchType = SearchType.Saved, 
                SearchTerm = savedSearch.GlobalSearchTerm,
                Filters = savedSearch.Filters 
            });
        }

        private void ApplyFilterToControls(string filterName, object filterValue)
        {
            switch (filterName)
            {
                case "QuickFilter":
                    _quickFilterComboBox.SelectedItem = filterValue;
                    break;
                case "FromDate":
                    if (filterValue is DateTime fromDate)
                        _fromDatePicker.SelectedDate = fromDate;
                    break;
                case "ToDate":
                    if (filterValue is DateTime toDate)
                        _toDatePicker.SelectedDate = toDate;
                    break;
                case "Status":
                    _statusFilterComboBox.SelectedItem = filterValue;
                    break;
            }
        }

        private void DeleteSavedSearch(SavedSearch savedSearch)
        {
            if (MessageBox.Show($"هل تريد حذف البحث المحفوظ '{savedSearch.Name}'؟", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
            {
                _savedSearches.Remove(savedSearch);
                RefreshSavedSearchesComboBox();
                SaveSearchesToStorage();
            }
        }

        private void RefreshSavedSearchesComboBox()
        {
            _savedSearchesComboBox.Items.Clear();
            _savedSearchesComboBox.Items.Add("اختر بحث محفوظ...");
            
            foreach (var savedSearch in _savedSearches)
            {
                _savedSearchesComboBox.Items.Add(savedSearch);
            }
            
            _savedSearchesComboBox.SelectedIndex = 0;
        }

        private void LoadSavedSearches()
        {
            // تحميل البحثات المحفوظة من التخزين
            // يمكن تنفيذها لاحقاً مع قاعدة البيانات
            RefreshSavedSearchesComboBox();
        }

        private void SaveSearchesToStorage()
        {
            // حفظ البحثات المحفوظة في التخزين
            // يمكن تنفيذها لاحقاً مع قاعدة البيانات
        }

        #endregion

        #region Event Raising

        protected virtual void OnSearchRequested(SearchEventArgs e)
        {
            SearchRequested?.Invoke(this, e);
        }

        protected virtual void OnFilterChanged(FilterEventArgs e)
        {
            FilterChanged?.Invoke(this, e);
        }

        protected virtual void OnSearchCleared()
        {
            SearchCleared?.Invoke(this, EventArgs.Empty);
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// حقل البحث
    /// </summary>
    public class SearchField
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public SearchFieldType Type { get; set; }
        public List<string> Options { get; set; } = new List<string>();
    }

    /// <summary>
    /// نوع حقل البحث
    /// </summary>
    public enum SearchFieldType
    {
        Text,
        Number,
        Date,
        Boolean,
        Dropdown
    }

    /// <summary>
    /// البحث المحفوظ
    /// </summary>
    public class SavedSearch
    {
        public string Name { get; set; }
        public string GlobalSearchTerm { get; set; }
        public Dictionary<string, object> Filters { get; set; }
        public DateTime CreatedDate { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }

    /// <summary>
    /// معاملات حدث البحث
    /// </summary>
    public class SearchEventArgs : EventArgs
    {
        public string SearchTerm { get; set; }
        public SearchType SearchType { get; set; }
        public Dictionary<string, object> Filters { get; set; }
    }

    /// <summary>
    /// معاملات حدث الفلتر
    /// </summary>
    public class FilterEventArgs : EventArgs
    {
        public string FilterName { get; set; }
        public object FilterValue { get; set; }
    }

    /// <summary>
    /// نوع البحث
    /// </summary>
    public enum SearchType
    {
        Global,
        Advanced,
        Saved,
        Quick
    }

    #endregion
}
