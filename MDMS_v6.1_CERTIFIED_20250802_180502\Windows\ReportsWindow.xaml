<Window x:Class="MedicalDevicesManager.Windows.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقارير المتقدمة" Height="740" Width="1040"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        Style="{StaticResource ArabicWindow}">

    <Border Style="{StaticResource ModernCard}" Margin="16">
        <Grid Margin="32">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان المحدث -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,32">
                <Border Background="{StaticResource PrimaryBrush}" CornerRadius="12" Width="48" Height="48" Margin="0,0,16,0">
                    <TextBlock Text="📊" FontSize="24" Foreground="White"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="التقارير المتقدمة والتفاعلية"
                               Style="{StaticResource ModernTitle}" FontSize="24"/>
                    <TextBlock Text="إنشاء وعرض التقارير التفصيلية للنظام"
                               Style="{StaticResource ModernMutedText}" Margin="0,4,0,0"/>
                </StackPanel>
            </StackPanel>

            <!-- أزرار التقارير المحدثة -->
            <Border Grid.Row="1" Style="{StaticResource ArabicCard}" Background="#F8FAFC" Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="أنواع التقارير" Style="{StaticResource ModernSubtitle}" Margin="0,0,0,20"/>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- الصف الأول من الأزرار -->
                        <WrapPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Button x:Name="SalesReportBtn" Content="📈 تقرير المبيعات" Width="160"
                                    Style="{StaticResource SuccessButton}"
                                    Margin="8" Click="SalesReportBtn_Click"/>

                            <Button x:Name="InventoryReportBtn" Content="📦 تقرير المخزون" Width="160"
                                    Style="{StaticResource ModernButton}" Background="{StaticResource InfoBrush}"
                                    Margin="8" Click="InventoryReportBtn_Click"/>

                            <Button x:Name="CustomersReportBtn" Content="👥 تقرير العملاء" Width="160"
                                    Style="{StaticResource ModernButton}" Background="{StaticResource WarningBrush}"
                                    Margin="8" Click="CustomersReportBtn_Click"/>
                        </WrapPanel>

                        <!-- الصف الثاني من الأزرار -->
                        <WrapPanel Grid.Row="1" HorizontalAlignment="Center">
                            <Button x:Name="MaintenanceReportBtn" Content="🛠️ تقرير الصيانة" Width="160"
                                    Style="{StaticResource DangerButton}"
                                    Margin="8" Click="MaintenanceReportBtn_Click"/>

                            <Button x:Name="FinancialReportBtn" Content="💰 التقرير المالي" Width="160"
                                    Style="{StaticResource ModernButton}" Background="#8B5CF6"
                                    Margin="8" Click="FinancialReportBtn_Click"/>

                            <Button x:Name="ComprehensiveReportBtn" Content="📋 التقرير الشامل" Width="160"
                                    Style="{StaticResource SecondaryButton}"
                        FontSize="14" FontWeight="SemiBold" Margin="5" Click="ComprehensiveReportBtn_Click"/>
            </WrapPanel>

            <!-- أزرار الطباعة والتصدير -->
            <WrapPanel Grid.Row="1" HorizontalAlignment="Center">
                <Button x:Name="PrintReportBtn" Content="🖨️ طباعة التقرير" Width="140" Height="35"
                        Background="#495057" Foreground="White" BorderThickness="0"
                        FontSize="12" FontWeight="SemiBold" Margin="5" Click="PrintReportBtn_Click" IsEnabled="False"/>

                <Button x:Name="ExportPdfBtn" Content="📄 تصدير PDF" Width="120" Height="35"
                        Background="#E74C3C" Foreground="White" BorderThickness="0"
                        FontSize="12" FontWeight="SemiBold" Margin="5" Click="ExportPdfBtn_Click" IsEnabled="False"/>

                <Button x:Name="ExportExcelBtn" Content="📊 تصدير Excel" Width="120" Height="35"
                        Background="#1F7A1F" Foreground="White" BorderThickness="0"
                        FontSize="12" FontWeight="SemiBold" Margin="5" Click="ExportExcelBtn_Click" IsEnabled="False"/>
            </WrapPanel>
        </Grid>
        
        <!-- منطقة عرض التقارير -->
        <Border Grid.Row="2" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="5">
            <ScrollViewer x:Name="ReportScrollViewer" VerticalScrollBarVisibility="Auto">
                <StackPanel x:Name="ReportContent" Margin="20">
                    <!-- محتوى التقرير سيتم إضافته هنا -->
                    <TextBlock Text="اختر نوع التقرير من الأزرار أعلاه لعرض البيانات" 
                               FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center" 
                               Foreground="#6C757D" Margin="0,50,0,0"/>
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</Window>
