<Window x:Class="MedicalDevicesManager.Windows.SimpleExportQuotationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام عروض الأسعار مع التصدير" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">
    
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel>
                <TextBlock Text="💰 نظام عروض الأسعار مع التصدير" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="إنشاء وتصدير عروض الأسعار بصيغ مختلفة" 
                          FontSize="16" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Export Panel -->
        <Border Grid.Row="1" Background="White" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📤 التصدير:" VerticalAlignment="Center" Margin="0,0,15,0" FontWeight="Bold" FontSize="16"/>
                    <Button Content="🖨️ طباعة" Padding="12,6" Margin="5,0"
                            Background="#9C27B0" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="PrintButton_Click"/>
                    <Button Content="📄 حفظ PDF" Padding="12,6" Margin="5,0"
                            Background="#F44336" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="SavePdfButton_Click"/>
                    <Button Content="📊 حفظ Excel" Padding="12,6" Margin="5,0"
                            Background="#4CAF50" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="SaveExcelButton_Click"/>
                    <Button Content="📝 حفظ Word" Padding="12,6" Margin="5,0"
                            Background="#2196F3" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="SaveWordButton_Click"/>
                </StackPanel>
                
                <Button Grid.Column="1" Content="🔄 إنشاء عرض جديد" Padding="15,8"
                        Background="#FF9800" Foreground="White" BorderThickness="0"
                        FontWeight="Bold" Click="GenerateQuotationButton_Click"/>
            </Grid>
        </Border>

        <!-- Content Area -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel x:Name="ContentPanel">
                
                <!-- معلومات العرض -->
                <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock x:Name="QuotationTitleText" Text="📋 عرض أسعار شامل للأجهزة الطبية" 
                                  FontSize="18" FontWeight="Bold" 
                                  Foreground="#1976D2" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock x:Name="QuotationDateText" Text="📅 تاريخ العرض: --" FontWeight="Bold"/>
                                <TextBlock x:Name="QuotationNumberText" Text="🔢 رقم العرض: --" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock x:Name="ItemsCountText" Text="📦 عدد العناصر: --" FontWeight="Bold"/>
                                <TextBlock x:Name="CategoriesText" Text="📂 الفئات: --" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock x:Name="TotalAmountText" Text="💰 المبلغ الإجمالي: --" FontWeight="Bold" Foreground="Green"/>
                                <TextBlock x:Name="ValidityText" Text="⏰ صالح حتى: --" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- جدول العناصر -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📋 تفاصيل العناصر" 
                                  FontSize="16" FontWeight="Bold" 
                                  Foreground="#333" Margin="0,0,0,15"/>
                        
                        <DataGrid x:Name="ItemsDataGrid" AutoGenerateColumns="False" 
                                  CanUserAddRows="False" CanUserDeleteRows="False"
                                  GridLinesVisibility="All" HeadersVisibility="Column"
                                  Background="White" AlternatingRowBackground="#F9F9F9">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="#" Binding="{Binding ItemNumber}" Width="50" IsReadOnly="True"/>
                                <DataGridTextColumn Header="اسم العنصر" Binding="{Binding Name}" Width="200" IsReadOnly="True"/>
                                <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="120" IsReadOnly="True"/>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="150" IsReadOnly="True"/>
                                <DataGridTextColumn Header="السعر" Binding="{Binding UnitPriceFormatted}" Width="100" IsReadOnly="True"/>
                                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80" IsReadOnly="True"/>
                                <DataGridTextColumn Header="المجموع" Binding="{Binding TotalPriceFormatted}" Width="120" IsReadOnly="True"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- ملخص المبالغ -->
                <Border Background="#F1F8E9" BorderBrush="#8BC34A" BorderThickness="1" 
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="💰 ملخص المبالغ" 
                                  FontSize="16" FontWeight="Bold" 
                                  Foreground="#689F38" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock x:Name="SubtotalText" Text="المجموع الفرعي: -- ريال" FontWeight="Bold" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock x:Name="TaxText" Text="ضريبة القيمة المضافة (15%): -- ريال" FontWeight="Bold" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock x:Name="GrandTotalText" Text="المجموع الإجمالي: -- ريال" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Green" FontSize="16"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- ملاحظات -->
                <Border Background="#FFF3E0" BorderBrush="#FF9800" BorderThickness="1" 
                        CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📝 ملاحظات وشروط العرض" 
                                  FontSize="16" FontWeight="Bold" 
                                  Foreground="#F57C00" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        
                        <StackPanel>
                            <TextBlock Text="• العرض صالح لمدة 30 يوماً من تاريخ الإصدار" Margin="0,5,0,0"/>
                            <TextBlock Text="• جميع الأسعار شاملة ضريبة القيمة المضافة" Margin="0,5,0,0"/>
                            <TextBlock Text="• التوصيل والتركيب مجاني داخل المدينة" Margin="0,5,0,0"/>
                            <TextBlock Text="• ضمان شامل حسب نوع الجهاز" Margin="0,5,0,0"/>
                            <TextBlock Text="• تدريب مجاني للفريق الطبي" Margin="0,5,0,0"/>
                            <TextBlock Text="• دعم فني متخصص" Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="3" Background="#1976D2" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusText" Text="جاهز للتصدير" 
                          Foreground="White" VerticalAlignment="Center" FontWeight="Bold"/>
                
                <Button Grid.Column="1" Content="❌ إغلاق" Padding="15,8"
                        Background="#F44336" Foreground="White" BorderThickness="0"
                        FontWeight="Bold" Click="Close_Click"/>
            </Grid>
        </Border>
    </Grid>
</Window>
