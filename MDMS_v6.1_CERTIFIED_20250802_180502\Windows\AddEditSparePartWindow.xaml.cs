using System;
using System.Linq;
using System.Windows;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditSparePartWindow : Window
    {
        private SparePart _sparePart;
        private bool _isEditMode;

        public AddEditSparePartWindow(SparePart sparePart = null)
        {
            InitializeComponent();
            _sparePart = sparePart;
            _isEditMode = sparePart != null;
            
            LoadDataAsync();
            
            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل قطعة غيار أو ملحق";
                LoadSparePartData();
            }
            else
            {
                // تعيين القيم الافتراضية
                PurchaseDatePicker.SelectedDate = DateTime.Now;
                AvailableQuantityTextBox.Text = "0";
                PurchasePriceTextBox.Text = "0";
                IndividualSellingPriceTextBox.Text = "0";
                WholesaleSellingPriceTextBox.Text = "0";
                CreatedDateTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            }
        }
        
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل أسماء الأجهزة الطبية
                var devices = await App.DatabaseContext.MedicalDevices.ToListAsync();
                AssociatedDeviceComboBox.ItemsSource = devices;
                
                if (!_isEditMode && devices.Any())
                    AssociatedDeviceComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void LoadSparePartData()
        {
            if (_sparePart == null) return;

            NameTextBox.Text = _sparePart.Name;
            AssociatedDeviceComboBox.Text = _sparePart.AssociatedDeviceName;
            AvailableQuantityTextBox.Text = _sparePart.AvailableQuantity.ToString();
            PurchasePriceTextBox.Text = _sparePart.PurchasePrice.ToString();
            IndividualSellingPriceTextBox.Text = _sparePart.IndividualSellingPrice.ToString();
            WholesaleSellingPriceTextBox.Text = _sparePart.WholesaleSellingPrice.ToString();
            PurchaseDatePicker.SelectedDate = _sparePart.PurchaseDate;
            WorkshopStorageLocationTextBox.Text = _sparePart.WorkshopStorageLocation;
            CreatedDateTextBlock.Text = _sparePart.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            LastUpdatedTextBlock.Text = _sparePart.LastUpdated.ToString("dd/MM/yyyy HH:mm");
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateSparePartData();
                    App.DatabaseContext.SpareParts.Update(_sparePart);
                }
                else
                {
                    _sparePart = new SparePart();
                    UpdateSparePartData();
                    _sparePart.CreatedDate = DateTime.Now;
                    App.DatabaseContext.SpareParts.Add(_sparePart);
                }
                
                _sparePart.LastUpdated = DateTime.Now;
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _isEditMode ? "تم تحديث قطعة الغيار بنجاح!" : "تم إضافة قطعة الغيار بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateSparePartData()
        {
            _sparePart.Name = NameTextBox.Text.Trim();
            _sparePart.AssociatedDeviceName = AssociatedDeviceComboBox.Text.Trim();
            _sparePart.AvailableQuantity = int.Parse(AvailableQuantityTextBox.Text);
            _sparePart.PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text);
            _sparePart.IndividualSellingPrice = decimal.Parse(IndividualSellingPriceTextBox.Text);
            _sparePart.WholesaleSellingPrice = decimal.Parse(WholesaleSellingPriceTextBox.Text);
            _sparePart.PurchaseDate = PurchaseDatePicker.SelectedDate ?? DateTime.Now;
            _sparePart.WorkshopStorageLocation = WorkshopStorageLocationTextBox.Text.Trim();
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم القطعة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(AssociatedDeviceComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار اسم الجهاز المرتبط", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!int.TryParse(AvailableQuantityTextBox.Text, out int quantity) || quantity < 0)
            {
                MessageBox.Show("يرجى إدخال عدد صحيح للكمية المتوفرة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) || purchasePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(IndividualSellingPriceTextBox.Text, out decimal individualPrice) || individualPrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع مفرد صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(WholesaleSellingPriceTextBox.Text, out decimal wholesalePrice) || wholesalePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع جملة صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (PurchaseDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الشراء", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
