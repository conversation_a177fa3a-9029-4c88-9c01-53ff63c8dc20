<Window x:Class="MedicalDevicesManager.Windows.SmartSystemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="النظام الذكي المتكامل" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان والبحث -->
        <Border Grid.Row="0" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="🧠 النظام الذكي المتكامل" 
                           FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                           Margin="0,0,0,15" Foreground="#1976D2"/>
                
                <!-- شريط البحث الذكي -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Grid.Column="0" x:Name="SmartSearchTextBox" Height="40" FontSize="16"
                             Padding="15,10" Margin="0,0,10,0"
                             Text="🔍 البحث الذكي عبر جميع الوحدات..."
                             Foreground="#6C757D" GotFocus="SmartSearchTextBox_GotFocus"
                             LostFocus="SmartSearchTextBox_LostFocus" TextChanged="SmartSearchTextBox_TextChanged"/>
                    
                    <Button Grid.Column="1" x:Name="SearchButton" Content="🔍 بحث" Width="100" Height="40"
                            Background="#007BFF" Foreground="White" BorderThickness="0" 
                            FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SearchButton_Click"/>
                    
                    <Button Grid.Column="2" x:Name="RefreshButton" Content="🔄 تحديث" Width="100" Height="40"
                            Background="#28A745" Foreground="White" BorderThickness="0" 
                            FontSize="14" FontWeight="SemiBold" Click="RefreshButton_Click"/>
                </Grid>
            </Grid>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="1" FontSize="14">
            
            <!-- تبويب الإحصائيات المباشرة -->
            <TabItem Header="📊 الإحصائيات المباشرة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- إحصائيات المبيعات -->
                        <GroupBox Grid.Row="0" Header="💰 إحصائيات المبيعات" Margin="0,0,0,20" Padding="15">
                            <Grid x:Name="SalesStatsGrid">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- بطاقات الإحصائيات ستتم إضافتها برمجياً -->
                            </Grid>
                        </GroupBox>
                        
                        <!-- إحصائيات المخزون -->
                        <GroupBox Grid.Row="1" Header="📦 إحصائيات المخزون" Margin="0,0,0,20" Padding="15">
                            <Grid x:Name="InventoryStatsGrid">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إحصائيات الأجهزة -->
                        <GroupBox Grid.Row="2" Header="🏥 إحصائيات الأجهزة" Margin="0,0,0,20" Padding="15">
                            <Grid x:Name="DevicesStatsGrid">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إحصائيات العملاء -->
                        <GroupBox Grid.Row="3" Header="👥 إحصائيات العملاء" Margin="0,0,0,20" Padding="15">
                            <Grid x:Name="CustomersStatsGrid">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                            </Grid>
                        </GroupBox>
                        
                        <!-- الإحصائيات المالية -->
                        <GroupBox Grid.Row="4" Header="💳 الإحصائيات المالية" Padding="15">
                            <Grid x:Name="FinancialStatsGrid">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                            </Grid>
                        </GroupBox>
                    </Grid>
                </ScrollViewer>
            </TabItem>
            
            <!-- تبويب التنبيهات الذكية -->
            <TabItem Header="🔔 التنبيهات الذكية">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- شريط أدوات التنبيهات -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <Button x:Name="GenerateAlertsButton" Content="🔄 إنشاء تنبيهات" Width="130" Height="35"
                                Background="#17A2B8" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Margin="0,0,10,0" Click="GenerateAlertsButton_Click"/>
                        
                        <ComboBox x:Name="AlertTypeFilterComboBox" Width="150" Height="35" Margin="0,0,10,0"
                                  SelectionChanged="AlertTypeFilterComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع التنبيهات"/>
                            <ComboBoxItem Content="ضمان"/>
                            <ComboBoxItem Content="صيانة"/>
                            <ComboBoxItem Content="مخزون"/>
                            <ComboBoxItem Content="عام"/>
                        </ComboBox>
                        
                        <ComboBox x:Name="AlertPriorityFilterComboBox" Width="120" Height="35" Margin="0,0,10,0"
                                  SelectionChanged="AlertPriorityFilterComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع الأولويات"/>
                            <ComboBoxItem Content="عالي"/>
                            <ComboBoxItem Content="متوسط"/>
                            <ComboBoxItem Content="منخفض"/>
                        </ComboBox>
                        
                        <Button x:Name="MarkAllReadButton" Content="✅ تحديد الكل كمقروء" Width="140" Height="35"
                                Background="#6C757D" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Click="MarkAllReadButton_Click"/>
                    </StackPanel>
                    
                    <!-- جدول التنبيهات -->
                    <DataGrid Grid.Row="1" x:Name="AlertsDataGrid" 
                              AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA" FontSize="12">
                        
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Header="الأولوية" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Priority}" HorizontalAlignment="Center" FontWeight="Bold">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Priority}" Value="عالي">
                                                            <Setter Property="Foreground" Value="#DC3545"/>
                                                            <Setter Property="Text" Value="🔴 عالي"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Priority}" Value="متوسط">
                                                            <Setter Property="Foreground" Value="#FFC107"/>
                                                            <Setter Property="Text" Value="🟡 متوسط"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Priority}" Value="منخفض">
                                                            <Setter Property="Foreground" Value="#28A745"/>
                                                            <Setter Property="Text" Value="🟢 منخفض"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTextColumn Header="النوع" Binding="{Binding AlertType}" Width="80"/>
                            <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="200"/>
                            <DataGridTextColumn Header="الرسالة" Binding="{Binding Message}" Width="300"/>
                            <DataGridTextColumn Header="العنصر المرتبط" Binding="{Binding RelatedItemName}" Width="150"/>
                            <DataGridTextColumn Header="تاريخ التنبيه" Binding="{Binding AlertDate, StringFormat=dd/MM/yyyy HH:mm}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                            <DataGridTextColumn Header="الإجراء المطلوب" Binding="{Binding ActionRequired}" Width="200"/>
                            
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="✅" Width="30" Height="25" Background="#28A745" 
                                                    Foreground="White" BorderThickness="0" Margin="2,0,2,0"
                                                    ToolTip="تحديد كمقروء" Click="MarkReadButton_Click"/>
                                            <Button Content="⏰" Width="30" Height="25" Background="#FFC107" 
                                                    Foreground="White" BorderThickness="0" Margin="2,0,2,0"
                                                    ToolTip="تأجيل" Click="SnoozeButton_Click"/>
                                            <Button Content="✔️" Width="30" Height="25" Background="#17A2B8" 
                                                    Foreground="White" BorderThickness="0" Margin="2,0,2,0"
                                                    ToolTip="مكتمل" Click="CompleteButton_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                    
                    <!-- إحصائيات التنبيهات -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,15,0,0">
                        <TextBlock x:Name="AlertsCountTextBlock" Text="" FontWeight="SemiBold" 
                                   VerticalAlignment="Center" Margin="0,0,20,0"/>
                        <TextBlock x:Name="HighPriorityCountTextBlock" Text="" FontWeight="SemiBold" 
                                   Foreground="#DC3545" VerticalAlignment="Center" Margin="0,0,20,0"/>
                        <TextBlock x:Name="OverdueCountTextBlock" Text="" FontWeight="SemiBold" 
                                   Foreground="#FD7E14" VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </TabItem>
            
            <!-- تبويب البحث الذكي -->
            <TabItem Header="🔍 البحث الذكي">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- خيارات البحث المتقدم -->
                    <GroupBox Grid.Row="0" Header="🎯 خيارات البحث المتقدم" Margin="0,0,0,15" Padding="15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع العنصر:" FontWeight="Bold" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <ComboBox Grid.Row="0" Grid.Column="1" x:Name="ItemTypeFilterComboBox" Height="30" 
                                      Margin="0,0,20,10">
                                <ComboBoxItem Content="جميع الأنواع"/>
                                <ComboBoxItem Content="الأجهزة الطبية"/>
                                <ComboBoxItem Content="العملاء"/>
                                <ComboBoxItem Content="المبيعات"/>
                                <ComboBoxItem Content="التنصيبات"/>
                                <ComboBoxItem Content="المخزون"/>
                                <ComboBoxItem Content="الموردين"/>
                            </ComboBox>
                            
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="الفئة:" FontWeight="Bold" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <ComboBox Grid.Row="0" Grid.Column="3" x:Name="CategoryFilterComboBox" Height="30" 
                                      Margin="0,0,20,10"/>
                            
                            <TextBlock Grid.Row="0" Grid.Column="4" Text="الحالة:" FontWeight="Bold" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <ComboBox Grid.Row="0" Grid.Column="5" x:Name="StatusFilterComboBox" Height="30" 
                                      Margin="0,0,0,10"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="من تاريخ:" FontWeight="Bold" 
                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <DatePicker Grid.Row="1" Grid.Column="1" x:Name="FromDatePicker" Height="30" 
                                        Margin="0,0,20,0"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="إلى تاريخ:" FontWeight="Bold" 
                                       VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <DatePicker Grid.Row="1" Grid.Column="3" x:Name="ToDatePicker" Height="30" 
                                        Margin="0,0,20,0"/>
                            
                            <Button Grid.Row="1" Grid.Column="4" Grid.ColumnSpan="2" x:Name="AdvancedSearchButton" 
                                    Content="🔍 بحث متقدم" Width="120" Height="30"
                                    Background="#6F42C1" Foreground="White" BorderThickness="0" 
                                    FontSize="12" FontWeight="SemiBold" Click="AdvancedSearchButton_Click"/>
                        </Grid>
                    </GroupBox>
                    
                    <!-- نتائج البحث -->
                    <DataGrid Grid.Row="1" x:Name="SearchResultsDataGrid" 
                              AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA" FontSize="12">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="النوع" Binding="{Binding ItemType}" Width="100"/>
                            <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="250"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="300"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="120"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                            <DataGridTextColumn Header="نقاط الصلة" Binding="{Binding RelevanceScore}" Width="80"/>
                            
                            <DataGridTemplateColumn Header="الإجراءات" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="👁️ عرض" Width="70" Height="25" Background="#17A2B8" 
                                                Foreground="White" BorderThickness="0" FontSize="10"
                                                Click="ViewSearchResultButton_Click"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                    
                    <!-- إحصائيات البحث -->
                    <TextBlock Grid.Row="2" x:Name="SearchResultsCountTextBlock" Text="" 
                               FontWeight="SemiBold" Margin="0,15,0,0" Foreground="#6C757D"/>
                </Grid>
            </TabItem>
            
            <!-- تبويب الإعدادات -->
            <TabItem Header="⚙️ إعدادات النظام الذكي">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- إعدادات التنبيهات -->
                        <GroupBox Grid.Row="0" Header="🔔 إعدادات التنبيهات" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <CheckBox Grid.Row="0" Grid.Column="0" x:Name="WarrantyAlertsCheckBox" 
                                          Content="تنبيهات انتهاء الضمان" IsChecked="True" Margin="0,0,0,10"/>
                                <TextBox Grid.Row="0" Grid.Column="1" x:Name="WarrantyDaysTextBox" 
                                         Text="30" Width="60" Height="25" Margin="10,0,10,10"/>
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="يوم قبل الانتهاء" 
                                           VerticalAlignment="Center" Margin="0,0,0,10"/>
                                
                                <CheckBox Grid.Row="1" Grid.Column="0" x:Name="MaintenanceAlertsCheckBox" 
                                          Content="تنبيهات الصيانة" IsChecked="True" Margin="0,0,0,10"/>
                                <TextBox Grid.Row="1" Grid.Column="1" x:Name="MaintenanceDaysTextBox" 
                                         Text="7" Width="60" Height="25" Margin="10,0,10,10"/>
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="يوم قبل الصيانة" 
                                           VerticalAlignment="Center" Margin="0,0,0,10"/>
                                
                                <CheckBox Grid.Row="2" Grid.Column="0" x:Name="InventoryAlertsCheckBox" 
                                          Content="تنبيهات المخزون المنخفض" IsChecked="True" Margin="0,0,0,10"/>
                                
                                <CheckBox Grid.Row="3" Grid.Column="0" x:Name="ExpiryAlertsCheckBox" 
                                          Content="تنبيهات انتهاء الصلاحية" IsChecked="True" Margin="0,0,0,10"/>
                                <TextBox Grid.Row="3" Grid.Column="1" x:Name="ExpiryDaysTextBox" 
                                         Text="30" Width="60" Height="25" Margin="10,0,10,10"/>
                                <TextBlock Grid.Row="3" Grid.Column="2" Text="يوم قبل انتهاء الصلاحية" 
                                           VerticalAlignment="Center" Margin="0,0,0,10"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات البحث -->
                        <GroupBox Grid.Row="1" Header="🔍 إعدادات البحث" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="عدد نتائج البحث الأقصى:" 
                                           FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,15,10"/>
                                <TextBox Grid.Row="0" Grid.Column="1" x:Name="MaxSearchResultsTextBox" 
                                         Text="50" Width="80" Height="25" Margin="0,0,0,10"/>
                                
                                <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" x:Name="AutoSuggestionsCheckBox" 
                                          Content="تفعيل الاقتراحات التلقائية" IsChecked="True" Margin="0,0,0,10"/>
                                
                                <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" x:Name="RebuildIndexButton" 
                                        Content="🔄 إعادة بناء فهرس البحث" Width="180" Height="35"
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        FontSize="12" FontWeight="SemiBold" Click="RebuildIndexButton_Click"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات الإحصائيات -->
                        <GroupBox Grid.Row="2" Header="📊 إعدادات الإحصائيات" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="تحديث الإحصائيات كل:" 
                                           FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,15,10"/>
                                <ComboBox Grid.Row="0" Grid.Column="1" x:Name="StatsUpdateIntervalComboBox" 
                                          Width="150" Height="25" Margin="0,0,0,10">
                                    <ComboBoxItem Content="5 دقائق"/>
                                    <ComboBoxItem Content="15 دقيقة" IsSelected="True"/>
                                    <ComboBoxItem Content="30 دقيقة"/>
                                    <ComboBoxItem Content="ساعة واحدة"/>
                                </ComboBox>
                                
                                <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" x:Name="RealTimeStatsCheckBox" 
                                          Content="تحديث الإحصائيات في الوقت الفعلي" IsChecked="True" Margin="0,0,0,10"/>
                                
                                <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" x:Name="RecalculateStatsButton" 
                                        Content="🔄 إعادة حساب جميع الإحصائيات" Width="200" Height="35"
                                        Background="#28A745" Foreground="White" BorderThickness="0" 
                                        FontSize="12" FontWeight="SemiBold" Click="RecalculateStatsButton_Click"/>
                            </Grid>
                        </GroupBox>
                    </Grid>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
