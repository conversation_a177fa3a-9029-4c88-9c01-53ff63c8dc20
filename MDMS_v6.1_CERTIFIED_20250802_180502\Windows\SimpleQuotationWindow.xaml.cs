using System;
using System.Windows;

namespace MedicalDevicesManager.Windows
{
    public partial class SimpleQuotationWindow : Window
    {
        public SimpleQuotationWindow()
        {
            InitializeComponent();
        }

        private void PrintQuotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("تم إنشاء تقرير العرض بنجاح!\n\n" +
                               "العروض المتاحة:\n" +
                               "🥉 شيلر AT-102: 85,500 ريال\n" +
                               "🥇 فيليبس PageWriter TC70: 124,200 ريال\n" +
                               "🥈 جنرال إلكتريك MAC 2000: 106,020 ريال\n\n" +
                               "سيتم طباعة العرض مع جميع التفاصيل والمواصفات.", 
                    "طباعة العرض", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EmailQuotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد إرسال العرض بالبريد الإلكتروني؟\n\n" +
                                           "سيتم إرسال العروض الثلاثة مع جميع التفاصيل:\n" +
                                           "• المواصفات التقنية\n" +
                                           "• الأسعار والخصومات\n" +
                                           "• المزايا الإضافية\n" +
                                           "• شروط التسليم والضمان", 
                    "إرسال العرض", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("تم إرسال العرض بالبريد الإلكتروني بنجاح!\n\n" +
                                   "تم إرسال العرض إلى العميل مع جميع التفاصيل.", 
                        "إرسال العرض", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveQuotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var quotationNumber = $"QUO-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
                
                MessageBox.Show($"تم حفظ العرض بنجاح!\n\n" +
                               $"رقم العرض: {quotationNumber}\n" +
                               $"تاريخ العرض: {DateTime.Now:yyyy/MM/dd}\n" +
                               $"عدد العروض: 3 عروض\n" +
                               $"إجمالي أقل سعر: 85,500 ريال\n" +
                               $"إجمالي أعلى سعر: 124,200 ريال\n\n" +
                               "تم حفظ العرض في النظام لمتابعة العميل.", 
                    "حفظ العرض", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
