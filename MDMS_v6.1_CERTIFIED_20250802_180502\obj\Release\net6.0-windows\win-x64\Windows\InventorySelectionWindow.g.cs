﻿#pragma checksum "..\..\..\..\..\Windows\InventorySelectionWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "501D0BCD83A10AB1E1CF850A6D6C7DB5A9A1B402"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// InventorySelectionWindow
    /// </summary>
    public partial class InventorySelectionWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 33 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersBtn;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InventoryDataGrid;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectBtn;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SelectedItemPanel;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedItemInfoTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/inventoryselectionwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 34 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 38 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            this.CategoryFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CategoryFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ClearFiltersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            this.ClearFiltersBtn.Click += new System.Windows.RoutedEventHandler(this.ClearFiltersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.InventoryDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 51 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            this.InventoryDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.InventoryDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            
            #line 52 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            this.InventoryDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InventoryDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SelectBtn = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            this.SelectBtn.Click += new System.Windows.RoutedEventHandler(this.SelectBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 85 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SelectedItemPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.SelectedItemInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 5:
            
            #line 69 "..\..\..\..\..\Windows\InventorySelectionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectItemBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

