using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditInstallationWindow : Window
    {
        private DeviceInstallation _installation;
        private bool _isEditMode;
        private List<MedicalDevice> _devices;
        private List<Customer> _customers;
        private MedicalDevice _selectedDevice;

        public AddEditInstallationWindow(DeviceInstallation installation = null)
        {
            InitializeComponent();
            _installation = installation;
            _isEditMode = installation != null;
            
            if (_isEditMode)
            {
                TitleTextBlock.Text = "🔧 تعديل تنصيب الجهاز";
                Title = "تعديل تنصيب الجهاز";
            }
            
            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // تحميل الأجهزة المتاحة
                _devices = await App.DatabaseContext.MedicalDevices
                    .Where(d => d.Status == "متاح" || (_isEditMode && d.Id == _installation.MedicalDeviceId))
                    .ToListAsync();

                // تحميل العملاء
                _customers = await App.DatabaseContext.Customers
                    .Where(c => c.Status == "نشط")
                    .ToListAsync();
                
                CustomerComboBox.ItemsSource = _customers;
                CustomerComboBox.DisplayMemberPath = "Name";
                CustomerComboBox.SelectedValuePath = "Id";

                // تعيين القيم الافتراضية
                InstallationDatePicker.SelectedDate = DateTime.Now;
                WarrantyYearsTextBox.Text = "1";
                InstallationStatusComboBox.SelectedIndex = 0; // مكتمل
                EquipmentConditionComboBox.SelectedIndex = 0; // ممتاز

                // إذا كان في وضع التعديل، تحميل البيانات
                if (_isEditMode && _installation != null)
                {
                    LoadInstallationData();
                    await LoadSelectedDeviceAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadInstallationData()
        {
            if (_installation == null) return;
            CustomerComboBox.SelectedValue = _installation.CustomerId;
            InstallationDatePicker.SelectedDate = _installation.InstallationDate;
            WarrantyYearsTextBox.Text = _installation.WarrantyYears.ToString();
            WarrantyEndDatePicker.SelectedDate = _installation.WarrantyEndDate;
            InstallationLocationTextBox.Text = _installation.InstallationLocation;
            ContactPersonNameTextBox.Text = _installation.ContactPersonName;
            ContactPersonPhoneTextBox.Text = _installation.ContactPersonPhone;
            ContactPersonEmailTextBox.Text = _installation.ContactPersonEmail;
            ContactPersonPositionTextBox.Text = _installation.ContactPersonPosition;
            InstallationCostTextBox.Text = _installation.InstallationCost.ToString();

            // تحميل كتيب التقرير
            if (!string.IsNullOrEmpty(_installation.InstallationReportBookletPath))
            {
                InstallationReportPathTextBox.Text = Path.GetFileName(_installation.InstallationReportBookletPath);
                InstallationReportPathTextBox.Tag = _installation.InstallationReportBookletPath;
                ViewInstallationReportBtn.IsEnabled = File.Exists(_installation.InstallationReportBookletPath);
                RemoveInstallationReportBtn.IsEnabled = true;
            }

            // تم إزالة هذا الجزء لأننا نستخدم نظام اختيار الجهاز الجديد
            TechnicianNameTextBox.Text = _installation.TechnicianName;
            InstallationNotesTextBox.Text = _installation.InstallationNotes;
            SupplierCompanyTextBox.Text = _installation.SupplierCompany;
            ModelTextBox.Text = _installation.Model ?? "";
            ManufacturerTextBox.Text = _installation.Manufacturer ?? "";

            // تعيين حالة التنصيب
            foreach (ComboBoxItem item in InstallationStatusComboBox.Items)
            {
                if (item.Content.ToString() == _installation.InstallationStatus)
                {
                    InstallationStatusComboBox.SelectedItem = item;
                    break;
                }
            }

            // تعيين حالة المعدات
            foreach (ComboBoxItem item in EquipmentConditionComboBox.Items)
            {
                if (item.Content.ToString() == _installation.EquipmentCondition)
                {
                    EquipmentConditionComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        // تم حذف DeviceComboBox_SelectionChanged لأننا نستخدم نظام اختيار الجهاز الجديد

        // تم حذف LoadSerialNumbersForDevice لأننا نستخدم نظام اختيار الجهاز الجديد

        // تم حذف SerialNumberComboBox_SelectionChanged لأننا نستخدم نظام اختيار الجهاز الجديد

        private void WarrantyYearsTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (int.TryParse(WarrantyYearsTextBox.Text, out int years) &&
                InstallationDatePicker.SelectedDate.HasValue)
            {
                WarrantyEndDatePicker.SelectedDate = InstallationDatePicker.SelectedDate.Value.AddYears(years);
            }
        }

        private void WarrantyEndDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق للتحقق من صحة تاريخ انتهاء الضمان
            if (WarrantyEndDatePicker.SelectedDate.HasValue && InstallationDatePicker.SelectedDate.HasValue)
            {
                if (WarrantyEndDatePicker.SelectedDate < InstallationDatePicker.SelectedDate)
                {
                    MessageBox.Show("تاريخ انتهاء الضمان لا يمكن أن يكون قبل تاريخ التنصيب", "خطأ في التاريخ",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    WarrantyEndDatePicker.SelectedDate = InstallationDatePicker.SelectedDate.Value.AddYears(1);
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                var selectedCustomer = CustomerComboBox.SelectedItem as Customer;

                if (_isEditMode)
                {
                    // تحديث التنصيب الموجود
                    _installation.MedicalDeviceId = _selectedDevice.Id;
                    _installation.DeviceName = _selectedDevice.Name;
                    _installation.SerialNumber = _selectedDevice.SerialNumber;
                    _installation.Model = ModelTextBox.Text.Trim();
                    _installation.Manufacturer = ManufacturerTextBox.Text.Trim();
                    _installation.CustomerId = selectedCustomer.Id;
                    _installation.CustomerName = selectedCustomer.Name;
                    _installation.InstallationDate = InstallationDatePicker.SelectedDate.Value;
                    _installation.WarrantyYears = int.Parse(WarrantyYearsTextBox.Text);
                    _installation.WarrantyEndDate = WarrantyEndDatePicker.SelectedDate.Value;
                    _installation.InstallationLocation = InstallationLocationTextBox.Text;
                    _installation.ContactPersonName = ContactPersonNameTextBox.Text;
                    _installation.ContactPersonPhone = ContactPersonPhoneTextBox.Text;
                    _installation.ContactPersonEmail = ContactPersonEmailTextBox.Text;
                    _installation.ContactPersonPosition = ContactPersonPositionTextBox.Text;
                    _installation.InstallationCost = decimal.Parse(InstallationCostTextBox.Text);
                    _installation.InstallationStatus = ((ComboBoxItem)InstallationStatusComboBox.SelectedItem).Content.ToString();
                    _installation.TechnicianName = TechnicianNameTextBox.Text;
                    _installation.InstallationNotes = InstallationNotesTextBox.Text;
                    _installation.EquipmentCondition = ((ComboBoxItem)EquipmentConditionComboBox.SelectedItem).Content.ToString();
                    _installation.SupplierCompany = SupplierCompanyTextBox.Text;

                    // حفظ مسار كتيب التقرير
                    if (InstallationReportPathTextBox.Text != "لم يتم اختيار ملف")
                    {
                        _installation.InstallationReportBookletPath = InstallationReportPathTextBox.Tag?.ToString() ?? "";
                    }

                    _installation.LastUpdated = DateTime.Now;

                    App.DatabaseContext.DeviceInstallations.Update(_installation);
                }
                else
                {
                    // إنشاء تنصيب جديد
                    var newInstallation = new DeviceInstallation
                    {
                        MedicalDeviceId = _selectedDevice.Id,
                        DeviceName = _selectedDevice.Name,
                        SerialNumber = _selectedDevice.SerialNumber,
                        Model = ModelTextBox.Text.Trim(),
                        Manufacturer = ManufacturerTextBox.Text.Trim(),
                        CustomerId = selectedCustomer.Id,
                        CustomerName = selectedCustomer.Name,
                        InstallationDate = InstallationDatePicker.SelectedDate.Value,
                        WarrantyYears = int.Parse(WarrantyYearsTextBox.Text),
                        WarrantyEndDate = WarrantyEndDatePicker.SelectedDate.Value,
                        InstallationLocation = InstallationLocationTextBox.Text,
                        ContactPersonName = ContactPersonNameTextBox.Text,
                        ContactPersonPhone = ContactPersonPhoneTextBox.Text,
                        ContactPersonEmail = ContactPersonEmailTextBox.Text,
                        ContactPersonPosition = ContactPersonPositionTextBox.Text,
                        InstallationCost = decimal.Parse(InstallationCostTextBox.Text),
                        InstallationStatus = ((ComboBoxItem)InstallationStatusComboBox.SelectedItem).Content.ToString(),
                        TechnicianName = TechnicianNameTextBox.Text,
                        InstallationNotes = InstallationNotesTextBox.Text,
                        EquipmentCondition = ((ComboBoxItem)EquipmentConditionComboBox.SelectedItem).Content.ToString(),
                        SupplierCompany = SupplierCompanyTextBox.Text,
                        InstallationReportBookletPath = InstallationReportPathTextBox.Text != "لم يتم اختيار ملف" ?
                                                       InstallationReportPathTextBox.Tag?.ToString() ?? "" : "",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };

                    App.DatabaseContext.DeviceInstallations.Add(newInstallation);
                }

                await App.DatabaseContext.SaveChangesAsync();

                MessageBox.Show(
                    _isEditMode ? "تم تحديث التنصيب بنجاح!" : "تم إضافة التنصيب بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التنصيب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (_selectedDevice == null)
            {
                MessageBox.Show("يرجى اختيار الجهاز الطبي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SelectDeviceButton.Focus();
                return false;
            }

            if (CustomerComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CustomerComboBox.Focus();
                return false;
            }

            if (!InstallationDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد تاريخ التنصيب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                InstallationDatePicker.Focus();
                return false;
            }

            if (!int.TryParse(WarrantyYearsTextBox.Text, out int warrantyYears) || warrantyYears < 0)
            {
                MessageBox.Show("يرجى إدخال عدد سنوات الضمان بشكل صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarrantyYearsTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(InstallationCostTextBox.Text, out decimal cost) || cost < 0)
            {
                MessageBox.Show("يرجى إدخال تكلفة التنصيب بشكل صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                InstallationCostTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(InstallationLocationTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال موقع التنصيب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                InstallationLocationTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BrowseInstallationReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Title = "اختيار كتيب تقرير التنصيب",
                    Filter = "PDF Files|*.pdf|Word Documents|*.doc;*.docx|Image Files|*.jpg;*.jpeg;*.png;*.bmp|All Files|*.*",
                    FilterIndex = 1
                };

                if (openDialog.ShowDialog() == true)
                {
                    var fileName = openDialog.FileName;
                    var fileInfo = new FileInfo(fileName);

                    // التحقق من حجم الملف (أقل من 10 ميجابايت)
                    if (fileInfo.Length > 10 * 1024 * 1024)
                    {
                        MessageBox.Show("حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 10 ميجابايت.",
                                      "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // نسخ الملف إلى مجلد التطبيق
                    var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                                  "MedicalDevicesManager", "InstallationReports");
                    Directory.CreateDirectory(appDataPath);

                    var newFileName = $"Installation_{DateTime.Now:yyyyMMdd_HHmmss}_{Path.GetFileName(fileName)}";
                    var destinationPath = Path.Combine(appDataPath, newFileName);

                    File.Copy(fileName, destinationPath, true);

                    InstallationReportPathTextBox.Text = Path.GetFileName(destinationPath);
                    InstallationReportPathTextBox.Tag = destinationPath;
                    ViewInstallationReportBtn.IsEnabled = true;
                    RemoveInstallationReportBtn.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewInstallationReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = InstallationReportPathTextBox.Tag?.ToString();
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ResetInstallationReportBooklet();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveInstallationReportBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد حذف كتيب التقرير؟", "تأكيد الحذف",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var filePath = InstallationReportPathTextBox.Tag?.ToString();
                    if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
                catch (Exception ex)
                {
                    // تجاهل أخطاء حذف الملف
                    System.Diagnostics.Debug.WriteLine($"Error deleting file: {ex.Message}");
                }

                ResetInstallationReportBooklet();
            }
        }

        private void ResetInstallationReportBooklet()
        {
            InstallationReportPathTextBox.Text = "لم يتم اختيار ملف";
            InstallationReportPathTextBox.Tag = null;
            ViewInstallationReportBtn.IsEnabled = false;
            RemoveInstallationReportBtn.IsEnabled = false;
        }

        private void SelectDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectDeviceWindow = new SelectDeviceWindow();
                if (selectDeviceWindow.ShowDialog() == true)
                {
                    _selectedDevice = selectDeviceWindow.SelectedDevice;
                    if (_selectedDevice != null)
                    {
                        SelectedDeviceTextBox.Text = $"{_selectedDevice.SerialNumber} - {_selectedDevice.Name}";

                        // تحميل القيم الافتراضية في الخلايا القابلة للتعديل (إذا كانت فارغة)
                        if (string.IsNullOrWhiteSpace(ModelTextBox.Text))
                        {
                            ModelTextBox.Text = _selectedDevice.Model ?? "";
                        }
                        if (string.IsNullOrWhiteSpace(ManufacturerTextBox.Text))
                        {
                            ManufacturerTextBox.Text = _selectedDevice.Brand ?? "";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة اختيار الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSelectedDeviceAsync()
        {
            try
            {
                if (_installation?.MedicalDeviceId != null)
                {
                    _selectedDevice = await App.DatabaseContext.MedicalDevices
                        .FirstOrDefaultAsync(d => d.Id == _installation.MedicalDeviceId);

                    if (_selectedDevice != null)
                    {
                        SelectedDeviceTextBox.Text = $"{_selectedDevice.SerialNumber} - {_selectedDevice.Name}";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
