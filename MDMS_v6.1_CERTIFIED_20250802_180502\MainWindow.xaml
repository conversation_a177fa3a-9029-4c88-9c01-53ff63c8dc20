<Window x:Class="MedicalDevicesManager.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام إدارة الأجهزة الطبية المتكامل v6.0 - Medical Devices Management System Integrated"
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"
        FontSize="14"
        Background="#FAFAFA">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernSidebarButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#2D3748"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,16"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontFamily" Value="Segoe UI Variable, Segoe UI, Tahoma, Arial"/>
            <Setter Property="Margin" Value="8,2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Margin="0">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F1F5F9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E2E8F0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Active Button Style -->
        <Style x:Key="ActiveSidebarButton" TargetType="Button" BasedOn="{StaticResource ModernSidebarButton}">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2563EB"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1D4ED8"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان المحدث -->
        <Border Grid.Row="0" Background="White"
                BorderBrush="#E5E7EB" BorderThickness="0,0,0,1">
            <Border.Effect>
                <DropShadowEffect Color="#000000" Opacity="0.05"
                                BlurRadius="8" ShadowDepth="2" Direction="270"/>
            </Border.Effect>
            <Grid Margin="32,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#3B82F6" CornerRadius="12" Width="48" Height="48" Margin="0,0,20,0">
                        <TextBlock Text="🏥" FontSize="24" Foreground="White"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="نظام إدارة الأجهزة الطبية المتكامل"
                                  FontSize="20" FontWeight="SemiBold" Foreground="#1F2937"
                                  FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"/>
                        <TextBlock Text="Medical Devices Management System v6.1 - Certified Edition"
                                  FontSize="12" Foreground="#6B7280"
                                  FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"
                                  Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#F3F4F6" CornerRadius="20" Padding="16,8" Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <Border Background="#10B981" CornerRadius="10" Width="20" Height="20" Margin="0,0,8,0">
                                <TextBlock Text="👤" FontSize="10" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="المدير العام"
                                      Foreground="#374151" FontSize="13" FontWeight="Medium"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    <TextBlock x:Name="DateTimeBlock"
                              Foreground="#6B7280" FontSize="12" VerticalAlignment="Center"
                              FontFamily="Segoe UI Variable, Segoe UI"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي المحدث -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- القائمة الجانبية المحدثة -->
            <Border Grid.Column="0" Background="White"
                    BorderBrush="#E5E7EB" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                    <StackPanel Margin="16,24,16,24">

                        <!-- القائمة الرئيسية -->
                        <TextBlock Text="القائمة الرئيسية"
                                  Foreground="#6B7280" FontSize="11" FontWeight="SemiBold"
                                  Margin="12,0,0,12"
                                  FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"/>

                        <Button x:Name="DashboardBtn" Content="🏠 لوحة التحكم"
                                Style="{StaticResource ActiveSidebarButton}"
                                Click="DashboardBtn_Click"/>

                        <Button x:Name="DevicesBtn" Content="🏥 الأجهزة الطبية"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="DevicesBtn_Click"/>

                        <Button x:Name="InventoryBtn" Content="📦 إدارة المخزون"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="InventoryBtn_Click"/>

                        <Button x:Name="SalesBtn" Content="💰 إدارة المبيعات"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="SalesBtn_Click"/>

                        <Button x:Name="QuotationsBtn" Content="📋 عروض الأسعار"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="QuotationsBtn_Click"/>

                        <Button x:Name="CustomersBtn" Content="👥 إدارة العملاء"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="CustomersBtn_Click"/>

                        <Button x:Name="SuppliersBtn" Content="🏭 إدارة الموردين"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="SuppliersBtn_Click"/>

                        <!-- فاصل أنيق -->
                        <Border Height="1" Background="#E5E7EB" Margin="12,20,12,20" CornerRadius="0.5"/>

                        <!-- العمليات -->
                        <TextBlock Text="العمليات"
                                  Foreground="#6B7280" FontSize="11" FontWeight="SemiBold"
                                  Margin="12,0,0,12"
                                  FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"/>

                        <Button x:Name="ShipmentsBtn" Content="🚚 إدارة الشحنات"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="ShipmentsBtn_Click"/>

                        <Button x:Name="MaintenanceBtn" Content="🛠️ الضمان والصيانة"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="MaintenanceBtn_Click"/>

                        <Button x:Name="InstallationsBtn" Content="🔧 إدارة التنصيبات"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="InstallationsBtn_Click"/>

                        <Button x:Name="SparePartsBtn" Content="🔧 قطع الغيار والملحقات"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="SparePartsBtn_Click"/>

                        <!-- فاصل أنيق -->
                        <Border Height="1" Background="#E5E7EB" Margin="12,20,12,20" CornerRadius="0.5"/>

                        <!-- الأدوات -->
                        <TextBlock Text="الأدوات والتقارير"
                                  Foreground="#6B7280" FontSize="11" FontWeight="SemiBold"
                                  Margin="12,0,0,12"
                                  FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"/>

                        <Button x:Name="ReportsBtn" Content="📊 التقارير"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="ReportsBtn_Click"/>

                        <Button x:Name="SettingsBtn" Content="⚙️ الإعدادات"
                                Style="{StaticResource ModernSidebarButton}"
                                Click="SettingsBtn_Click"/>

                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- منطقة المحتوى المحدثة -->
            <Border Grid.Column="1" Background="#FAFAFA">
                <ScrollViewer x:Name="ContentArea"
                             Padding="32"
                             VerticalScrollBarVisibility="Auto"
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled"/>
            </Border>

        </Grid>

        <!-- شريط الحالة المحدث -->
        <Border Grid.Row="2" Background="White"
                BorderBrush="#E5E7EB" BorderThickness="0,1,0,0">
            <Grid Margin="32,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#10B981" CornerRadius="6" Width="12" Height="12" Margin="0,0,8,0"/>
                    <TextBlock Text="جاهز - النظام يعمل بكفاءة"
                              FontSize="12" Foreground="#374151" VerticalAlignment="Center"
                              FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#F3F4F6" CornerRadius="12" Padding="8,4" Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <Border Background="#10B981" CornerRadius="4" Width="8" Height="8" Margin="0,0,6,0"/>
                            <TextBlock Text="قاعدة البيانات: متصلة"
                                      FontSize="11" Foreground="#059669" FontWeight="Medium"
                                      FontFamily="Segoe UI Variable, Segoe UI"/>
                        </StackPanel>
                    </Border>
                    <Border Background="#F3F4F6" CornerRadius="12" Padding="8,4">
                        <TextBlock Text="الإصدار: 6.1"
                                  FontSize="11" Foreground="#6B7280" FontWeight="Medium"
                                  FontFamily="Segoe UI Variable, Segoe UI"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
