﻿#pragma checksum "..\..\..\..\Windows\ManageCitiesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E8E55B99F893781D9840A3A6972320697244B334"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// ManageCitiesWindow
    /// </summary>
    public partial class ManageCitiesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 33 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCityBtn;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CountryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CitiesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CityNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CountryComboBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveCityBtn;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/managecitieswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddCityBtn = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            this.AddCityBtn.Click += new System.Windows.RoutedEventHandler(this.AddCityBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CountryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 43 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            this.CountryFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CountryFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CitiesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 7:
            this.CityNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.CountryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.SaveCityBtn = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            this.SaveCityBtn.Click += new System.Windows.RoutedEventHandler(this.SaveCityBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 5:
            
            #line 66 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCityBtn_Click);
            
            #line default
            #line hidden
            break;
            case 6:
            
            #line 70 "..\..\..\..\Windows\ManageCitiesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteCityBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

