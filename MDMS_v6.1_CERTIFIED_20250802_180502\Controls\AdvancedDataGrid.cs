using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.Win32;
using System.IO;
using System.Text;
using OfficeOpenXml;
using iText.Kernel.Pdf;
using System.Globalization;
using System.Reflection;
using iText.Layout;
using iText.Layout.Element;

namespace MedicalDevicesManager.Controls
{
    /// <summary>
    /// جدول بيانات متقدم مع مميزات شاملة
    /// </summary>
    public partial class AdvancedDataGrid : UserControl
    {
        #region Properties

        // الخصائص الأساسية
        public bool EnablePagination { get; set; } = true;
        public int PageSize { get; set; } = 50;
        public bool EnableAdvancedSearch { get; set; } = true;
        public bool EnableExport { get; set; } = true;
        public bool EnablePrint { get; set; } = true;
        public bool EnableColumnFreeze { get; set; } = true;
        public bool EnableRowNumbers { get; set; } = true;
        public bool EnableMultiSelect { get; set; } = true;
        public bool EnableQuickPreview { get; set; } = true;
        public bool EnableColumnCustomization { get; set; } = true;
        public bool EnableAdvancedFiltering { get; set; } = true;
        public bool EnableAutoFitColumns { get; set; } = true;
        public bool EnableKeyboardShortcuts { get; set; } = true;

        // البيانات
        // private ICollectionView _collectionView; // غير مستخدم حالياً
        private ObservableCollection<object> _originalData;
        private ObservableCollection<object> _filteredData;

        // عناصر التحكم
        private DataGrid _mainDataGrid;
        private StackPanel _toolbarPanel;
        private StackPanel _searchPanel;
        private StackPanel _paginationPanel;
        private TextBox _globalSearchBox;
        private ComboBox _pageSizeComboBox;
        private TextBlock _recordsInfoTextBlock;
        private Button _firstPageButton;
        private Button _previousPageButton;
        private Button _nextPageButton;
        private Button _lastPageButton;
        private TextBox _currentPageTextBox;
        private TextBlock _totalPagesTextBlock;

        // معلومات الصفحات
        private int _currentPage = 1;
        private int _totalPages = 1;
        private int _totalRecords = 0;

        // الفلاتر المتقدمة
        private Dictionary<string, object> _activeFilters = new Dictionary<string, object>();

        #endregion

        #region Events

        public event EventHandler<object> RowDoubleClick;
        public event EventHandler<object> RowSelected;
        public event EventHandler<List<object>> MultipleRowsSelected;
        public event EventHandler DataRefreshRequested;
        public event EventHandler<object> DeleteRequested;
        public event EventHandler<object> EditRequested;
        public event EventHandler<object> ViewRequested;

        #endregion

        #region Constructor

        public AdvancedDataGrid()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        #endregion

        #region Initialization

        private void InitializeComponent()
        {
            // إعداد التخطيط الرئيسي
            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // شريط الأدوات
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // البحث
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // الجدول
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // التنقل بين الصفحات

            // إنشاء شريط الأدوات
            CreateToolbar();
            Grid.SetRow(_toolbarPanel, 0);
            mainGrid.Children.Add(_toolbarPanel);

            // إنشاء لوحة البحث
            CreateSearchPanel();
            Grid.SetRow(_searchPanel, 1);
            mainGrid.Children.Add(_searchPanel);

            // إنشاء الجدول الرئيسي
            CreateMainDataGrid();
            Grid.SetRow(_mainDataGrid, 2);
            mainGrid.Children.Add(_mainDataGrid);

            // إنشاء لوحة التنقل
            CreatePaginationPanel();
            Grid.SetRow(_paginationPanel, 3);
            mainGrid.Children.Add(_paginationPanel);

            Content = mainGrid;
        }

        private void CreateToolbar()
        {
            _toolbarPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                Margin = new Thickness(0, 0, 0, 10),
                Height = 50
            };

            // زر التحديث
            var refreshButton = CreateToolbarButton("🔄", "تحديث البيانات", RefreshData_Click);
            _toolbarPanel.Children.Add(refreshButton);

            // فاصل
            _toolbarPanel.Children.Add(CreateSeparator());

            // أزرار التصدير
            if (EnableExport)
            {
                var exportExcelButton = CreateToolbarButton("📊", "تصدير إلى Excel", ExportToExcel_Click);
                var exportPdfButton = CreateToolbarButton("📄", "تصدير إلى PDF", ExportToPdf_Click);
                var exportCsvButton = CreateToolbarButton("📋", "تصدير إلى CSV", ExportToCsv_Click);

                _toolbarPanel.Children.Add(exportExcelButton);
                _toolbarPanel.Children.Add(exportPdfButton);
                _toolbarPanel.Children.Add(exportCsvButton);
                _toolbarPanel.Children.Add(CreateSeparator());
            }

            // زر الطباعة
            if (EnablePrint)
            {
                var printButton = CreateToolbarButton("🖨️", "طباعة", Print_Click);
                _toolbarPanel.Children.Add(printButton);
                _toolbarPanel.Children.Add(CreateSeparator());
            }

            // زر تخصيص الأعمدة
            if (EnableColumnCustomization)
            {
                var customizeButton = CreateToolbarButton("⚙️", "تخصيص الأعمدة", CustomizeColumns_Click);
                _toolbarPanel.Children.Add(customizeButton);
                _toolbarPanel.Children.Add(CreateSeparator());
            }

            // زر Auto-fit للأعمدة
            if (EnableAutoFitColumns)
            {
                var autoFitButton = CreateToolbarButton("📏", "ملائمة الأعمدة تلقائياً", AutoFitColumns_Click);
                _toolbarPanel.Children.Add(autoFitButton);
                _toolbarPanel.Children.Add(CreateSeparator());
            }

            // أزرار الإجراءات مع دعم لوحة المفاتيح
            if (EnableKeyboardShortcuts)
            {
                var deleteButton = CreateToolbarButton("🗑️", "حذف المحدد (P)", DeleteSelected_Click);
                deleteButton.Background = new SolidColorBrush(Color.FromRgb(220, 53, 69));
                _toolbarPanel.Children.Add(deleteButton);

                var editButton = CreateToolbarButton("✏️", "تعديل المحدد (E)", EditSelected_Click);
                editButton.Background = new SolidColorBrush(Color.FromRgb(255, 193, 7));
                _toolbarPanel.Children.Add(editButton);

                var viewButton = CreateToolbarButton("👁️", "عرض المحدد (V)", ViewSelected_Click);
                viewButton.Background = new SolidColorBrush(Color.FromRgb(23, 162, 184));
                _toolbarPanel.Children.Add(viewButton);

                _toolbarPanel.Children.Add(CreateSeparator());
            }

            // معلومات السجلات
            _recordsInfoTextBlock = new TextBlock
            {
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 0, 0),
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            _toolbarPanel.Children.Add(_recordsInfoTextBlock);

            // إضافة تلميح لوحة المفاتيح
            if (EnableKeyboardShortcuts)
            {
                var keyboardHintTextBlock = new TextBlock
                {
                    Text = "💡 P=حذف | E=تعديل | V=عرض | F5=تحديث | Ctrl+F=بحث",
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(20, 0, 0, 0),
                    FontSize = 10,
                    FontStyle = FontStyles.Italic,
                    Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
                };
                _toolbarPanel.Children.Add(keyboardHintTextBlock);
            }
        }

        private void CreateSearchPanel()
        {
            _searchPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(Color.FromRgb(255, 255, 255)),
                Margin = new Thickness(0, 0, 0, 10),
                Height = 50
            };

            // أيقونة البحث
            var searchIcon = new TextBlock
            {
                Text = "🔍",
                FontSize = 16,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0)
            };
            _searchPanel.Children.Add(searchIcon);

            // مربع البحث العام
            _globalSearchBox = new TextBox
            {
                Width = 300,
                Height = 35,
                Padding = new Thickness(10, 5, 10, 5),
                FontSize = 14,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            _globalSearchBox.SetValue(TextBox.TextProperty, "");
            _globalSearchBox.SetValue(TextBox.TagProperty, "البحث في جميع الحقول...");
            _searchPanel.Children.Add(_globalSearchBox);

            // زر مسح البحث
            var clearSearchButton = CreateToolbarButton("❌", "مسح البحث", ClearSearch_Click);
            _searchPanel.Children.Add(clearSearchButton);

            // فاصل
            _searchPanel.Children.Add(CreateSeparator());

            // زر الفلترة المتقدمة
            if (EnableAdvancedFiltering)
            {
                var advancedFilterButton = CreateToolbarButton("🔧", "فلترة متقدمة", ShowAdvancedFilter_Click);
                _searchPanel.Children.Add(advancedFilterButton);
            }
        }

        private void CreateMainDataGrid()
        {
            _mainDataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                IsReadOnly = true,
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                SelectionMode = EnableMultiSelect ? DataGridSelectionMode.Extended : DataGridSelectionMode.Single,
                SelectionUnit = DataGridSelectionUnit.FullRow,
                RowHeight = 35,
                FontSize = 14,
                FontFamily = new FontFamily("Segoe UI, Tahoma, Arial"),
                FlowDirection = FlowDirection.RightToLeft,
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1)
            };

            // إضافة عمود الأرقام إذا كان مفعلاً
            if (EnableRowNumbers)
            {
                AddRowNumberColumn();
            }

            // إضافة دعم لوحة المفاتيح
            _mainDataGrid.KeyDown += MainDataGrid_KeyDown;
            _mainDataGrid.PreviewKeyDown += MainDataGrid_PreviewKeyDown;
        }

        private void CreatePaginationPanel()
        {
            _paginationPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                Height = 50,
                Margin = new Thickness(0, 10, 0, 0)
            };

            if (!EnablePagination) return;

            // أزرار التنقل
            _firstPageButton = CreatePaginationButton("⏮️", "الصفحة الأولى", FirstPage_Click);
            _previousPageButton = CreatePaginationButton("◀️", "الصفحة السابقة", PreviousPage_Click);

            // معلومات الصفحة الحالية
            _currentPageTextBox = new TextBox
            {
                Width = 50,
                Height = 30,
                TextAlignment = TextAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 5, 0)
            };

            var ofLabel = new TextBlock
            {
                Text = " من ",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 5, 0)
            };

            _totalPagesTextBlock = new TextBlock
            {
                VerticalAlignment = VerticalAlignment.Center,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 10, 0)
            };

            _nextPageButton = CreatePaginationButton("▶️", "الصفحة التالية", NextPage_Click);
            _lastPageButton = CreatePaginationButton("⏭️", "الصفحة الأخيرة", LastPage_Click);

            // حجم الصفحة
            var pageSizeLabel = new TextBlock
            {
                Text = " عدد السجلات: ",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 5, 0)
            };

            _pageSizeComboBox = new ComboBox
            {
                Width = 80,
                Height = 30,
                VerticalAlignment = VerticalAlignment.Center
            };
            _pageSizeComboBox.Items.Add(25);
            _pageSizeComboBox.Items.Add(50);
            _pageSizeComboBox.Items.Add(100);
            _pageSizeComboBox.Items.Add(200);
            _pageSizeComboBox.SelectedItem = PageSize;

            // إضافة العناصر للوحة
            _paginationPanel.Children.Add(_firstPageButton);
            _paginationPanel.Children.Add(_previousPageButton);
            _paginationPanel.Children.Add(_currentPageTextBox);
            _paginationPanel.Children.Add(ofLabel);
            _paginationPanel.Children.Add(_totalPagesTextBlock);
            _paginationPanel.Children.Add(_nextPageButton);
            _paginationPanel.Children.Add(_lastPageButton);
            _paginationPanel.Children.Add(pageSizeLabel);
            _paginationPanel.Children.Add(_pageSizeComboBox);
        }

        #endregion

        #region Helper Methods

        private Button CreateToolbarButton(string content, string tooltip, RoutedEventHandler clickHandler)
        {
            var button = new Button
            {
                Content = content,
                Width = 40,
                Height = 35,
                Margin = new Thickness(2, 0, 2, 0),
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand,
                ToolTip = tooltip,
                FontSize = 14
            };

            button.Click += clickHandler;
            return button;
        }

        private Button CreatePaginationButton(string content, string tooltip, RoutedEventHandler clickHandler)
        {
            var button = new Button
            {
                Content = content,
                Width = 35,
                Height = 30,
                Margin = new Thickness(2, 0, 2, 0),
                Background = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand,
                ToolTip = tooltip
            };

            button.Click += clickHandler;
            return button;
        }

        private Border CreateSeparator()
        {
            return new Border
            {
                Width = 1,
                Height = 25,
                Background = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                Margin = new Thickness(5, 0, 5, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
        }

        private void AddRowNumberColumn()
        {
            var rowNumberColumn = new DataGridTextColumn
            {
                Header = "#",
                Width = 50,
                IsReadOnly = true,
                CanUserSort = false,
                CanUserResize = false
            };

            var binding = new Binding();
            binding.Converter = new RowNumberConverter();
            rowNumberColumn.Binding = binding;

            _mainDataGrid.Columns.Insert(0, rowNumberColumn);
        }

        #endregion

        #region Event Handlers

        private void SetupEventHandlers()
        {
            // دعم لوحة المفاتيح
            if (EnableKeyboardShortcuts)
            {
                this.KeyDown += AdvancedDataGrid_KeyDown;
                this.Focusable = true;
            }

            // أحداث البحث
            if (_globalSearchBox != null)
            {
                _globalSearchBox.TextChanged += GlobalSearch_TextChanged;
            }

            // أحداث التنقل بين الصفحات
            if (_currentPageTextBox != null)
            {
                _currentPageTextBox.KeyDown += CurrentPageTextBox_KeyDown;
            }

            if (_pageSizeComboBox != null)
            {
                _pageSizeComboBox.SelectionChanged += PageSizeComboBox_SelectionChanged;
            }

            // أحداث الجدول
            if (_mainDataGrid != null)
            {
                _mainDataGrid.MouseDoubleClick += MainDataGrid_MouseDoubleClick;
                _mainDataGrid.SelectionChanged += MainDataGrid_SelectionChanged;
            }
        }

        private void GlobalSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CurrentPageTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (int.TryParse(_currentPageTextBox.Text, out int page))
                {
                    if (page >= 1 && page <= _totalPages)
                    {
                        _currentPage = page;
                        LoadCurrentPage();
                    }
                }
            }
        }

        private void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_pageSizeComboBox.SelectedItem != null)
            {
                PageSize = (int)_pageSizeComboBox.SelectedItem;
                _currentPage = 1;
                CalculatePagination();
                LoadCurrentPage();
            }
        }

        private void MainDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (_mainDataGrid.SelectedItem != null)
            {
                RowDoubleClick?.Invoke(this, _mainDataGrid.SelectedItem);
            }
        }

        private void MainDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (EnableMultiSelect && _mainDataGrid.SelectedItems.Count > 1)
            {
                var selectedItems = _mainDataGrid.SelectedItems.Cast<object>().ToList();
                MultipleRowsSelected?.Invoke(this, selectedItems);
            }
            else if (_mainDataGrid.SelectedItem != null)
            {
                RowSelected?.Invoke(this, _mainDataGrid.SelectedItem);
            }
        }

        // أحداث أزرار شريط الأدوات
        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            DataRefreshRequested?.Invoke(this, EventArgs.Empty);
        }

        private void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            ExportToExcel();
        }

        private void ExportToPdf_Click(object sender, RoutedEventArgs e)
        {
            ExportToPdf();
        }

        private void ExportToCsv_Click(object sender, RoutedEventArgs e)
        {
            ExportToCsv();
        }

        private void Print_Click(object sender, RoutedEventArgs e)
        {
            PrintData();
        }

        private void CustomizeColumns_Click(object sender, RoutedEventArgs e)
        {
            ShowColumnCustomizationDialog();
        }

        private void AutoFitColumns_Click(object sender, RoutedEventArgs e)
        {
            AutoFitColumns();
        }

        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            _globalSearchBox.Text = "";
            _activeFilters.Clear();
            ApplyFilters();
        }

        private void ShowAdvancedFilter_Click(object sender, RoutedEventArgs e)
        {
            ShowAdvancedFilterDialog();
        }

        // معالجات أزرار الإجراءات الجديدة
        private void DeleteSelected_Click(object sender, RoutedEventArgs e)
        {
            DeleteSelectedItem();
        }

        private void EditSelected_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = _mainDataGrid?.SelectedItem;
            if (selectedItem != null)
            {
                EditRequested?.Invoke(this, selectedItem);
            }
        }

        private void ViewSelected_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = _mainDataGrid?.SelectedItem;
            if (selectedItem != null)
            {
                ViewRequested?.Invoke(this, selectedItem);
            }
        }

        // أحداث التنقل بين الصفحات
        private void FirstPage_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            LoadCurrentPage();
        }

        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                LoadCurrentPage();
            }
        }

        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                LoadCurrentPage();
            }
        }

        private void LastPage_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = _totalPages;
            LoadCurrentPage();
        }

        // معالج أحداث لوحة المفاتيح
        private void AdvancedDataGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (!EnableKeyboardShortcuts) return;

            var selectedItem = _mainDataGrid?.SelectedItem;

            switch (e.Key)
            {
                case Key.P: // حذف العنصر المحدد
                    if (selectedItem != null)
                    {
                        DeleteSelectedItem();
                        e.Handled = true;
                    }
                    break;

                case Key.E: // تعديل العنصر المحدد
                    if (selectedItem != null)
                    {
                        EditRequested?.Invoke(this, selectedItem);
                        e.Handled = true;
                    }
                    break;

                case Key.V: // عرض تفاصيل العنصر المحدد
                    if (selectedItem != null)
                    {
                        ViewRequested?.Invoke(this, selectedItem);
                        e.Handled = true;
                    }
                    break;

                case Key.F5: // تحديث البيانات
                    DataRefreshRequested?.Invoke(this, EventArgs.Empty);
                    e.Handled = true;
                    break;

                case Key.F: // التركيز على مربع البحث
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        _globalSearchBox?.Focus();
                        e.Handled = true;
                    }
                    break;

                case Key.A: // تحديد الكل
                    if (Keyboard.Modifiers == ModifierKeys.Control && EnableMultiSelect)
                    {
                        _mainDataGrid?.SelectAll();
                        e.Handled = true;
                    }
                    break;

                case Key.Delete: // حذف بديل
                    if (selectedItem != null)
                    {
                        DeleteSelectedItem();
                        e.Handled = true;
                    }
                    break;

                case Key.Enter: // تعديل أو عرض
                    if (selectedItem != null)
                    {
                        if (Keyboard.Modifiers == ModifierKeys.Shift)
                            ViewRequested?.Invoke(this, selectedItem);
                        else
                            EditRequested?.Invoke(this, selectedItem);
                        e.Handled = true;
                    }
                    break;

                case Key.Escape: // إلغاء التحديد
                    _mainDataGrid?.UnselectAll();
                    e.Handled = true;
                    break;
            }
        }

        private void DeleteSelectedItem()
        {
            var selectedItem = _mainDataGrid?.SelectedItem;
            if (selectedItem != null)
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من حذف هذا العنصر؟\n\nملاحظة: يمكنك استخدام P للحذف السريع",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question,
                    MessageBoxResult.No);

                if (result == MessageBoxResult.Yes)
                {
                    DeleteRequested?.Invoke(this, selectedItem);
                }
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// تعيين أعمدة الجدول
        /// </summary>
        public void SetColumns(IEnumerable<DataGridColumn> columns)
        {
            if (_mainDataGrid != null)
            {
                _mainDataGrid.Columns.Clear();
                foreach (var column in columns)
                {
                    _mainDataGrid.Columns.Add(column);
                }
            }
        }

        /// <summary>
        /// تعيين مصدر البيانات
        /// </summary>
        public void SetDataSource<T>(IEnumerable<T> data)
        {
            _originalData = new ObservableCollection<object>(data.Cast<object>());
            _filteredData = new ObservableCollection<object>(_originalData);

            CalculatePagination();
            LoadCurrentPage();
            UpdateRecordsInfo();

            // تطبيق Auto-fit للأعمدة بعد تحميل البيانات
            if (EnableAutoFitColumns)
            {
                Dispatcher.BeginInvoke(new Action(() => AutoFitColumns()), System.Windows.Threading.DispatcherPriority.Loaded);
            }

            // إضافة تلميح لوحة المفاتيح للجدول
            if (EnableKeyboardShortcuts && _mainDataGrid != null)
            {
                _mainDataGrid.ToolTip = "اختصارات لوحة المفاتيح:\n" +
                                       "P = حذف العنصر المحدد\n" +
                                       "E = تعديل العنصر المحدد\n" +
                                       "V = عرض تفاصيل العنصر\n" +
                                       "F5 = تحديث البيانات\n" +
                                       "Ctrl+F = البحث\n" +
                                       "Ctrl+A = تحديد الكل\n" +
                                       "Enter = تعديل (Shift+Enter = عرض)\n" +
                                       "Delete = حذف\n" +
                                       "Escape = إلغاء التحديد";
            }
        }

        /// <summary>
        /// إضافة عمود للجدول
        /// </summary>
        public void AddColumn(DataGridColumn column)
        {
            _mainDataGrid.Columns.Add(column);
        }

        /// <summary>
        /// إضافة عمود نصي
        /// </summary>
        public void AddTextColumn(string header, string bindingPath, double width = 100)
        {
            var column = new DataGridTextColumn
            {
                Header = header,
                Binding = new Binding(bindingPath),
                Width = width
            };

            // تحسين النمط للنصوص العربية
            var style = new System.Windows.Style(typeof(TextBlock));
            style.Setters.Add(new Setter(TextBlock.TextAlignmentProperty, TextAlignment.Right));
            style.Setters.Add(new Setter(TextBlock.FontFamilyProperty, new FontFamily("Segoe UI, Tahoma, Arial")));
            style.Setters.Add(new Setter(TextBlock.FontSizeProperty, 13.0));
            column.ElementStyle = style;

            AddColumn(column);
        }

        /// <summary>
        /// إضافة عمود تاريخ
        /// </summary>
        public void AddDateColumn(string header, string bindingPath, string format = "yyyy/MM/dd", double width = 120)
        {
            var column = new DataGridTextColumn
            {
                Header = header,
                Binding = new Binding(bindingPath) { StringFormat = format },
                Width = width
            };

            // تحسين النمط للتواريخ العربية
            var style = new System.Windows.Style(typeof(TextBlock));
            style.Setters.Add(new Setter(TextBlock.TextAlignmentProperty, TextAlignment.Center));
            style.Setters.Add(new Setter(TextBlock.FontFamilyProperty, new FontFamily("Segoe UI, Tahoma, Arial")));
            style.Setters.Add(new Setter(TextBlock.FontSizeProperty, 13.0));
            column.ElementStyle = style;

            AddColumn(column);
        }

        /// <summary>
        /// إضافة عمود رقمي
        /// </summary>
        public void AddNumericColumn(string header, string bindingPath, string format = "N2", double width = 100)
        {
            var column = new DataGridTextColumn
            {
                Header = header,
                Binding = new Binding(bindingPath) { StringFormat = format },
                Width = width
            };
            AddColumn(column);
        }

        /// <summary>
        /// إضافة عمود عملة
        /// </summary>
        public void AddCurrencyColumn(string header, string bindingPath, double width = 120)
        {
            var column = new DataGridTextColumn
            {
                Header = header,
                Binding = new Binding(bindingPath) { StringFormat = "C" },
                Width = width
            };
            AddColumn(column);
        }

        /// <summary>
        /// إضافة عمود إجراءات
        /// </summary>
        public void AddActionsColumn(string header, List<ActionButton> actions, double width = 150)
        {
            var column = new DataGridTemplateColumn
            {
                Header = header,
                Width = width
            };

            var template = new DataTemplate();
            var panel = new FrameworkElementFactory(typeof(StackPanel));
            panel.SetValue(StackPanel.OrientationProperty, Orientation.Horizontal);
            panel.SetValue(StackPanel.HorizontalAlignmentProperty, HorizontalAlignment.Center);

            foreach (var action in actions)
            {
                var button = new FrameworkElementFactory(typeof(Button));
                button.SetValue(Button.ContentProperty, action.Icon);
                button.SetValue(Button.WidthProperty, 30.0);
                button.SetValue(Button.HeightProperty, 25.0);
                button.SetValue(Button.BackgroundProperty, new SolidColorBrush(action.BackgroundColor));
                button.SetValue(Button.ForegroundProperty, Brushes.White);
                button.SetValue(Button.BorderThicknessProperty, new Thickness(0));
                button.SetValue(Button.MarginProperty, new Thickness(2, 0, 2, 0));
                button.SetValue(Button.CursorProperty, Cursors.Hand);
                button.SetValue(Button.ToolTipProperty, action.Tooltip);
                button.AddHandler(Button.ClickEvent, action.ClickHandler);

                panel.AppendChild(button);
            }

            template.VisualTree = panel;
            column.CellTemplate = template;
            AddColumn(column);
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        public void RefreshData()
        {
            ApplyFilters();
        }

        /// <summary>
        /// الحصول على العناصر المحددة
        /// </summary>
        public List<object> GetSelectedItems()
        {
            return _mainDataGrid.SelectedItems.Cast<object>().ToList();
        }

        /// <summary>
        /// تحديد عنصر معين
        /// </summary>
        public void SelectItem(object item)
        {
            _mainDataGrid.SelectedItem = item;
        }

        #endregion

        #region Auto-Fit Columns

        public void AutoFitColumns()
        {
            if (!EnableAutoFitColumns || _mainDataGrid == null)
                return;

            try
            {
                // تطبيق Auto-fit على جميع الأعمدة
                foreach (DataGridColumn column in _mainDataGrid.Columns)
                {
                    if (column is DataGridTextColumn textColumn)
                    {
                        // حساب العرض المطلوب بناءً على المحتوى والعنوان
                        var headerWidth = MeasureTextWidth(column.Header?.ToString() ?? "", _mainDataGrid.FontFamily, _mainDataGrid.FontSize, FontWeights.Bold);
                        var maxContentWidth = GetMaxContentWidth(textColumn);

                        // تحديد العرض الأمثل مع إضافة هامش
                        var optimalWidth = Math.Max(headerWidth, maxContentWidth) + 20;

                        // تحديد حد أدنى وأقصى للعرض
                        optimalWidth = Math.Max(optimalWidth, 80);  // حد أدنى
                        optimalWidth = Math.Min(optimalWidth, 300); // حد أقصى

                        column.Width = new DataGridLength(optimalWidth);
                    }
                    else if (column is DataGridTemplateColumn templateColumn)
                    {
                        // للأعمدة التي تحتوي على أزرار أو عناصر أخرى
                        var headerWidth = MeasureTextWidth(column.Header?.ToString() ?? "", _mainDataGrid.FontFamily, _mainDataGrid.FontSize, FontWeights.Bold);
                        var optimalWidth = Math.Max(headerWidth + 40, 120);
                        column.Width = new DataGridLength(optimalWidth);
                    }
                }

                // تحديث التخطيط
                _mainDataGrid.UpdateLayout();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق Auto-fit للأعمدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private double MeasureTextWidth(string text, FontFamily fontFamily, double fontSize, FontWeight fontWeight)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            var formattedText = new FormattedText(
                text,
                CultureInfo.CurrentCulture,
                FlowDirection.RightToLeft,
                new Typeface(fontFamily, FontStyles.Normal, fontWeight, FontStretches.Normal),
                fontSize,
                Brushes.Black,
                new NumberSubstitution(),
                1);

            return formattedText.Width;
        }

        private double GetMaxContentWidth(DataGridTextColumn column)
        {
            double maxWidth = 0;

            try
            {
                if (_mainDataGrid.ItemsSource != null)
                {
                    var items = _mainDataGrid.ItemsSource.Cast<object>().Take(100); // فحص أول 100 عنصر فقط للأداء

                    foreach (var item in items)
                    {
                        var binding = column.Binding as Binding;
                        if (binding != null)
                        {
                            var property = item.GetType().GetProperty(binding.Path.Path);
                            if (property != null)
                            {
                                var value = property.GetValue(item)?.ToString() ?? "";
                                var width = MeasureTextWidth(value, _mainDataGrid.FontFamily, _mainDataGrid.FontSize, FontWeights.Normal);
                                maxWidth = Math.Max(maxWidth, width);
                            }
                        }
                    }
                }
            }
            catch
            {
                // في حالة الخطأ، استخدم عرض افتراضي
                maxWidth = 100;
            }

            return maxWidth;
        }

        #endregion

        #region Keyboard Support

        private void MainDataGrid_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            // السماح بالتنقل باستخدام الأسهم
            if (e.Key == Key.Up || e.Key == Key.Down || e.Key == Key.PageUp || e.Key == Key.PageDown ||
                e.Key == Key.Home || e.Key == Key.End)
            {
                e.Handled = false;
                return;
            }

            // السماح بالتحديد المتعدد
            if ((e.Key == Key.A && Keyboard.Modifiers == ModifierKeys.Control) ||
                (e.Key == Key.Space && Keyboard.Modifiers == ModifierKeys.Control))
            {
                e.Handled = false;
                return;
            }
        }

        private void MainDataGrid_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // حرف P للحذف
                if (e.Key == Key.P && _mainDataGrid.SelectedItem != null)
                {
                    DeleteRequested?.Invoke(this, _mainDataGrid.SelectedItem);
                    e.Handled = true;
                    return;
                }

                // Delete key للحذف أيضاً
                if (e.Key == Key.Delete && _mainDataGrid.SelectedItem != null)
                {
                    DeleteRequested?.Invoke(this, _mainDataGrid.SelectedItem);
                    e.Handled = true;
                    return;
                }

                // Enter للتفاصيل
                if (e.Key == Key.Enter && _mainDataGrid.SelectedItem != null)
                {
                    RowDoubleClick?.Invoke(this, _mainDataGrid.SelectedItem);
                    e.Handled = true;
                    return;
                }

                // F5 لتحديث البيانات
                if (e.Key == Key.F5)
                {
                    DataRefreshRequested?.Invoke(this, EventArgs.Empty);
                    e.Handled = true;
                    return;
                }

                // Ctrl+F للبحث
                if (e.Key == Key.F && Keyboard.Modifiers == ModifierKeys.Control)
                {
                    if (_globalSearchBox != null)
                    {
                        _globalSearchBox.Focus();
                        _globalSearchBox.SelectAll();
                    }
                    e.Handled = true;
                    return;
                }

                // Escape لإلغاء التحديد
                if (e.Key == Key.Escape)
                {
                    _mainDataGrid.UnselectAll();
                    e.Handled = true;
                    return;
                }

                // Page navigation
                if (EnablePagination)
                {
                    if (e.Key == Key.Right && Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        NextPage_Click(null, null);
                        e.Handled = true;
                        return;
                    }

                    if (e.Key == Key.Left && Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        PreviousPage_Click(null, null);
                        e.Handled = true;
                        return;
                    }

                    if (e.Key == Key.Home && Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        FirstPage_Click(null, null);
                        e.Handled = true;
                        return;
                    }

                    if (e.Key == Key.End && Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        LastPage_Click(null, null);
                        e.Handled = true;
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة لوحة المفاتيح: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }

    /// <summary>
    /// كلاس لتعريف أزرار الإجراءات
    /// </summary>
    public class ActionButton
    {
        public string Icon { get; set; }
        public string Tooltip { get; set; }
        public Color BackgroundColor { get; set; }
        public RoutedEventHandler ClickHandler { get; set; }
    }

    /// <summary>
    /// محول لعرض رقم الصف
    /// </summary>
    public class RowNumberConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is DataGridRow row)
            {
                return (row.GetIndex() + 1).ToString();
            }
            return "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
