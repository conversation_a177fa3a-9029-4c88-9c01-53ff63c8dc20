# Create Distribution Package for Medical Devices Management System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Creating Distribution Package" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    $publishDir = "Release/Publish"
    $packageName = "MedicalDevicesManager_v6.1_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    $packageDir = "Release/$packageName"

    Write-Host "Creating package directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $packageDir -Force | Out-Null

    Write-Host "Copying executable files..." -ForegroundColor Yellow
    Copy-Item "$publishDir/*" $packageDir -Recurse -Force

    Write-Host "Copying database files..." -ForegroundColor Yellow
    if (Test-Path "MedicalDevices.db") {
        Copy-Item "MedicalDevices.db" $packageDir -Force
        Write-Host "  Copied MedicalDevices.db" -ForegroundColor Green
    }
    if (Test-Path "MedicalDevicesIntegrated.db") {
        Copy-Item "MedicalDevicesIntegrated.db" $packageDir -Force
        Write-Host "  Copied MedicalDevicesIntegrated.db" -ForegroundColor Green
    }

    Write-Host "Copying resources..." -ForegroundColor Yellow
    if (Test-Path "Resources") {
        Copy-Item "Resources" $packageDir -Recurse -Force
        Write-Host "  Copied Resources folder" -ForegroundColor Green
    }

    Write-Host "Creating README file..." -ForegroundColor Yellow
    $buildDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $readmeContent = @"
Medical Devices Management System v6.1
نظام إدارة الأجهزة الطبية المتكامل

Build Date: $buildDate
Target: Windows x64
Type: Self-contained executable

How to Run:
1. Double-click on MedicalDevicesManager.exe
2. The system will start automatically
3. No additional software installation required

System Requirements:
- Windows 10 or later (x64)
- Minimum 4GB RAM
- 500MB free disk space

Features:
- Complete Arabic interface with RTL support
- Medical devices management
- Sales and invoicing system
- Inventory and parts management
- Maintenance and installation tracking
- Automatic backup system
- Comprehensive reports and statistics

Files Included:
- MedicalDevicesManager.exe (Main executable)
- Supporting DLL files
- Database files (if available)
- Resources folder (if available)

First Run:
On first run, the system will:
1. Initialize the database
2. Create default settings
3. Set up backup directories
4. Configure Arabic language support

Enjoy using the Medical Devices Management System!
"@

    $readmeContent | Out-File -FilePath "$packageDir/README.txt" -Encoding UTF8
    Write-Host "  Created README.txt" -ForegroundColor Green

    Write-Host "Creating ZIP package..." -ForegroundColor Yellow
    $zipPath = "$packageDir.zip"
    Compress-Archive -Path $packageDir -DestinationPath $zipPath -Force

    # Get file information
    $exeFile = Get-Item "$packageDir/MedicalDevicesManager.exe"
    $zipFile = Get-Item $zipPath
    $exeSize = [math]::Round($exeFile.Length / 1MB, 2)
    $zipSize = [math]::Round($zipFile.Length / 1MB, 2)

    Write-Host ""
    Write-Host "Package created successfully!" -ForegroundColor Green
    Write-Host ""

    Write-Host "Build Summary:" -ForegroundColor Cyan
    Write-Host "  Executable: MedicalDevicesManager.exe ($exeSize MB)" -ForegroundColor White
    Write-Host "  Package: $packageName.zip ($zipSize MB)" -ForegroundColor White
    Write-Host "  Location: Release/" -ForegroundColor White
    Write-Host "  Status: Ready for distribution" -ForegroundColor Green
    Write-Host ""

    Write-Host "Distribution Options:" -ForegroundColor Magenta
    Write-Host "  1. Share the ZIP file for complete package" -ForegroundColor White
    Write-Host "  2. Share just the package folder" -ForegroundColor White
    Write-Host "  3. Share only the EXE file for simple distribution" -ForegroundColor White
    Write-Host ""

    Write-Host "Package Contents:" -ForegroundColor Yellow
    $files = Get-ChildItem $packageDir
    foreach ($file in $files) {
        if ($file.PSIsContainer) {
            Write-Host "  📁 $($file.Name)/" -ForegroundColor Blue
        } else {
            $size = [math]::Round($file.Length / 1KB, 1)
            Write-Host "  📄 $($file.Name) ($size KB)" -ForegroundColor White
        }
    }

} catch {
    Write-Host "Error creating package:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Distribution Package Completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
