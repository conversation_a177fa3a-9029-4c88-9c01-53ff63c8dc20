<Window x:Class="MedicalDevicesManager.Windows.AddEditSupplierWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل مورد" Height="640" Width="640"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Style="{StaticResource ArabicWindow}">

    <Border Style="{StaticResource ModernCard}" Margin="16">
        <Grid Margin="32">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان المحدث -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,32">
                <Border Background="{StaticResource PrimaryBrush}" CornerRadius="12" Width="48" Height="48" Margin="0,0,16,0">
                    <TextBlock Text="🏭" FontSize="24" Foreground="White"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock x:Name="TitleBlock" Text="إضافة مورد جديد"
                               Style="{StaticResource ModernTitle}"/>
                    <TextBlock Text="أدخل بيانات المورد الجديد"
                               Style="{StaticResource ModernMutedText}" Margin="0,4,0,0"/>
                </StackPanel>
            </StackPanel>

            <!-- النموذج المحدث -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                         ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    <!-- اسم المورد -->
                    <TextBlock Text="اسم المورد *" Style="{StaticResource ModernLabel}" Margin="0,0,0,8"/>
                    <TextBox x:Name="NameTextBox" Style="{StaticResource ArabicTextBox}" Margin="0,0,0,20"/>

                    <!-- معلومات الاتصال -->
                    <TextBlock Text="معلومات الاتصال" Style="{StaticResource ModernSubtitle}" Margin="0,0,0,12"/>
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="رقم الهاتف *" Style="{StaticResource ModernLabel}" Margin="0,0,0,6"/>
                            <TextBox x:Name="PhoneTextBox" Style="{StaticResource ArabicTextBox}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="البريد الإلكتروني" Style="{StaticResource ModernLabel}" Margin="0,0,0,6"/>
                            <TextBox x:Name="EmailTextBox" Style="{StaticResource ArabicTextBox}"/>
                        </StackPanel>
                    </Grid>

                    <!-- العنوان -->
                    <TextBlock Text="العنوان *" Style="{StaticResource ModernLabel}" Margin="0,0,0,8"/>
                    <TextBox x:Name="AddressTextBox" Style="{StaticResource ArabicTextBox}" Height="80"
                             TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,20"/>

                    <!-- الموقع -->
                    <TextBlock Text="الموقع" Style="{StaticResource ModernSubtitle}" Margin="0,0,0,12"/>
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="المدينة *" Style="{StaticResource ModernLabel}" Margin="0,0,0,6"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <ComboBox Grid.Column="0" x:Name="CityComboBox"
                                          Style="{StaticResource ArabicComboBox}"
                                          IsEditable="True" Margin="0,0,8,0"/>

                                <Button Grid.Column="1" x:Name="ManageSupplierCitiesBtn" Content="⚙️" Width="40" Height="40"
                                        Style="{StaticResource SecondaryButton}"
                                        ToolTip="إدارة المدن" Click="ManageSupplierCitiesBtn_Click"/>
                            </Grid>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="الرمز البريدي" Style="{StaticResource ModernLabel}" Margin="0,0,0,6"/>
                            <TextBox x:Name="PostalCodeTextBox" Style="{StaticResource ArabicTextBox}"/>
                        </StackPanel>
                    </Grid>

                    <!-- التقييم والحالة -->
                    <TextBlock Text="التقييم والحالة" Style="{StaticResource ModernSubtitle}" Margin="0,0,0,12"/>
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="التقييم (من 1 إلى 5) *" Style="{StaticResource ModernLabel}" Margin="0,0,0,6"/>
                            <ComboBox x:Name="RatingComboBox" Style="{StaticResource ArabicComboBox}">
                                <ComboBoxItem Content="1.0 - ضعيف"/>
                                <ComboBoxItem Content="1.5"/>
                                <ComboBoxItem Content="2.0 - مقبول"/>
                                <ComboBoxItem Content="2.5"/>
                                <ComboBoxItem Content="3.0 - جيد"/>
                                <ComboBoxItem Content="3.5"/>
                                <ComboBoxItem Content="4.0 - جيد جداً"/>
                                <ComboBoxItem Content="4.5"/>
                                <ComboBoxItem Content="5.0 - ممتاز"/>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="الحالة *" Style="{StaticResource ModernLabel}" Margin="0,0,0,6"/>
                            <ComboBox x:Name="StatusComboBox" Style="{StaticResource ArabicComboBox}">
                                <ComboBoxItem Content="نشط"/>
                                <ComboBoxItem Content="معلق"/>
                                <ComboBoxItem Content="محظور"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- ملاحظات -->
                    <TextBlock Text="ملاحظات" Style="{StaticResource ModernLabel}" Margin="0,0,0,8"/>
                    <TextBox x:Name="NotesTextBox" Style="{StaticResource ArabicTextBox}" Height="100"
                             TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,24"/>

                    <!-- معلومات إضافية -->
                    <Border Style="{StaticResource ArabicCard}" Background="#F8FAFC">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <Border Background="{StaticResource InfoBrush}" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                    <TextBlock Text="ℹ️" FontSize="16" Foreground="White"
                                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="معلومات إضافية" Style="{StaticResource ModernSubtitle}"/>
                            </StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="تاريخ الإنشاء:" Style="{StaticResource ModernMutedText}"/>
                                    <TextBlock x:Name="CreatedDateTextBlock" Text="سيتم تعيينه تلقائياً"
                                               Style="{StaticResource ModernLabel}" Margin="0,4,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="آخر تحديث:" Style="{StaticResource ModernMutedText}"/>
                                    <TextBlock x:Name="LastUpdatedTextBlock" Text="سيتم تعيينه تلقائياً"
                                               Style="{StaticResource ModernLabel}" Margin="0,4,0,0"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- الأزرار المحدثة -->
            <Border Grid.Row="2" Background="#F9FAFB" BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="0,1,0,0" Margin="-32,24,-32,-32">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="32,20">
                    <Button x:Name="SaveButton" Content="💾 حفظ المورد" Width="160"
                            Style="{StaticResource SuccessButton}"
                            Margin="0,0,12,0" Click="SaveButton_Click"/>
                    <Button x:Name="CancelButton" Content="❌ إلغاء" Width="140"
                            Style="{StaticResource DangerButton}"
                            Click="CancelButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
