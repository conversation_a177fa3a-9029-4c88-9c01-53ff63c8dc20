<Window x:Class="MedicalDevicesManager.Windows.AddEditSupplierWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل مورد" Height="600" Width="600"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="إضافة مورد جديد" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- اسم المورد -->
                <TextBlock Text="اسم المورد *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- معلومات الاتصال -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الهاتف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PhoneTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="البريد الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="EmailTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- العنوان -->
                <TextBlock Text="العنوان *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AddressTextBox" Height="60" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- المدينة والرمز البريدي -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المدينة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox Grid.Column="0" x:Name="CityComboBox" Height="35" Padding="10,8" FontSize="14"
                                      IsEditable="True" Margin="0,0,5,0"/>

                            <Button Grid.Column="1" x:Name="ManageSupplierCitiesBtn" Content="⚙️" Width="35" Height="35"
                                    Background="#6C757D" Foreground="White" BorderThickness="0"
                                    ToolTip="إدارة المدن" Click="ManageSupplierCitiesBtn_Click"/>
                        </Grid>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الرمز البريدي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PostalCodeTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- التقييم والحالة -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="التقييم (من 1 إلى 5) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="RatingComboBox" Height="35" Padding="10,8" FontSize="14">
                            <ComboBoxItem Content="1.0 - ضعيف"/>
                            <ComboBoxItem Content="1.5"/>
                            <ComboBoxItem Content="2.0 - مقبول"/>
                            <ComboBoxItem Content="2.5"/>
                            <ComboBoxItem Content="3.0 - جيد"/>
                            <ComboBoxItem Content="3.5"/>
                            <ComboBoxItem Content="4.0 - جيد جداً"/>
                            <ComboBoxItem Content="4.5"/>
                            <ComboBoxItem Content="5.0 - ممتاز"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الحالة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="StatusComboBox" Height="35" Padding="10,8" FontSize="14">
                            <ComboBoxItem Content="نشط"/>
                            <ComboBoxItem Content="معلق"/>
                            <ComboBoxItem Content="محظور"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>
                
                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox" Height="80" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- معلومات إضافية -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات إضافية" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="تاريخ الإنشاء:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="CreatedDateTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="آخر تحديث:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="LastUpdatedTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ المورد" Width="140" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
