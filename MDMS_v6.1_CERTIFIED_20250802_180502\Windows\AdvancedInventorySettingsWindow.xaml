<Window x:Class="MedicalDevicesManager.Windows.AdvancedInventorySettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات المخزون المتقدمة" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="⚙️ إعدادات المخزون المتقدمة" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="35" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="RefreshBtn_Click"/>
            
            <Button Grid.Column="1" x:Name="ApplyDefaultsBtn" Content="⚙️ تطبيق الافتراضي" Width="150" Height="35" 
                    Background="#FFC107" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="ApplyDefaultsBtn_Click"/>
            
            <Button Grid.Column="2" x:Name="CheckAlertsBtn" Content="🔔 فحص التنبيهات" Width="130" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,20,0" Click="CheckAlertsBtn_Click"/>
            
            <TextBlock Grid.Column="4" x:Name="StatusTextBlock" Text="جاهز" VerticalAlignment="Center" 
                       FontWeight="Bold" Foreground="#28A745"/>
        </Grid>
        
        <!-- جدول إعدادات المخزون -->
        <DataGrid Grid.Row="2" x:Name="InventorySettingsDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14"
                  SelectionChanged="InventorySettingsDataGrid_SelectionChanged">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding InventoryItem.Name}" Width="200"/>
                <DataGridTextColumn Header="الفئة" Binding="{Binding InventoryItem.Category}" Width="120"/>
                <DataGridTextColumn Header="المخزون الحالي" Binding="{Binding InventoryItem.CurrentStock}" Width="100"/>
                <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinimumStock}" Width="80"/>
                <DataGridTextColumn Header="مستوى إعادة الطلب" Binding="{Binding ReorderLevel}" Width="120"/>
                <DataGridTextColumn Header="الحد الأقصى" Binding="{Binding MaximumStock}" Width="80"/>
                
                <DataGridTemplateColumn Header="تنبيه المخزون" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox IsChecked="{Binding EnableLowStockAlert}" IsEnabled="False" HorizontalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTemplateColumn Header="تنبيه الصلاحية" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox IsChecked="{Binding EnableExpiryAlert}" IsEnabled="False" HorizontalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTextColumn Header="أيام التنبيه" Binding="{Binding ExpiryAlertDays}" Width="80"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="✏️" Width="30" Height="25" 
                                        Background="#FFC107" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="تعديل"
                                        Click="EditSettingsBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteSettingsBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- نموذج تعديل الإعدادات -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0" x:Name="SettingsPanel" Visibility="Collapsed">
            
            <StackPanel>
                <TextBlock Text="📝 تعديل إعدادات المخزون" FontWeight="Bold" Margin="0,0,0,15" Foreground="#495057"/>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- الصف الأول - معلومات المنتج -->
                    <Grid Grid.Row="0" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="المنتج:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" x:Name="ProductNameTextBlock" Text="" VerticalAlignment="Center" 
                                   Foreground="#17A2B8" FontWeight="SemiBold" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="2" Text="المخزون الحالي:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                        <TextBlock Grid.Column="3" x:Name="CurrentStockTextBlock" Text="" VerticalAlignment="Center" 
                                   Foreground="#28A745" FontWeight="SemiBold"/>
                    </Grid>
                    
                    <!-- الصف الثاني - إعدادات المخزون -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="الحد الأدنى:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="1" x:Name="MinimumStockTextBox" Height="30" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="2" Text="مستوى إعادة الطلب:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="3" x:Name="ReorderLevelTextBox" Height="30" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="4" Text="الحد الأقصى:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="5" x:Name="MaximumStockTextBox" Height="30"/>
                    </Grid>
                    
                    <!-- الصف الثالث - إعدادات التنبيهات -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <CheckBox Grid.Column="0" x:Name="EnableLowStockAlertCheckBox" Content="تنبيه المخزون المنخفض" 
                                  VerticalAlignment="Center" Margin="0,0,20,0"/>
                        
                        <CheckBox Grid.Column="1" x:Name="EnableExpiryAlertCheckBox" Content="تنبيه انتهاء الصلاحية" 
                                  VerticalAlignment="Center" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="2" Text="أيام التنبيه:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="3" x:Name="ExpiryAlertDaysTextBox" Height="30" Width="60" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="4" Text="البريد الإلكتروني للتنبيهات:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="5" x:Name="AlertEmailTextBox" Height="30" Width="200" Margin="0,0,20,0"/>
                        
                        <StackPanel Grid.Column="6" Orientation="Horizontal">
                            <Button x:Name="SaveSettingsBtn" Content="💾 حفظ" Width="80" Height="30" 
                                    Background="#28A745" Foreground="White" BorderThickness="0" 
                                    FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="SaveSettingsBtn_Click"/>
                            <Button x:Name="CancelBtn" Content="❌ إلغاء" Width="80" Height="30" 
                                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                                    FontSize="12" FontWeight="SemiBold" Click="CancelBtn_Click"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</Window>
