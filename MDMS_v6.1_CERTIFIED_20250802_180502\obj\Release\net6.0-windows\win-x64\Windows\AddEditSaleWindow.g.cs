﻿#pragma checksum "..\..\..\..\..\Windows\AddEditSaleWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "88A31167BEE060840976D42F44DC2C60966549A3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// AddEditSaleWindow
    /// </summary>
    public partial class AddEditSaleWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 16 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBlock;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker SaleDatePicker;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerComboBox;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DeviceComboBox;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectFromInventoryBtn;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SerialNumberComboBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TotalAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountPercentageTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FinalAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PartialPaymentPanel;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PaidAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PaymentPercentageTextBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/addeditsalewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.InvoiceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.SaleDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.CustomerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.DeviceComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 45 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.DeviceComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DeviceComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SelectFromInventoryBtn = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.SelectFromInventoryBtn.Click += new System.Windows.RoutedEventHandler(this.SelectFromInventoryBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SerialNumberComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            
            #line 65 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshSerialNumbers_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 78 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.QuantityTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 10:
            this.UnitPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 83 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.UnitPriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TotalAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.DiscountPercentageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 108 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.DiscountPercentageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 13:
            this.FinalAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 122 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.PaymentStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PartialPaymentPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.PaidAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 145 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.PaidAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PaidAmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PaymentPercentageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.RemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.DeviceInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.CustomerInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 183 "..\..\..\..\..\Windows\AddEditSaleWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

