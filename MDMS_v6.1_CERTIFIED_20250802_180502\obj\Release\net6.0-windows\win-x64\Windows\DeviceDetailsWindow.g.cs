﻿#pragma checksum "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5C4B4A4C89CCD592DEBDF784242A8DB688C56221"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// DeviceDetailsWindow
    /// </summary>
    public partial class DeviceDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 33 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentFilterText;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilterDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesCountSummary;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceCountSummary;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstallationsCountSummary;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilesCountSummary;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BrandTextBlock;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelTextBlock;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryTextBlock;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescriptionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PurchasePriceTextBlock;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SellingPriceTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierTextBlock;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocationTextBlock;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PurchaseDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarrantyStartTextBlock;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarrantyEndTextBlock;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SpecificationsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshSerialNumbersBtn;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SerialNumbersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumbersCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserManualTextBlock;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenUserManualBtn;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceManualTextBlock;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMaintenanceManualBtn;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OriginCertificateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOriginCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QualityCertificateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenQualityCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficialCertificationsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOfficialCertificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 422 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TechnicalInfoBookletTextBlock;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenTechnicalInfoBookletBtn;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImportPapersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenImportPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractPapersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 458 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenContractPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 480 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SalesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 497 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 514 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid MaintenanceDataGrid;
        
        #line default
        #line hidden
        
        
        #line 530 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 547 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InstallationsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 565 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstallationsCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 605 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserManualFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 607 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenUserManualFileBtn;
        
        #line default
        #line hidden
        
        
        #line 613 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceManualFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 615 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMaintenanceManualFileBtn;
        
        #line default
        #line hidden
        
        
        #line 621 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OriginCertificateFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 623 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOriginCertificateFileBtn;
        
        #line default
        #line hidden
        
        
        #line 629 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QualityCertificateFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 631 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenQualityCertificateFileBtn;
        
        #line default
        #line hidden
        
        
        #line 637 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficialCertificationsFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 639 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOfficialCertificationsFileBtn;
        
        #line default
        #line hidden
        
        
        #line 645 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TechnicalInfoBookletFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 647 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenTechnicalInfoBookletFileBtn;
        
        #line default
        #line hidden
        
        
        #line 653 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImportPapersFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 655 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenImportPapersFileBtn;
        
        #line default
        #line hidden
        
        
        #line 661 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractPapersFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 663 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenContractPapersFileBtn;
        
        #line default
        #line hidden
        
        
        #line 671 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportBookletsPanel;
        
        #line default
        #line hidden
        
        
        #line 679 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AdditionalFilesPanel;
        
        #line default
        #line hidden
        
        
        #line 688 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 704 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditDeviceBtn;
        
        #line default
        #line hidden
        
        
        #line 708 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintDetailsBtn;
        
        #line default
        #line hidden
        
        
        #line 712 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/devicedetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DeviceNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SerialNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CurrentFilterText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FilterDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            
            #line 65 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectSerialNumber_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 69 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowAllData_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SalesCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.MaintenanceCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.InstallationsCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.FilesCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.BrandTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ModelTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.CategoryTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.DescriptionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.PurchasePriceTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SellingPriceTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.SupplierTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.LocationTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.PurchaseDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.CreatedDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.WarrantyStartTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.WarrantyEndTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.NotesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.SpecificationsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.RefreshSerialNumbersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 251 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.RefreshSerialNumbersBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshSerialNumbersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SerialNumbersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 28:
            this.SerialNumbersCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.UserManualTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.OpenUserManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenUserManualBtn.Click += new System.Windows.RoutedEventHandler(this.OpenUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.MaintenanceManualTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.OpenMaintenanceManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 358 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenMaintenanceManualBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.OriginCertificateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.OpenOriginCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 375 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOriginCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.QualityCertificateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.OpenQualityCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 392 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenQualityCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.OpenQualityCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.OfficialCertificationsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.OpenOfficialCertificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 409 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOfficialCertificationsBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.TechnicalInfoBookletTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.OpenTechnicalInfoBookletBtn = ((System.Windows.Controls.Button)(target));
            
            #line 426 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenTechnicalInfoBookletBtn.Click += new System.Windows.RoutedEventHandler(this.OpenTechnicalInfoBookletBtn_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.ImportPapersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.OpenImportPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 443 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenImportPapersBtn.Click += new System.Windows.RoutedEventHandler(this.OpenImportPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.ContractPapersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 44:
            this.OpenContractPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 460 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenContractPapersBtn.Click += new System.Windows.RoutedEventHandler(this.OpenContractPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.SalesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 46:
            this.SalesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 47:
            this.MaintenanceDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 48:
            this.MaintenanceCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.InstallationsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 50:
            this.InstallationsCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.UserManualFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.OpenUserManualFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 608 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenUserManualFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            this.MaintenanceManualFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.OpenMaintenanceManualFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 616 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenMaintenanceManualFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.OriginCertificateFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 56:
            this.OpenOriginCertificateFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 624 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOriginCertificateFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.QualityCertificateFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.OpenQualityCertificateFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 632 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenQualityCertificateFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenQualityCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 59:
            this.OfficialCertificationsFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 60:
            this.OpenOfficialCertificationsFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 640 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOfficialCertificationsFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.TechnicalInfoBookletFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 62:
            this.OpenTechnicalInfoBookletFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 648 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenTechnicalInfoBookletFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenTechnicalInfoBookletBtn_Click);
            
            #line default
            #line hidden
            return;
            case 63:
            this.ImportPapersFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 64:
            this.OpenImportPapersFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 656 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenImportPapersFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenImportPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 65:
            this.ContractPapersFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 66:
            this.OpenContractPapersFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 664 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenContractPapersFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenContractPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 67:
            this.ReportBookletsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 68:
            this.AdditionalFilesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 69:
            this.FilesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 70:
            this.EditDeviceBtn = ((System.Windows.Controls.Button)(target));
            
            #line 706 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.EditDeviceBtn.Click += new System.Windows.RoutedEventHandler(this.EditDeviceBtn_Click);
            
            #line default
            #line hidden
            return;
            case 71:
            this.PrintDetailsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 710 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.PrintDetailsBtn.Click += new System.Windows.RoutedEventHandler(this.PrintDetailsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 72:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 714 "..\..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

