<Window x:Class="MedicalDevicesManager.Windows.AddEditDeviceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل جهاز طبي" Height="720" Width="640"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI Variable, Segoe UI, Tahoma, Arial"
        Background="#FAFAFA">

    <Window.Resources>
        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="Hidden"
                                        VerticalScrollBarVisibility="Hidden"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#9CA3AF"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#3B82F6"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="16,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="12" Margin="16">
        <Border.Effect>
            <DropShadowEffect Color="#000000" Opacity="0.1"
                            BlurRadius="20" ShadowDepth="4" Direction="270"/>
        </Border.Effect>

        <Grid Margin="32">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان المحدث -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,32">
                <Border Background="#3B82F6" CornerRadius="12" Width="48" Height="48" Margin="0,0,16,0">
                    <TextBlock Text="🏥" FontSize="24" Foreground="White"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock x:Name="TitleBlock" Text="إضافة جهاز طبي جديد"
                               FontSize="20" FontWeight="SemiBold" Foreground="#1F2937"/>
                    <TextBlock Text="أدخل بيانات الجهاز الطبي الجديد"
                               FontSize="14" Foreground="#6B7280" Margin="0,4,0,0"/>
                </StackPanel>
            </StackPanel>

            <!-- النموذج المحدث -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                         ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    <!-- اسم الجهاز -->
                    <TextBlock Text="اسم الجهاز *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <TextBox x:Name="NameTextBox" Style="{StaticResource ModernTextBox}" Margin="0,0,0,20"/>

                    <!-- الماركة -->
                    <TextBlock Text="الماركة *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <TextBox x:Name="BrandTextBox" Style="{StaticResource ModernTextBox}" Margin="0,0,0,20"/>

                    <!-- الموديل -->
                    <TextBlock Text="الموديل *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <TextBox x:Name="ModelTextBox" Style="{StaticResource ModernTextBox}" Margin="0,0,0,20"/>

                    <!-- الأرقام التسلسلية -->
                    <TextBlock Text="الأرقام التسلسلية *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <Border Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="12" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <!-- معلومات توضيحية -->
                            <Border Background="#EBF8FF" BorderBrush="#3B82F6" BorderThickness="1" CornerRadius="8" Padding="12" Margin="0,0,0,16">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="💡" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="سيتم إنشاء جهاز منفصل لكل رقم تسلسلي مضاف"
                                              FontSize="12" Foreground="#1E40AF" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>

                            <!-- نموذج إضافة رقم تسلسلي -->
                            <Grid Margin="0,0,0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- تسميات الحقول -->
                                <Grid Grid.Row="0" Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="الرقم التسلسلي" FontSize="12"
                                              FontWeight="SemiBold" Foreground="#374151" Margin="0,0,8,0"/>
                                    <TextBlock Grid.Column="1" Text="اسم المكون" FontSize="12"
                                              FontWeight="SemiBold" Foreground="#374151" Margin="8,0,8,0"/>
                                </Grid>

                                <!-- حقول الإدخال -->
                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0" x:Name="NewSerialNumberTextBox"
                                             Style="{StaticResource ModernTextBox}" Height="36" Margin="0,0,8,0"/>
                                    <TextBox Grid.Column="1" x:Name="NewComponentNameTextBox"
                                             Style="{StaticResource ModernTextBox}" Height="36" Margin="8,0,8,0"/>
                                    <Button Grid.Column="2" x:Name="AddSerialBtn" Content="➕" Width="36" Height="36"
                                            Background="#10B981" Foreground="White"
                                            Style="{StaticResource ModernButton}"
                                            FontSize="16" FontWeight="Bold" Click="AddSerialBtn_Click"
                                            ToolTip="إضافة رقم تسلسلي"/>
                                </Grid>
                            </Grid>

                            <!-- جدول الأرقام التسلسلية -->
                            <Border Background="White" CornerRadius="8" BorderBrush="#E5E7EB" BorderThickness="1">
                                <DataGrid x:Name="SerialNumbersDataGrid"
                                          AutoGenerateColumns="False" CanUserAddRows="False"
                                          CanUserDeleteRows="False" IsReadOnly="True"
                                          GridLinesVisibility="None" HeadersVisibility="Column"
                                          Background="Transparent" RowBackground="Transparent"
                                          AlternatingRowBackground="#F9FAFB"
                                          BorderThickness="0" FontSize="12"
                                          SelectionMode="Single" SelectionUnit="FullRow"
                                          Height="180" Margin="8">

                                    <DataGrid.ColumnHeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#F3F4F6"/>
                                            <Setter Property="Foreground" Value="#374151"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Padding" Value="12,8"/>
                                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                            <Setter Property="BorderBrush" Value="#E5E7EB"/>
                                        </Style>
                                    </DataGrid.ColumnHeaderStyle>

                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="الرقم التسلسلي" Binding="{Binding SerialNumber}" Width="*"/>
                                        <DataGridTextColumn Header="اسم المكون" Binding="{Binding ComponentName}" Width="*"/>
                                        <DataGridTemplateColumn Header="إجراءات" Width="80">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <Button Content="🗑️" Width="28" Height="28"
                                                            Background="#EF4444" Foreground="White"
                                                            Style="{StaticResource ModernButton}"
                                                            FontSize="12" Click="RemoveSerialBtn_Click"
                                                            ToolTip="حذف"/>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Border>

                            <!-- معلومات إضافية -->
                            <TextBlock x:Name="SerialCountTextBlock" Text="العدد: 0"
                                       FontSize="12" Foreground="#6B7280" FontWeight="Medium" Margin="0,12,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- الفئة -->
                    <TextBlock Text="الفئة *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ComboBox Grid.Column="0" x:Name="CategoryComboBox"
                                  Style="{StaticResource ModernComboBox}"
                                  IsEditable="True" Margin="0,0,8,0"/>

                        <Button Grid.Column="1" x:Name="ManageCategoriesBtn" Content="⚙️" Width="40" Height="40"
                                Background="#6B7280" Foreground="White"
                                Style="{StaticResource ModernButton}"
                                ToolTip="إدارة الفئات" Click="ManageCategoriesBtn_Click"/>
                    </Grid>

                    <!-- الوصف -->
                    <TextBlock Text="الوصف" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <TextBox x:Name="DescriptionTextBox" Height="100"
                             Style="{StaticResource ModernTextBox}"
                             TextWrapping="Wrap" AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto" Margin="0,0,0,20"/>

                    <!-- الأسعار -->
                    <TextBlock Text="الأسعار" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="سعر الشراء *" FontWeight="Medium" Foreground="#6B7280" Margin="0,0,0,6"/>
                            <TextBox x:Name="PurchasePriceTextBox" Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="سعر البيع *" FontWeight="Medium" Foreground="#6B7280" Margin="0,0,0,6"/>
                            <TextBox x:Name="SellingPriceTextBox" Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>
                    </Grid>

                    <!-- التواريخ -->
                    <TextBlock Text="التواريخ المهمة" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="تاريخ الشراء *" FontWeight="Medium" Foreground="#6B7280" Margin="0,0,0,6"/>
                            <DatePicker x:Name="PurchaseDatePicker" Height="40" FontSize="14"
                                       BorderBrush="#D1D5DB" BorderThickness="1" Background="White"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="بداية الضمان *" FontWeight="Medium" Foreground="#6B7280" Margin="0,0,0,6"/>
                            <DatePicker x:Name="WarrantyStartDatePicker" Height="40" FontSize="14"
                                       BorderBrush="#D1D5DB" BorderThickness="1" Background="White"/>
                        </StackPanel>
                    </Grid>

                    <!-- نهاية الضمان -->
                    <TextBlock Text="نهاية الضمان *" FontWeight="Medium" Foreground="#6B7280" Margin="0,0,0,6"/>
                    <DatePicker x:Name="WarrantyEndDatePicker" Height="40" FontSize="14" Margin="0,0,0,20"
                               BorderBrush="#D1D5DB" BorderThickness="1" Background="White"/>

                    <!-- المورد -->
                    <TextBlock Text="المورد *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <ComboBox x:Name="SupplierComboBox" Style="{StaticResource ModernComboBox}" Margin="0,0,0,20"/>

                    <!-- الموقع والحالة -->
                    <TextBlock Text="الموقع والحالة" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                    <Grid Margin="0,0,0,24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="الموقع *" FontWeight="Medium" Foreground="#6B7280" Margin="0,0,0,6"/>
                            <ComboBox x:Name="LocationComboBox" Style="{StaticResource ModernComboBox}">
                                <ComboBoxItem Content="المستودع الرئيسي"/>
                                <ComboBoxItem Content="المستودع الثانوي"/>
                                <ComboBoxItem Content="المعرض"/>
                                <ComboBoxItem Content="قيد التسليم"/>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="الحالة *" FontWeight="Medium" Foreground="#6B7280" Margin="0,0,0,6"/>
                            <ComboBox x:Name="StatusComboBox" Style="{StaticResource ModernComboBox}">
                                <ComboBoxItem Content="متاح"/>
                                <ComboBoxItem Content="مباع"/>
                                <ComboBoxItem Content="تحت الصيانة"/>
                                <ComboBoxItem Content="معطل"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- المستندات والملفات -->
                    <Border Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="12" Padding="24" Margin="0,0,0,24">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                <Border Background="#3B82F6" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                    <TextBlock Text="📄" FontSize="16" Foreground="White"
                                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="المستندات والملفات" FontWeight="SemiBold" FontSize="16" Foreground="#1F2937"/>
                                    <TextBlock Text="إرفاق المستندات المتعلقة بالجهاز" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- كتيب المستخدم -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="140"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="كتيب المستخدم:" VerticalAlignment="Center"
                                          FontSize="13" FontWeight="Medium" Foreground="#374151"/>
                                <TextBox Grid.Column="1" x:Name="UserManualPathTextBox"
                                         Style="{StaticResource ModernTextBox}" Height="36" Margin="8,0,8,0"
                                         IsReadOnly="True"/>
                                <Button Grid.Column="2" x:Name="BrowseUserManualBtn" Content="📁" Width="36" Height="36"
                                        Background="#10B981" Foreground="White"
                                        Style="{StaticResource ModernButton}"
                                        ToolTip="اختيار ملف" Click="BrowseUserManualBtn_Click"/>
                                <Button Grid.Column="3" x:Name="ClearUserManualBtn" Content="❌" Width="36" Height="36"
                                        Background="#EF4444" Foreground="White"
                                        Style="{StaticResource ModernButton}"
                                        ToolTip="مسح الملف" Margin="4,0,0,0" Click="ClearUserManualBtn_Click"/>
                            </Grid>

                        <!-- كتيب الصيانة -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="كتيب الصيانة:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="MaintenanceManualPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseMaintenanceManualBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseMaintenanceManualBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearMaintenanceManualBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearMaintenanceManualBtn_Click"/>
                        </Grid>

                        <!-- شهادة المنشأ -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="شهادة المنشأ:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="OriginCertificatePathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseOriginCertificateBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseOriginCertificateBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearOriginCertificateBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearOriginCertificateBtn_Click"/>
                        </Grid>

                        <!-- أوراق شهادة الجودة -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="أوراق شهادة الجودة:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="QualityCertificatePathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseQualityCertificateBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseQualityCertificateBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearQualityCertificateBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearQualityCertificateBtn_Click"/>
                        </Grid>

                        <!-- المصادقات الرسمية -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="المصادقات الرسمية:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="OfficialCertificationsPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseOfficialCertificationsBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseOfficialCertificationsBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearOfficialCertificationsBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearOfficialCertificationsBtn_Click"/>
                        </Grid>

                        <!-- كتيب المعلومات التقنية -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="كتيب المعلومات التقنية:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="TechnicalInfoBookletPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseTechnicalInfoBookletBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseTechnicalInfoBookletBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearTechnicalInfoBookletBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearTechnicalInfoBookletBtn_Click"/>
                        </Grid>

                        <!-- أوراق الاستيراد -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="أوراق الاستيراد:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="ImportPapersPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseImportPapersBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseImportPapersBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearImportPapersBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearImportPapersBtn_Click"/>
                        </Grid>

                        <!-- أوراق العقود -->
                        <Grid Margin="0,0,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="أوراق العقود:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="ContractPapersPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseContractPapersBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseContractPapersBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearContractPapersBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearContractPapersBtn_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

            <!-- الأزرار المحدثة -->
            <Border Grid.Row="2" Background="#F9FAFB" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Margin="-32,24,-32,-32">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="32,20">
                    <Button x:Name="SaveButton" Content="💾 حفظ" Width="140" Height="44"
                            Background="#10B981" Foreground="White"
                            Style="{StaticResource ModernButton}"
                            FontSize="14" FontWeight="SemiBold" Margin="0,0,12,0" Click="SaveButton_Click"/>
                    <Button x:Name="CancelButton" Content="❌ إلغاء" Width="140" Height="44"
                            Background="#EF4444" Foreground="White"
                            Style="{StaticResource ModernButton}"
                            FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
