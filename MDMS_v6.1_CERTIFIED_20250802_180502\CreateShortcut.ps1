# Create Medical Devices Management System Desktop Shortcut

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Medical Devices Management System" -ForegroundColor Yellow
Write-Host "   Creating Desktop Shortcut" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Creating desktop shortcut..." -ForegroundColor Green
Write-Host ""

try {
    # Get paths
    $currentDir = Get-Location
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = Join-Path $desktopPath "Medical Devices System.lnk"
    $batFilePath = Join-Path $desktopPath "Medical-Devices-System.bat"
    
    # Create enhanced batch file
    $batContent = @"
@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    Medical Devices Management System
echo ========================================
echo.
echo Starting system with Arabic improvements...
echo.

cd /d "$currentDir"

echo Building project...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo Build failed
    pause
    exit /b 1
)

echo Build successful - System ready to run
echo.

start "" dotnet run --configuration Release

echo System started successfully!
echo.
echo System is now running in background
echo.
echo Available features:
echo   • Medical Devices Management
echo   • Sales and Invoicing System
echo   • Inventory and Parts Management
echo   • Maintenance and Installation System
echo   • Automatic Backup System
echo   • Reports and Statistics
echo.
timeout /t 3 /nobreak >nul
"@

    # Write batch file
    $batContent | Out-File -FilePath $batFilePath -Encoding UTF8
    
    Write-Host "Batch file created successfully!" -ForegroundColor Green
    Write-Host ""
    
    # Create Windows shortcut
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($shortcutPath)
    $Shortcut.TargetPath = $batFilePath
    $Shortcut.WorkingDirectory = $currentDir
    $Shortcut.Description = "Medical Devices Management System"
    $Shortcut.IconLocation = "shell32.dll,21"
    $Shortcut.Save()
    
    Write-Host "Shortcut created successfully!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "File locations:" -ForegroundColor Cyan
    Write-Host "   • Shortcut: $shortcutPath" -ForegroundColor White
    Write-Host "   • Batch file: $batFilePath" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Ways to run the system:" -ForegroundColor Yellow
    Write-Host "   1. Double-click the desktop shortcut" -ForegroundColor White
    Write-Host "   2. Double-click the Medical-Devices-System.bat file" -ForegroundColor White
    Write-Host ""
    
    Write-Host "New features:" -ForegroundColor Magenta
    Write-Host "   • Complete Arabic interface with RTL direction" -ForegroundColor White
    Write-Host "   • Enhanced automatic backup system" -ForegroundColor White
    Write-Host "   • Advanced inventory and parts management" -ForegroundColor White
    Write-Host "   • Comprehensive reports and statistics system" -ForegroundColor White
    Write-Host "   • Improved interfaces for all operations" -ForegroundColor White
    Write-Host ""
    
    # Test shortcut
    if (Test-Path $shortcutPath) {
        Write-Host "Testing shortcut..." -ForegroundColor Yellow
        
        if (Test-Path $batFilePath) {
            Write-Host "All files ready for use!" -ForegroundColor Green
        } else {
            Write-Host "Warning: Batch file not found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Warning: Shortcut not found" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Error creating shortcut:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Shortcut creation completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
