using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditCustomerWindow : Window
    {
        private Customer _customer;
        private readonly bool _isEditMode;

        public AddEditCustomerWindow(Customer customer = null)
        {
            InitializeComponent();
            _customer = customer ?? new Customer();
            _isEditMode = customer != null;

            LoadCustomerTypesAsync();
            LoadCitiesAsync();
            
            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل عميل";
                LoadCustomerData();
            }
            else
            {
                // تعيين القيم الافتراضية
                CustomerTypeComboBox.SelectedIndex = 0;
                CityComboBox.SelectedIndex = 0;
                StatusComboBox.SelectedIndex = 0;
                CreditLimitTextBox.Text = "0";
                CreatedDateTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            }
        }
        
        private void LoadCustomerData()
        {
            if (_customer == null) return;
            
            NameTextBox.Text = _customer.Name;
            PhoneTextBox.Text = _customer.Phone;
            EmailTextBox.Text = _customer.Email;
            AddressTextBox.Text = _customer.Address;
            PostalCodeTextBox.Text = _customer.PostalCode;
            CreditLimitTextBox.Text = _customer.CreditLimit.ToString();
            CreatedDateTextBlock.Text = _customer.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            LastUpdatedTextBlock.Text = _customer.LastUpdated.ToString("dd/MM/yyyy HH:mm");
            
            // تعيين نوع العميل
            foreach (var item in CustomerTypeComboBox.Items)
            {
                if (item.ToString().Contains(_customer.CustomerType))
                {
                    CustomerTypeComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين المدينة
            foreach (var item in CityComboBox.Items)
            {
                if (item.ToString().Contains(_customer.City))
                {
                    CityComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (var item in StatusComboBox.Items)
            {
                if (item.ToString().Contains(_customer.Status))
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateCustomerData();
                    App.DatabaseContext.Customers.Update(_customer);
                }
                else
                {
                    _customer = new Customer();
                    UpdateCustomerData();
                    _customer.CreatedDate = DateTime.Now;
                    App.DatabaseContext.Customers.Add(_customer);
                }
                
                _customer.LastUpdated = DateTime.Now;

                try
                {
                    await App.DatabaseContext.SaveChangesAsync();

                    MessageBox.Show(
                    _isEditMode ? "تم تحديث العميل بنجاح!" : "تم إضافة العميل بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                    DialogResult = true;
                    Close();
                }
                catch (Exception saveEx)
                {
                    MessageBox.Show($"خطأ في حفظ البيانات: {saveEx.Message}\n\nتفاصيل: {saveEx.InnerException?.Message}",
                        "خطأ في الحفظ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة البيانات: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateCustomerData()
        {
            _customer.Name = NameTextBox.Text.Trim();
            _customer.CustomerType = CustomerTypeComboBox.Text;
            _customer.Phone = PhoneTextBox.Text.Trim();
            _customer.Email = EmailTextBox.Text.Trim();
            _customer.Address = AddressTextBox.Text.Trim();
            _customer.City = CityComboBox.Text;
            _customer.PostalCode = PostalCodeTextBox.Text.Trim();
            _customer.CreditLimit = decimal.Parse(CreditLimitTextBox.Text);
            _customer.Status = StatusComboBox.Text;
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }
            
            if (CustomerTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CustomerTypeComboBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(AddressTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال العنوان", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AddressTextBox.Focus();
                return false;
            }
            
            if (CityComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المدينة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CityComboBox.Focus();
                return false;
            }
            
            if (!decimal.TryParse(CreditLimitTextBox.Text, out decimal creditLimit) || creditLimit < 0)
            {
                MessageBox.Show("يرجى إدخال حد ائتماني صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CreditLimitTextBox.Focus();
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الحالة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void LoadCustomerTypesAsync()
        {
            try
            {
                var customerTypes = App.DatabaseContext.CustomerTypes
                    .Where(ct => ct.IsActive)
                    .OrderBy(ct => ct.Name)
                    .ToList();

                CustomerTypeComboBox.Items.Clear();

                foreach (var type in customerTypes)
                {
                    CustomerTypeComboBox.Items.Add(type.Name);
                }

                // إضافة أنواع افتراضية إذا لم توجد
                if (!customerTypes.Any())
                {
                    CustomerTypeComboBox.Items.Add("مستشفى حكومي");
                    CustomerTypeComboBox.Items.Add("مستشفى خاص");
                    CustomerTypeComboBox.Items.Add("عيادة طبية");
                    CustomerTypeComboBox.Items.Add("مركز طبي");
                    CustomerTypeComboBox.Items.Add("صيدلية");
                    CustomerTypeComboBox.Items.Add("مختبر طبي");
                    CustomerTypeComboBox.Items.Add("عميل فردي");
                    CustomerTypeComboBox.Items.Add("موزع طبي");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أنواع العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCitiesAsync()
        {
            try
            {
                var cities = App.DatabaseContext.Cities
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Country)
                    .ThenBy(c => c.Name)
                    .ToList();

                CityComboBox.Items.Clear();

                foreach (var city in cities)
                {
                    CityComboBox.Items.Add($"{city.Name} - {city.Country}");
                }

                // إضافة مدن افتراضية إذا لم توجد
                if (!cities.Any())
                {
                    CityComboBox.Items.Add("بغداد - العراق");
                    CityComboBox.Items.Add("البصرة - العراق");
                    CityComboBox.Items.Add("الموصل - العراق");
                    CityComboBox.Items.Add("الرياض - السعودية");
                    CityComboBox.Items.Add("جدة - السعودية");
                    CityComboBox.Items.Add("الكويت - الكويت");
                    CityComboBox.Items.Add("الدوحة - قطر");
                    CityComboBox.Items.Add("دبي - الإمارات");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المدن: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageCustomerTypesBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageTypesWindow = new ManageCustomerTypesWindow();
            if (manageTypesWindow.ShowDialog() == true)
            {
                LoadCustomerTypesAsync();
            }
        }

        private void ManageCitiesBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageCitiesWindow = new ManageCitiesWindow();
            if (manageCitiesWindow.ShowDialog() == true)
            {
                LoadCitiesAsync();
            }
        }
    }
}
