<Window x:Class="MedicalDevicesManager.Windows.SelectSerialNumberWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار الرقم التسلسلي" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">
    
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel>
                <TextBlock x:Name="DeviceNameText" Text="اختيار الرقم التسلسلي للجهاز" 
                          FontSize="20" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="اختر الرقم التسلسلي المحدد لعرض المعلومات الخاصة به" 
                          FontSize="14" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                
                <!-- معلومات الجهاز -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                        CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📱 معلومات الجهاز" FontSize="16" FontWeight="Bold" 
                                  Foreground="#1976D2" Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock x:Name="DeviceNameInfo" Text="اسم الجهاز: --" Margin="0,5,0,0"/>
                                <TextBlock x:Name="DeviceBrandInfo" Text="الماركة: --" Margin="0,5,0,0"/>
                                <TextBlock x:Name="DeviceModelInfo" Text="الموديل: --" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock x:Name="DeviceCategoryInfo" Text="الفئة: --" Margin="0,5,0,0"/>
                                <TextBlock x:Name="DeviceStatusInfo" Text="الحالة: --" Margin="0,5,0,0"/>
                                <TextBlock x:Name="SerialCountInfo" Text="عدد الأرقام التسلسلية: --" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- قائمة الأرقام التسلسلية -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                        CornerRadius="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="🔢 الأرقام التسلسلية المتاحة" FontSize="16" FontWeight="Bold" 
                                  Foreground="#1976D2" Margin="0,0,0,15"/>
                        
                        <!-- خيار عرض جميع البيانات -->
                        <Border Background="#E8F5E8" BorderBrush="#4CAF50" BorderThickness="2" 
                                CornerRadius="8" Padding="15" Margin="0,0,0,15" 
                                MouseLeftButtonUp="AllDataOption_Click" Cursor="Hand">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="📊" FontSize="24" VerticalAlignment="Center" Margin="0,0,15,0"/>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="عرض جميع البيانات (جميع الأرقام التسلسلية)" 
                                              FontWeight="Bold" FontSize="16" Foreground="#2E7D32"/>
                                    <TextBlock Text="عرض جميع سجلات الصيانة والمبيعات والتنصيبات لجميع الأرقام التسلسلية للجهاز" 
                                              FontSize="12" Foreground="#666" TextWrapping="Wrap" Margin="0,5,0,0"/>
                                </StackPanel>
                                
                                <TextBlock Grid.Column="2" Text="👆" FontSize="20" VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                        
                        <!-- قائمة الأرقام التسلسلية -->
                        <ItemsControl x:Name="SerialNumbersList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                                            CornerRadius="8" Padding="15" Margin="0,0,0,10" 
                                            MouseLeftButtonUp="SerialNumberOption_Click" Cursor="Hand"
                                            Tag="{Binding}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="🔢" FontSize="20" VerticalAlignment="Center" Margin="0,0,15,0"/>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding SerialNumber}" FontWeight="Bold" FontSize="16"/>
                                                <TextBlock Text="{Binding ComponentName}" FontSize="14" Foreground="#666" Margin="0,2,0,0"/>
                                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                                    <TextBlock Text="النوع: " FontSize="12" Foreground="#666"/>
                                                    <TextBlock Text="{Binding ComponentType}" FontSize="12" Foreground="#666"/>
                                                    <TextBlock Text=" • الحالة: " FontSize="12" Foreground="#666" Margin="10,0,0,0"/>
                                                    <TextBlock Text="{Binding Status}" FontSize="12" Foreground="#666"/>
                                                </StackPanel>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="2" Margin="10,0,10,0">
                                                <TextBlock FontSize="12" Foreground="#FF5722" HorizontalAlignment="Center">
                                                    <Run Text="{Binding MaintenanceCount}"/>
                                                    <Run Text=" صيانة"/>
                                                </TextBlock>
                                                <TextBlock FontSize="12" Foreground="#4CAF50" HorizontalAlignment="Center" Margin="0,2,0,0">
                                                    <Run Text="{Binding SalesCount}"/>
                                                    <Run Text=" مبيعة"/>
                                                </TextBlock>
                                            </StackPanel>
                                            
                                            <TextBlock Grid.Column="3" Text="👆" FontSize="16" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        
                        <!-- رسالة في حالة عدم وجود أرقام تسلسلية -->
                        <Border x:Name="NoSerialNumbersMessage" Background="#FFF3CD" BorderBrush="#FFC107" BorderThickness="1" 
                                CornerRadius="8" Padding="15" Visibility="Collapsed">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="⚠️" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <StackPanel>
                                    <TextBlock Text="لا توجد أرقام تسلسلية مسجلة لهذا الجهاز" FontWeight="Bold"/>
                                    <TextBlock Text="يمكنك إضافة أرقام تسلسلية من نافذة تفاصيل الجهاز" FontSize="12" Margin="0,5,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                        
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#1976D2" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="اختر رقماً تسلسلياً لعرض المعلومات الخاصة به فقط" 
                          Foreground="White" VerticalAlignment="Center"/>
                
                <Button Grid.Column="1" Content="❌ إلغاء" Padding="15,8"
                        Background="#F44336" Foreground="White" BorderThickness="0"
                        FontWeight="Bold" Click="Cancel_Click"/>
            </Grid>
        </Border>
    </Grid>
</Window>
