using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class QuotationWindow : Window
    {
        private List<QuotationItem> _quotationItems;
        
        public QuotationWindow()
        {
            InitializeComponent();
            LoadQuotationData();
        }

        private void LoadQuotationData()
        {
            // إنشاء بيانات العروض
            _quotationItems = new List<QuotationItem>
            {
                new QuotationItem
                {
                    Id = 1,
                    DeviceName = "جهاز تخطيط القلب شيلر AT-102",
                    Brand = "Schiller",
                    Model = "AT-102",
                    Quantity = 3,
                    UnitPrice = 30000,
                    TotalPrice = 90000,
                    DiscountAmount = 4500,
                    FinalPrice = 85500,
                    DeliveryDays = 7,
                    WarrantyYears = 2,
                    Features = string.Join(";", new List<string>
                    {
                        "12 قناة رقمية",
                        "شاشة ملونة 7 بوصة",
                        "طباعة حرارية عالية الجودة",
                        "ذاكرة تخزين 1000 تخطيط",
                        "ضمان شامل لمدة سنتين",
                        "تدريب مجاني للفريق الطبي",
                        "صيانة مجانية لمدة 6 أشهر",
                        "توصيل وتركيب مجاني"
                    }),
                    Status = "متوفر",
                    Supplier = "شيلر الطبية السعودية"
                },
                new QuotationItem
                {
                    Id = 2,
                    DeviceName = "جهاز تخطيط القلب فيليبس PageWriter TC70",
                    Brand = "Philips",
                    Model = "PageWriter TC70",
                    Quantity = 3,
                    UnitPrice = 45000,
                    TotalPrice = 135000,
                    DiscountAmount = 10800,
                    FinalPrice = 124200,
                    DeliveryDays = 10,
                    WarrantyYears = 3,
                    Features = string.Join(";", new List<string>
                    {
                        "12 قناة رقمية متقدمة",
                        "شاشة لمس 10 بوصة",
                        "اتصال لاسلكي WiFi",
                        "تحليل تلقائي للنتائج",
                        "ضمان شامل لمدة 3 سنوات",
                        "تدريب متقدم للفريق الطبي",
                        "صيانة مجانية لمدة سنة كاملة",
                        "دعم فني 24/7",
                        "برنامج إدارة البيانات مجاني"
                    }),
                    Status = "متوفر",
                    Supplier = "شركة فيليبس الطبية"
                },
                new QuotationItem
                {
                    Id = 3,
                    DeviceName = "جهاز تخطيط القلب جنرال إلكتريك MAC 2000",
                    Brand = "GE Healthcare",
                    Model = "MAC 2000",
                    Quantity = 3,
                    UnitPrice = 38000,
                    TotalPrice = 114000,
                    DiscountAmount = 7980,
                    FinalPrice = 106020,
                    DeliveryDays = 14,
                    WarrantyYears = 2.5,
                    Features = string.Join(";", new List<string>
                    {
                        "12 قناة عالية الدقة",
                        "شاشة ملونة 8 بوصة",
                        "طباعة ليزر عالية الجودة",
                        "ذاكرة تخزين 2000 تخطيط",
                        "ضمان شامل لمدة سنتين ونصف",
                        "تدريب شامل للفريق الطبي",
                        "صيانة مجانية لمدة 8 أشهر",
                        "توصيل وتركيب مجاني",
                        "قطع غيار مجانية لسنة"
                    }),
                    Status = "طلب مسبق",
                    Supplier = "جنرال إلكتريك الطبية"
                }
            };
        }

        private async void PrintQuotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء تقرير العرض
                var quotationReport = GenerateQuotationReport();
                
                // هنا يمكن إضافة كود الطباعة الفعلي
                MessageBox.Show("تم إنشاء تقرير العرض بنجاح!\n\nسيتم طباعة العرض مع جميع التفاصيل والمواصفات.", 
                    "طباعة العرض", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // حفظ سجل الطباعة
                await SaveQuotationActivity("طباعة العرض");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void EmailQuotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء نافذة إرسال البريد الإلكتروني
                var emailWindow = new EmailQuotationWindow(_quotationItems);
                if (emailWindow.ShowDialog() == true)
                {
                    MessageBox.Show("تم إرسال العرض بالبريد الإلكتروني بنجاح!", 
                        "إرسال العرض", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // حفظ سجل الإرسال
                    await SaveQuotationActivity($"إرسال العرض بالبريد إلى {emailWindow.RecipientEmail}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveQuotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var quotationNumber = GenerateQuotationNumber();
                var totalAmount = _quotationItems.Sum(q => q.FinalPrice);

                MessageBox.Show($"تم حفظ العرض بنجاح!\n\nرقم العرض: {quotationNumber}\nإجمالي المبلغ: {totalAmount:N0} ريال\n\nملاحظة: سيتم حفظ العرض في قاعدة البيانات عند إضافة الجداول المطلوبة.",
                    "حفظ العرض", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private string GenerateQuotationReport()
        {
            var report = "=== عرض أسعار أجهزة تخطيط القلب 12 قناة ===\n\n";
            report += $"تاريخ العرض: {DateTime.Now:yyyy/MM/dd}\n";
            report += $"العدد المطلوب: 3 أجهزة\n\n";

            foreach (var item in _quotationItems)
            {
                report += $"--- {item.DeviceName} ---\n";
                report += $"الماركة: {item.Brand}\n";
                report += $"الموديل: {item.Model}\n";
                report += $"سعر الوحدة: {item.UnitPrice:N0} ريال\n";
                report += $"المجموع الفرعي: {item.TotalPrice:N0} ريال\n";
                report += $"الخصم: {item.DiscountAmount:N0} ريال\n";
                report += $"المجموع النهائي: {item.FinalPrice:N0} ريال\n";
                report += $"مدة التسليم: {item.DeliveryDays} أيام\n";
                report += $"الضمان: {item.WarrantyYears} سنة\n";
                report += $"الحالة: {item.Status}\n\n";
            }

            report += $"إجمالي العرض: {_quotationItems.Sum(q => q.FinalPrice):N0} ريال\n";
            report += "العرض صالح لمدة 30 يوماً من تاريخ الإصدار\n";

            return report;
        }

        private async System.Threading.Tasks.Task SaveQuotationActivity(string activity)
        {
            try
            {
                // سيتم حفظ سجل النشاط عند إضافة الجداول المطلوبة
                Console.WriteLine($"نشاط العرض: {activity}");
                await System.Threading.Tasks.Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حفظ سجل النشاط: {ex.Message}");
            }
        }

        private string GenerateQuotationNumber()
        {
            return $"QUO-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
        }
    }



    // نافذة إرسال البريد الإلكتروني (مبسطة)
    public class EmailQuotationWindow : Window
    {
        public string RecipientEmail { get; private set; } = "";
        private List<QuotationItem> _quotationItems;

        public EmailQuotationWindow(List<QuotationItem> quotationItems)
        {
            _quotationItems = quotationItems;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Title = "إرسال العرض بالبريد الإلكتروني";
            Width = 400;
            Height = 200;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            FlowDirection = FlowDirection.RightToLeft;

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var emailLabel = new Label { Content = "البريد الإلكتروني:", Margin = new Thickness(10, 10, 10, 10) };
            Grid.SetRow(emailLabel, 0);
            grid.Children.Add(emailLabel);

            var emailTextBox = new TextBox { Margin = new Thickness(10, 10, 10, 10), Name = "EmailTextBox" };
            Grid.SetRow(emailTextBox, 1);
            grid.Children.Add(emailTextBox);

            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Center, Margin = new Thickness(10, 10, 10, 10) };
            var sendButton = new Button { Content = "إرسال", Margin = new Thickness(5, 5, 5, 5), Padding = new Thickness(15, 5, 15, 5) };
            var cancelButton = new Button { Content = "إلغاء", Margin = new Thickness(5, 5, 5, 5), Padding = new Thickness(15, 5, 15, 5) };

            sendButton.Click += (s, e) => {
                RecipientEmail = emailTextBox.Text;
                if (!string.IsNullOrWhiteSpace(RecipientEmail))
                {
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("يرجى إدخال البريد الإلكتروني", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            };

            cancelButton.Click += (s, e) => {
                DialogResult = false;
                Close();
            };

            buttonPanel.Children.Add(sendButton);
            buttonPanel.Children.Add(cancelButton);
            Grid.SetRow(buttonPanel, 2);
            grid.Children.Add(buttonPanel);

            Content = grid;
        }
    }
}
