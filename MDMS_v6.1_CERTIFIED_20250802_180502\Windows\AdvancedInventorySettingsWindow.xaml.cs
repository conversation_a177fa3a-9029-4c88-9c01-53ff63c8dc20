using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using MedicalDevicesManager.Services;

namespace MedicalDevicesManager.Windows
{
    public partial class AdvancedInventorySettingsWindow : Window
    {
        private List<InventorySettings> _allSettings;
        private InventorySettings _editingSettings = null;
        private NotificationService _notificationService;
        
        public AdvancedInventorySettingsWindow()
        {
            InitializeComponent();
            _notificationService = new NotificationService(App.DatabaseContext);
            LoadSettingsAsync();
        }
        
        private async void LoadSettingsAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري التحميل...";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                
                _allSettings = await App.DatabaseContext.InventorySettings
                    .Include(s => s.InventoryItem)
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.InventoryItem.Name)
                    .ToListAsync();
                    
                InventorySettingsDataGrid.ItemsSource = _allSettings;
                
                StatusTextBlock.Text = "جاهز";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في التحميل";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"خطأ في تحميل إعدادات المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadSettingsAsync();
            ClearForm();
        }
        
        private async void ApplyDefaultsBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد تطبيق الإعدادات الافتراضية على جميع عناصر المخزون التي لا تحتوي على إعدادات؟",
                "تأكيد تطبيق الافتراضي",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question
            );
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    StatusTextBlock.Text = "جاري تطبيق الإعدادات الافتراضية...";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                    
                    // الحصول على عناصر المخزون التي لا تحتوي على إعدادات
                    var inventoryItemsWithoutSettings = await App.DatabaseContext.InventoryItems
                        .Where(i => !App.DatabaseContext.InventorySettings.Any(s => s.InventoryItemId == i.Id))
                        .ToListAsync();
                    
                    foreach (var item in inventoryItemsWithoutSettings)
                    {
                        var defaultSettings = new InventorySettings
                        {
                            InventoryItemId = item.Id,
                            MinimumStock = Math.Max(1, item.CurrentStock / 10), // 10% من المخزون الحالي
                            ReorderLevel = Math.Max(2, item.CurrentStock / 5), // 20% من المخزون الحالي
                            MaximumStock = item.CurrentStock * 2, // ضعف المخزون الحالي
                            EnableLowStockAlert = true,
                            EnableExpiryAlert = true,
                            ExpiryAlertDays = 30,
                            AlertEmail = "",
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            LastUpdated = DateTime.Now
                        };
                        
                        App.DatabaseContext.InventorySettings.Add(defaultSettings);
                    }
                    
                    await App.DatabaseContext.SaveChangesAsync();
                    
                    StatusTextBlock.Text = "تم تطبيق الإعدادات الافتراضية";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                    
                    MessageBox.Show(
                        $"تم تطبيق الإعدادات الافتراضية على {inventoryItemsWithoutSettings.Count} عنصر مخزون",
                        "نجح التطبيق",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );
                    
                    LoadSettingsAsync();
                }
                catch (Exception ex)
                {
                    StatusTextBlock.Text = "خطأ في تطبيق الإعدادات";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                    MessageBox.Show($"خطأ في تطبيق الإعدادات الافتراضية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private async void CheckAlertsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري فحص التنبيهات...";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                
                CheckAlertsBtn.IsEnabled = false;
                CheckAlertsBtn.Content = "🔄 جاري الفحص...";
                
                // فحص المخزون المنخفض
                await _notificationService.CheckLowStockAsync();
                
                // فحص انتهاء الصلاحية
                await _notificationService.CheckExpiryDatesAsync();
                
                StatusTextBlock.Text = "تم فحص التنبيهات";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                
                MessageBox.Show("تم فحص جميع التنبيهات وإنشاء الإشعارات اللازمة", "اكتمل الفحص", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في فحص التنبيهات";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"خطأ في فحص التنبيهات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                CheckAlertsBtn.IsEnabled = true;
                CheckAlertsBtn.Content = "🔔 فحص التنبيهات";
            }
        }
        
        private void InventorySettingsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (InventorySettingsDataGrid.SelectedItem is InventorySettings selectedSettings)
            {
                LoadSettingsToForm(selectedSettings);
                SettingsPanel.Visibility = Visibility.Visible;
            }
            else
            {
                SettingsPanel.Visibility = Visibility.Collapsed;
            }
        }
        
        private void EditSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var settings = button?.DataContext as InventorySettings;
            
            if (settings != null)
            {
                InventorySettingsDataGrid.SelectedItem = settings;
                LoadSettingsToForm(settings);
                SettingsPanel.Visibility = Visibility.Visible;
            }
        }
        
        private async void DeleteSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var settings = button?.DataContext as InventorySettings;
            
            if (settings != null)
            {
                var result = MessageBox.Show(
                    $"هل تريد حذف إعدادات المخزون للمنتج '{settings.InventoryItem?.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        settings.IsActive = false;
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف الإعدادات بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadSettingsAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private void LoadSettingsToForm(InventorySettings settings)
        {
            _editingSettings = settings;
            
            ProductNameTextBlock.Text = settings.InventoryItem?.Name ?? "";
            CurrentStockTextBlock.Text = settings.InventoryItem?.CurrentStock.ToString() ?? "0";
            
            MinimumStockTextBox.Text = settings.MinimumStock.ToString();
            ReorderLevelTextBox.Text = settings.ReorderLevel.ToString();
            MaximumStockTextBox.Text = settings.MaximumStock.ToString();
            
            EnableLowStockAlertCheckBox.IsChecked = settings.EnableLowStockAlert;
            EnableExpiryAlertCheckBox.IsChecked = settings.EnableExpiryAlert;
            ExpiryAlertDaysTextBox.Text = settings.ExpiryAlertDays.ToString();
            AlertEmailTextBox.Text = settings.AlertEmail;
        }
        
        private void ClearForm()
        {
            _editingSettings = null;
            SettingsPanel.Visibility = Visibility.Collapsed;
            
            ProductNameTextBlock.Text = "";
            CurrentStockTextBlock.Text = "";
            MinimumStockTextBox.Text = "";
            ReorderLevelTextBox.Text = "";
            MaximumStockTextBox.Text = "";
            EnableLowStockAlertCheckBox.IsChecked = true;
            EnableExpiryAlertCheckBox.IsChecked = true;
            ExpiryAlertDaysTextBox.Text = "30";
            AlertEmailTextBox.Text = "";
        }
        
        private async void SaveSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            if (_editingSettings == null)
            {
                MessageBox.Show("لم يتم تحديد إعدادات للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (!ValidateForm())
                return;
                
            try
            {
                _editingSettings.MinimumStock = int.Parse(MinimumStockTextBox.Text);
                _editingSettings.ReorderLevel = int.Parse(ReorderLevelTextBox.Text);
                _editingSettings.MaximumStock = int.Parse(MaximumStockTextBox.Text);
                _editingSettings.EnableLowStockAlert = EnableLowStockAlertCheckBox.IsChecked ?? false;
                _editingSettings.EnableExpiryAlert = EnableExpiryAlertCheckBox.IsChecked ?? false;
                _editingSettings.ExpiryAlertDays = int.Parse(ExpiryAlertDaysTextBox.Text);
                _editingSettings.AlertEmail = AlertEmailTextBox.Text.Trim();
                _editingSettings.LastUpdated = DateTime.Now;
                
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                LoadSettingsAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private bool ValidateForm()
        {
            if (!int.TryParse(MinimumStockTextBox.Text, out int minStock) || minStock < 0)
            {
                MessageBox.Show("يرجى إدخال رقم صحيح للحد الأدنى", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                MinimumStockTextBox.Focus();
                return false;
            }
            
            if (!int.TryParse(ReorderLevelTextBox.Text, out int reorderLevel) || reorderLevel < 0)
            {
                MessageBox.Show("يرجى إدخال رقم صحيح لمستوى إعادة الطلب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ReorderLevelTextBox.Focus();
                return false;
            }
            
            if (!int.TryParse(MaximumStockTextBox.Text, out int maxStock) || maxStock < 0)
            {
                MessageBox.Show("يرجى إدخال رقم صحيح للحد الأقصى", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                MaximumStockTextBox.Focus();
                return false;
            }
            
            if (!int.TryParse(ExpiryAlertDaysTextBox.Text, out int alertDays) || alertDays < 1)
            {
                MessageBox.Show("يرجى إدخال رقم صحيح لأيام التنبيه (أكبر من 0)", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ExpiryAlertDaysTextBox.Focus();
                return false;
            }
            
            if (minStock > reorderLevel)
            {
                MessageBox.Show("مستوى إعادة الطلب يجب أن يكون أكبر من أو يساوي الحد الأدنى", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (reorderLevel > maxStock)
            {
                MessageBox.Show("الحد الأقصى يجب أن يكون أكبر من أو يساوي مستوى إعادة الطلب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
    }
}
