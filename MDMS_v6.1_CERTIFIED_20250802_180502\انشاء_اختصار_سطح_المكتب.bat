@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    إنشاء اختصار نظام إدارة الأجهزة الطبية
echo    Creating Medical Devices System Shortcut
echo ========================================
echo.

echo 🔧 جاري إنشاء اختصار على سطح المكتب...
echo Creating desktop shortcut...
echo.

REM الحصول على مسار المجلد الحالي
set "CURRENT_DIR=%~dp0"

REM الحصول على مسار سطح المكتب
set "DESKTOP=%USERPROFILE%\Desktop"

REM إنشاء ملف batch على سطح المكتب
echo @echo off > "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo chcp 65001 ^>nul 2^>^&1 >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo cls >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo. >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo ======================================== >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo    نظام إدارة الأجهزة الطبية المتكامل >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo    Medical Devices Management System >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo ======================================== >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo. >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo 🚀 تشغيل النظام مع التحسينات العربية... >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo Starting system with Arabic improvements... >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo. >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo cd /d "%CURRENT_DIR%" >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo dotnet build --configuration Release --verbosity quiet >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo if %%ERRORLEVEL%% NEQ 0 ^( >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo     echo ❌ فشل في بناء المشروع >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo     pause >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo     exit /b 1 >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo ^) >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo ✅ تم البناء بنجاح - النظام جاهز للتشغيل >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo Build successful - System ready to run >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo. >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo start "" dotnet run --configuration Release >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo ✅ تم تشغيل النظام بنجاح! >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo System started successfully! >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo. >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo 💡 النظام يعمل الآن في الخلفية >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo System is now running in background >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo echo. >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"
echo timeout /t 3 /nobreak ^>nul >> "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat"

REM التحقق من إنشاء الاختصار
if exist "%DESKTOP%\نظام إدارة الأجهزة الطبية.bat" (
    echo ✅ تم إنشاء الاختصار بنجاح!
    echo ✅ Shortcut created successfully!
    echo.
    echo 📍 موقع الاختصار:
    echo 📍 Shortcut location:
    echo %DESKTOP%\نظام إدارة الأجهزة الطبية.bat
    echo.
    echo 🎯 يمكنك الآن النقر مرتين على الاختصار لتشغيل النظام
    echo 🎯 You can now double-click the shortcut to run the system
) else (
    echo ❌ فشل في إنشاء الاختصار
    echo ❌ Failed to create shortcut
)

echo.
echo ========================================
echo اضغط أي مفتاح للمتابعة...
pause >nul
