@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    🏥 نظام إدارة الأجهزة الطبية المتكامل
echo    Medical Devices Management System
echo ========================================
echo.
echo 🚀 تشغيل النظام مع التحسينات العربية الجديدة...
echo Starting system with new Arabic improvements...
echo.

REM الانتقال إلى مجلد المشروع
cd /d "c:\Users\<USER>\Desktop\Advanced System File 082025"

REM التحقق من وجود .NET
where dotnet >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET غير مثبت على النظام
    echo Please install .NET 6.0 or later
    pause
    exit /b 1
)

echo ✅ جاري بناء المشروع...
echo Building project...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo Build failed
    pause
    exit /b 1
)

echo ✅ تم البناء بنجاح
echo Build successful
echo.
echo 🎨 التحسينات العربية المطبقة:
echo Arabic improvements applied:
echo   ✅ واجهة عربية كاملة مع اتجاه RTL
echo   ✅ خطوط عربية محسنة وواضحة
echo   ✅ تخطيط محسن للنماذج والجداول
echo   ✅ أزرار وقوائم منسدلة محسنة
echo   ✅ تجربة مستخدم احترافية
echo.
echo 🚀 تشغيل النظام...
echo Starting system...

start "" dotnet run --configuration Release

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo System started successfully!
echo.
echo 💡 النظام يعمل الآن - يمكنك إغلاق هذه النافذة
echo System is now running - you can close this window
echo.
echo 🎯 نصائح للاستخدام:
echo Usage tips:
echo   • اضغط "تشخيص النظام" للتحقق من البيانات
echo   • جرب النوافذ المختلفة لرؤية التحسينات
echo   • استخدم البحث والفلترة في الجداول
echo.
timeout /t 5 /nobreak >nul
