using System;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager
{
    public class MedicalDevicesContext : DbContext
    {
        public DbSet<MedicalDevice> MedicalDevices { get; set; }
        public DbSet<DeviceCategory> DeviceCategories { get; set; }
        public DbSet<DeviceSerialNumber> DeviceSerialNumbers { get; set; }
        public DbSet<DeviceInstallation> DeviceInstallations { get; set; }
        public DbSet<InventoryItem> InventoryItems { get; set; }
        public DbSet<InventoryCategory> InventoryCategories { get; set; }
        public DbSet<InventoryMovement> InventoryMovements { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerType> CustomerTypes { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Sale> Sales { get; set; }
        public DbSet<Shipment> Shipments { get; set; }
        public DbSet<RecipientName> RecipientNames { get; set; }
        public DbSet<MaintenanceRecord> MaintenanceRecords { get; set; }
        public DbSet<TechnicianName> TechnicianNames { get; set; }
        public DbSet<City> Cities { get; set; }
        public DbSet<SparePart> SpareParts { get; set; }

        // الجداول الجديدة للوظائف المتقدمة
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<BackupRecord> BackupRecords { get; set; }
        public DbSet<ExportRecord> ExportRecords { get; set; }
        public DbSet<InventorySettings> InventorySettings { get; set; }

        // الجداول الجديدة للنظام الذكي
        public DbSet<SmartAlert> SmartAlerts { get; set; }
        public DbSet<AlertSettings> AlertSettings { get; set; }
        public DbSet<SearchIndex> SearchIndexes { get; set; }
        public DbSet<LiveStatistics> LiveStatistics { get; set; }

        // جداول عروض الأسعار والأنشطة
        public DbSet<Quotation> Quotations { get; set; }
        public DbSet<QuotationItem> QuotationItems { get; set; }
        public DbSet<ActivityLog> ActivityLogs { get; set; }
        
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlite("Data Source=MedicalDevicesIntegrated.db");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // تكوين الجداول
            modelBuilder.Entity<MedicalDevice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.UserManualPath).HasDefaultValue("");
                entity.Property(e => e.MaintenanceManualPath).HasDefaultValue("");
                entity.Property(e => e.OriginCertificatePath).HasDefaultValue("");
                entity.Property(e => e.QualityCertificatePath).HasDefaultValue("");
                entity.Property(e => e.OfficialCertificationsPath).HasDefaultValue("");
                entity.Property(e => e.TechnicalInfoBookletPath).HasDefaultValue("");
                entity.Property(e => e.ImportPapersPath).HasDefaultValue("");
                entity.Property(e => e.ContractPapersPath).HasDefaultValue("");
            });

            modelBuilder.Entity<DeviceCategory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<DeviceSerialNumber>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SerialNumber).IsRequired();
                entity.Property(e => e.ComponentName).IsRequired();
                entity.Property(e => e.ComponentType).HasDefaultValue("رئيسي");
                entity.Property(e => e.Status).HasDefaultValue("نشط");
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                // تكوين العلاقة مع الجهاز الطبي
                entity.HasOne(e => e.MedicalDevice)
                      .WithMany()
                      .HasForeignKey(e => e.DeviceId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // تكوين باقي الجداول
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Status).HasDefaultValue("نشط");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<Sale>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired();
                entity.Property(e => e.PaymentStatus).HasDefaultValue("غير مدفوع");
                entity.Property(e => e.PaymentMethod).HasDefaultValue("نقدي");
            });

            modelBuilder.Entity<InventoryItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Status).HasDefaultValue("متاح");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Status).HasDefaultValue("نشط");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<DeviceInstallation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DeviceName).IsRequired();
                entity.Property(e => e.InstallationStatus).HasDefaultValue("مجدول");
                entity.Property(e => e.InstallationReportBookletPath).HasDefaultValue("");
                entity.Property(e => e.SupplierCompany).HasDefaultValue("");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<MaintenanceRecord>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DeviceName).IsRequired();
                entity.Property(e => e.Status).HasDefaultValue("مجدولة");
                entity.Property(e => e.ReportBookletPath).HasDefaultValue("");
                entity.Property(e => e.DocumentsPath).HasDefaultValue("");
                entity.Property(e => e.SupplierCompany).HasDefaultValue("");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<Shipment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ShipmentNumber).IsRequired();
                entity.Property(e => e.Status).HasDefaultValue("قيد التحضير");
                entity.Property(e => e.ShipmentDocumentsPath).HasDefaultValue("");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            // تكوين الجداول الجديدة للنظام الذكي
            modelBuilder.Entity<SmartAlert>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AlertType).IsRequired();
                entity.Property(e => e.Title).IsRequired();
                entity.Property(e => e.Status).HasDefaultValue("نشط");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<SearchIndex>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ItemType).IsRequired();
                entity.Property(e => e.SearchableText).IsRequired();
                entity.Property(e => e.SearchCount).HasDefaultValue(0);
            });

            modelBuilder.Entity<LiveStatistics>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.StatType).IsRequired();
                entity.Property(e => e.StatName).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<AlertSettings>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AlertType).IsRequired();
                entity.Property(e => e.IsEnabled).HasDefaultValue(true);
            });

            // تكوين الجداول المساعدة
            modelBuilder.Entity<InventoryCategory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<CustomerType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<City>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<RecipientName>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<TechnicianName>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<SparePart>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.AssociatedDeviceName).IsRequired();
                entity.Property(e => e.WorkshopStorageLocation).HasDefaultValue("");
                entity.Property(e => e.PurchasePrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.IndividualSellingPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.WholesaleSellingPrice).HasColumnType("decimal(18,2)");
            });

            // تكوين جداول عروض الأسعار
            modelBuilder.Entity<Quotation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.QuotationNumber).IsRequired();
                entity.Property(e => e.CustomerName).IsRequired();
                entity.Property(e => e.Status).HasDefaultValue("مسودة");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.FinalAmount).HasColumnType("decimal(18,2)");
            });

            modelBuilder.Entity<QuotationItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DeviceName).IsRequired();
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.FinalPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Status).HasDefaultValue("متوفر");

                // تكوين العلاقة مع العرض
                entity.HasOne(e => e.Quotation)
                      .WithMany()
                      .HasForeignKey(e => e.QuotationId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ActivityLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ActivityType).IsRequired();
                entity.Property(e => e.Description).IsRequired();
                entity.Property(e => e.UserName).IsRequired();
                entity.Property(e => e.IsSuccess).HasDefaultValue(true);
            });
        }
    }
}
