using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using System.Text;

namespace MedicalDevicesManager.Windows
{
    public partial class SimpleExportQuotationWindow : Window
    {
        private List<QuotationItemDisplay> _quotationItems;
        private string _quotationNumber;
        private DateTime _quotationDate;
        private decimal _subtotal;
        private decimal _tax;
        private decimal _grandTotal;

        public SimpleExportQuotationWindow()
        {
            InitializeComponent();
            _quotationItems = new List<QuotationItemDisplay>();
            GenerateQuotation();
        }

        private async void GenerateQuotation()
        {
            try
            {
                StatusText.Text = "جاري إنشاء العرض...";
                
                await Task.Run(async () =>
                {
                    try
                    {
                        // إنشاء رقم العرض وتاريخه
                        _quotationNumber = $"QUO-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
                        _quotationDate = DateTime.Now;

                        // الحصول على عينة من الأجهزة والمخزون
                        var devices = await App.DatabaseContext.MedicalDevices
                            .Where(d => d.Status == "متاح")
                            .Take(8)
                            .ToListAsync();

                        var inventory = await App.DatabaseContext.InventoryItems
                            .Where(i => i.Status == "متاح" && i.CurrentStock > 0)
                            .Take(7)
                            .ToListAsync();

                        var items = new List<QuotationItemDisplay>();
                        var itemNumber = 1;

                        // إضافة الأجهزة
                        foreach (var device in devices)
                        {
                            items.Add(new QuotationItemDisplay
                            {
                                ItemNumber = itemNumber++,
                                Name = device.Name,
                                Category = device.Category,
                                Description = $"{device.Brand} - {device.Model}",
                                UnitPrice = device.SellingPrice,
                                Quantity = 1
                            });
                        }

                        // إضافة المخزون
                        foreach (var item in inventory)
                        {
                            items.Add(new QuotationItemDisplay
                            {
                                ItemNumber = itemNumber++,
                                Name = item.Name,
                                Category = item.Category,
                                Description = item.Description,
                                UnitPrice = item.UnitPrice,
                                Quantity = 1
                            });
                        }

                        // حساب المبالغ
                        _subtotal = items.Sum(i => i.TotalPrice);
                        _tax = _subtotal * 0.15m;
                        _grandTotal = _subtotal + _tax;

                        Dispatcher.Invoke(() =>
                        {
                            _quotationItems = items;
                            UpdateDisplay();
                            StatusText.Text = "تم إنشاء العرض بنجاح";
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            StatusText.Text = "خطأ في إنشاء العرض";
                            MessageBox.Show($"خطأ في إنشاء العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                StatusText.Text = "خطأ في النظام";
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateDisplay()
        {
            // تحديث معلومات العرض
            QuotationDateText.Text = $"📅 تاريخ العرض: {_quotationDate:yyyy/MM/dd}";
            QuotationNumberText.Text = $"🔢 رقم العرض: {_quotationNumber}";
            ItemsCountText.Text = $"📦 عدد العناصر: {_quotationItems.Count}";
            CategoriesText.Text = $"📂 الفئات: {_quotationItems.Select(i => i.Category).Distinct().Count()}";
            TotalAmountText.Text = $"💰 المبلغ الإجمالي: {_grandTotal:N0} ريال";
            ValidityText.Text = $"⏰ صالح حتى: {_quotationDate.AddDays(30):yyyy/MM/dd}";

            // تحديث ملخص المبالغ
            SubtotalText.Text = $"المجموع الفرعي: {_subtotal:N0} ريال";
            TaxText.Text = $"ضريبة القيمة المضافة (15%): {_tax:N0} ريال";
            GrandTotalText.Text = $"المجموع الإجمالي: {_grandTotal:N0} ريال";

            // تحديث الجدول
            ItemsDataGrid.ItemsSource = _quotationItems;
        }

        private void GenerateQuotationButton_Click(object sender, RoutedEventArgs e)
        {
            GenerateQuotation();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "جاري التحضير للطباعة...";
                
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء مستند للطباعة
                    var printContent = CreatePrintContent();
                    printDialog.PrintDocument(((System.Windows.Documents.IDocumentPaginatorSource)printContent).DocumentPaginator, "عرض الأسعار");
                    
                    StatusText.Text = "تم إرسال المستند للطباعة";
                    MessageBox.Show("تم إرسال عرض الأسعار للطباعة بنجاح!", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusText.Text = "تم إلغاء الطباعة";
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "خطأ في الطباعة";
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SavePdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Text Files (*.txt)|*.txt",
                    FileName = $"عرض_أسعار_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var content = GenerateTextContent();
                    File.WriteAllText(saveDialog.FileName, content, Encoding.UTF8);
                    
                    StatusText.Text = "تم حفظ الملف بنجاح";
                    MessageBox.Show($"تم حفظ عرض الأسعار في:\n{saveDialog.FileName}\n\nملاحظة: تم الحفظ كملف نصي لضمان التوافق", "حفظ الملف", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // فتح الملف
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "خطأ في الحفظ";
                MessageBox.Show($"خطأ في حفظ الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv",
                    FileName = $"عرض_أسعار_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var content = GenerateCsvContent();
                    File.WriteAllText(saveDialog.FileName, content, Encoding.UTF8);
                    
                    StatusText.Text = "تم حفظ ملف CSV بنجاح";
                    MessageBox.Show($"تم حفظ عرض الأسعار في:\n{saveDialog.FileName}\n\nملاحظة: تم الحفظ كملف CSV يمكن فتحه في Excel", "حفظ Excel", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // فتح الملف
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "خطأ في حفظ CSV";
                MessageBox.Show($"خطأ في حفظ ملف CSV: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveWordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Rich Text Format (*.rtf)|*.rtf",
                    FileName = $"عرض_أسعار_{DateTime.Now:yyyyMMdd_HHmmss}.rtf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var content = GenerateRtfContent();
                    File.WriteAllText(saveDialog.FileName, content, Encoding.UTF8);
                    
                    StatusText.Text = "تم حفظ ملف RTF بنجاح";
                    MessageBox.Show($"تم حفظ عرض الأسعار في:\n{saveDialog.FileName}\n\nملاحظة: تم الحفظ كملف RTF يمكن فتحه في Word", "حفظ Word", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // فتح الملف
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "خطأ في حفظ RTF";
                MessageBox.Show($"خطأ في حفظ ملف RTF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private System.Windows.Documents.FlowDocument CreatePrintContent()
        {
            var document = new System.Windows.Documents.FlowDocument();
            document.PagePadding = new Thickness(50);
            document.FontFamily = new System.Windows.Media.FontFamily("Arial");
            document.FontSize = 12;

            // العنوان
            var title = new System.Windows.Documents.Paragraph();
            title.TextAlignment = TextAlignment.Center;
            title.FontSize = 18;
            title.FontWeight = FontWeights.Bold;
            title.Inlines.Add("عرض أسعار شامل للأجهزة الطبية");
            document.Blocks.Add(title);

            // معلومات العرض
            var info = new System.Windows.Documents.Paragraph();
            info.Inlines.Add($"تاريخ العرض: {_quotationDate:yyyy/MM/dd}\n");
            info.Inlines.Add($"رقم العرض: {_quotationNumber}\n");
            info.Inlines.Add($"عدد العناصر: {_quotationItems.Count}\n\n");
            document.Blocks.Add(info);

            // العناصر
            foreach (var item in _quotationItems)
            {
                var itemPara = new System.Windows.Documents.Paragraph();
                itemPara.Inlines.Add($"{item.ItemNumber}. {item.Name} - {item.Category}\n");
                itemPara.Inlines.Add($"   الوصف: {item.Description}\n");
                itemPara.Inlines.Add($"   السعر: {item.UnitPrice:N0} ريال × {item.Quantity} = {item.TotalPrice:N0} ريال\n\n");
                document.Blocks.Add(itemPara);
            }

            // المجموع
            var total = new System.Windows.Documents.Paragraph();
            total.FontWeight = FontWeights.Bold;
            total.Inlines.Add($"المجموع الفرعي: {_subtotal:N0} ريال\n");
            total.Inlines.Add($"ضريبة القيمة المضافة (15%): {_tax:N0} ريال\n");
            total.Inlines.Add($"المجموع الإجمالي: {_grandTotal:N0} ريال\n");
            document.Blocks.Add(total);

            return document;
        }

        private string GenerateTextContent()
        {
            var content = new StringBuilder();
            content.AppendLine("=== عرض أسعار شامل للأجهزة الطبية ===");
            content.AppendLine();
            content.AppendLine($"تاريخ العرض: {_quotationDate:yyyy/MM/dd}");
            content.AppendLine($"رقم العرض: {_quotationNumber}");
            content.AppendLine($"عدد العناصر: {_quotationItems.Count}");
            content.AppendLine($"صالح حتى: {_quotationDate.AddDays(30):yyyy/MM/dd}");
            content.AppendLine();
            content.AppendLine("تفاصيل العناصر:");
            content.AppendLine(new string('=', 80));

            foreach (var item in _quotationItems)
            {
                content.AppendLine($"{item.ItemNumber}. {item.Name}");
                content.AppendLine($"   الفئة: {item.Category}");
                content.AppendLine($"   الوصف: {item.Description}");
                content.AppendLine($"   السعر: {item.UnitPrice:N0} ريال");
                content.AppendLine($"   الكمية: {item.Quantity}");
                content.AppendLine($"   المجموع: {item.TotalPrice:N0} ريال");
                content.AppendLine();
            }

            content.AppendLine(new string('-', 80));
            content.AppendLine($"المجموع الفرعي: {_subtotal:N0} ريال");
            content.AppendLine($"ضريبة القيمة المضافة (15%): {_tax:N0} ريال");
            content.AppendLine($"المجموع الإجمالي: {_grandTotal:N0} ريال");
            content.AppendLine();
            content.AppendLine("ملاحظات:");
            content.AppendLine("• العرض صالح لمدة 30 يوماً من تاريخ الإصدار");
            content.AppendLine("• جميع الأسعار شاملة ضريبة القيمة المضافة");
            content.AppendLine("• التوصيل والتركيب مجاني داخل المدينة");

            return content.ToString();
        }

        private string GenerateCsvContent()
        {
            var content = new StringBuilder();
            content.AppendLine("رقم,اسم العنصر,الفئة,الوصف,السعر,الكمية,المجموع");

            foreach (var item in _quotationItems)
            {
                content.AppendLine($"{item.ItemNumber},\"{item.Name}\",\"{item.Category}\",\"{item.Description}\",{item.UnitPrice},{item.Quantity},{item.TotalPrice}");
            }

            content.AppendLine();
            content.AppendLine($",,,,المجموع الفرعي,,{_subtotal}");
            content.AppendLine($",,,,ضريبة القيمة المضافة (15%),,{_tax}");
            content.AppendLine($",,,,المجموع الإجمالي,,{_grandTotal}");

            return content.ToString();
        }

        private string GenerateRtfContent()
        {
            var content = new StringBuilder();
            content.AppendLine("عرض أسعار شامل للأجهزة الطبية");
            content.AppendLine("=====================================");
            content.AppendLine();
            content.AppendLine($"تاريخ العرض: {_quotationDate:yyyy/MM/dd}");
            content.AppendLine($"رقم العرض: {_quotationNumber}");
            content.AppendLine($"عدد العناصر: {_quotationItems.Count}");
            content.AppendLine();

            foreach (var item in _quotationItems)
            {
                content.AppendLine($"{item.ItemNumber}. {item.Name}");
                content.AppendLine($"الفئة: {item.Category}");
                content.AppendLine($"الوصف: {item.Description}");
                content.AppendLine($"السعر: {item.UnitPrice:N0} ريال");
                content.AppendLine($"المجموع: {item.TotalPrice:N0} ريال");
                content.AppendLine();
            }

            content.AppendLine("=====================================");
            content.AppendLine($"المجموع الفرعي: {_subtotal:N0} ريال");
            content.AppendLine($"ضريبة القيمة المضافة (15%): {_tax:N0} ريال");
            content.AppendLine($"المجموع الإجمالي: {_grandTotal:N0} ريال");
            content.AppendLine();
            content.AppendLine("ملاحظات:");
            content.AppendLine("• العرض صالح لمدة 30 يوماً من تاريخ الإصدار");
            content.AppendLine("• جميع الأسعار شاملة ضريبة القيمة المضافة");
            content.AppendLine("• التوصيل والتركيب مجاني داخل المدينة");

            return content.ToString();
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // فئة عرض عنصر العرض
    public class QuotationItemDisplay
    {
        public int ItemNumber { get; set; }
        public string Name { get; set; } = "";
        public string Category { get; set; } = "";
        public string Description { get; set; } = "";
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
        public decimal TotalPrice => UnitPrice * Quantity;
        public string UnitPriceFormatted => $"{UnitPrice:N0} ريال";
        public string TotalPriceFormatted => $"{TotalPrice:N0} ريال";
    }
}
