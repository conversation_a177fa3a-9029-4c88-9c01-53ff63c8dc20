# PowerShell script to create desktop shortcut
# سكريبت لإنشاء اختصار على سطح المكتب

Write-Host "Creating desktop shortcut for Medical Devices Management System..." -ForegroundColor Green

# Get current directory
$currentDir = Get-Location

# Get desktop path
$desktopPath = [Environment]::GetFolderPath("Desktop")

# Create shortcut
$WshShell = New-Object -comObject WScript.Shell
$shortcut = $WshShell.CreateShortcut("$desktopPath\نظام إدارة الأجهزة الطبية.lnk")
$shortcut.TargetPath = "$currentDir\تشغيل_النظام_العربي.bat"
$shortcut.WorkingDirectory = $currentDir
$shortcut.Description = "نظام إدارة الأجهزة الطبية المتكامل مع التحسينات العربية"
$shortcut.IconLocation = "shell32.dll,21"
$shortcut.Save()

Write-Host "Desktop shortcut created successfully!" -ForegroundColor Green
Write-Host "Shortcut location: $desktopPath\نظام إدارة الأجهزة الطبية.lnk" -ForegroundColor Yellow

# Test if shortcut was created
if (Test-Path "$desktopPath\نظام إدارة الأجهزة الطبية.lnk") {
    Write-Host "✅ Shortcut verified and ready to use!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to create shortcut" -ForegroundColor Red
}

Write-Host ""
Write-Host "You can now double-click the shortcut on your desktop to run the system!" -ForegroundColor Cyan
Write-Host "يمكنك الآن النقر مرتين على الاختصار في سطح المكتب لتشغيل النظام!" -ForegroundColor Cyan
