using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditSupplierWindow : Window
    {
        private Supplier _supplier;
        private bool _isEditMode;
        
        public AddEditSupplierWindow(Supplier supplier = null)
        {
            InitializeComponent();
            _supplier = supplier;
            _isEditMode = supplier != null;

            LoadCitiesAsync();

            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل مورد";
                LoadSupplierData();
            }
            else
            {
                // تعيين القيم الافتراضية
                CityComboBox.SelectedIndex = 0;
                StatusComboBox.SelectedIndex = 0;
                RatingComboBox.SelectedIndex = 4; // 3.0 - جيد
                CreatedDateTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            }
        }
        
        private void LoadSupplierData()
        {
            if (_supplier == null) return;
            
            NameTextBox.Text = _supplier.Name;
            PhoneTextBox.Text = _supplier.Phone;
            EmailTextBox.Text = _supplier.Email;
            AddressTextBox.Text = _supplier.Address;
            PostalCodeTextBox.Text = _supplier.PostalCode;
            CreatedDateTextBlock.Text = _supplier.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            LastUpdatedTextBlock.Text = _supplier.LastUpdated.ToString("dd/MM/yyyy HH:mm");
            
            // تعيين المدينة
            foreach (var item in CityComboBox.Items)
            {
                if (item.ToString().Contains(_supplier.City))
                {
                    CityComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين التقييم
            var ratingText = _supplier.Rating.ToString("F1");
            foreach (var item in RatingComboBox.Items)
            {
                if (item.ToString().StartsWith(ratingText))
                {
                    RatingComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (var item in StatusComboBox.Items)
            {
                if (item.ToString().Contains(_supplier.Status))
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateSupplierData();
                    App.DatabaseContext.Suppliers.Update(_supplier);
                }
                else
                {
                    _supplier = new Supplier();
                    UpdateSupplierData();
                    _supplier.CreatedDate = DateTime.Now;
                    App.DatabaseContext.Suppliers.Add(_supplier);
                }
                
                _supplier.LastUpdated = DateTime.Now;
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _isEditMode ? "تم تحديث المورد بنجاح!" : "تم إضافة المورد بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateSupplierData()
        {
            _supplier.Name = NameTextBox.Text.Trim();
            _supplier.Phone = PhoneTextBox.Text.Trim();
            _supplier.Email = EmailTextBox.Text.Trim();
            _supplier.Address = AddressTextBox.Text.Trim();
            _supplier.City = CityComboBox.Text;
            _supplier.PostalCode = PostalCodeTextBox.Text.Trim();
            _supplier.Status = StatusComboBox.Text;
            
            // استخراج التقييم من النص
            var ratingText = RatingComboBox.Text.Split(' ')[0];
            _supplier.Rating = decimal.Parse(ratingText);
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(AddressTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال العنوان", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AddressTextBox.Focus();
                return false;
            }
            
            if (CityComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المدينة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CityComboBox.Focus();
                return false;
            }
            
            if (RatingComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار التقييم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                RatingComboBox.Focus();
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الحالة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async void LoadCitiesAsync()
        {
            try
            {
                var cities = await App.DatabaseContext.Cities
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Country)
                    .ThenBy(c => c.Name)
                    .ToListAsync();

                CityComboBox.Items.Clear();

                foreach (var city in cities)
                {
                    CityComboBox.Items.Add($"{city.Name} - {city.Country}");
                }

                // إضافة مدن افتراضية إذا لم توجد
                if (!cities.Any())
                {
                    CityComboBox.Items.Add("بغداد - العراق");
                    CityComboBox.Items.Add("البصرة - العراق");
                    CityComboBox.Items.Add("الرياض - السعودية");
                    CityComboBox.Items.Add("جدة - السعودية");
                    CityComboBox.Items.Add("الكويت - الكويت");
                    CityComboBox.Items.Add("الدوحة - قطر");
                    CityComboBox.Items.Add("دبي - الإمارات");
                    CityComboBox.Items.Add("أبو ظبي - الإمارات");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المدن: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageSupplierCitiesBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageCitiesWindow = new ManageCitiesWindow();
            if (manageCitiesWindow.ShowDialog() == true)
            {
                LoadCitiesAsync();
            }
        }
    }
}
