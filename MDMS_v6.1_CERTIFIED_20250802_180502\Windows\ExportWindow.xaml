<Window x:Class="MedicalDevicesManager.Windows.ExportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تصدير البيانات" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="📊 تصدير البيانات" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- أزرار التصدير السريع -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" x:Name="ExportDevicesBtn" Content="📱 تصدير الأجهزة" Width="140" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="ExportDevicesBtn_Click"/>
            
            <Button Grid.Column="1" x:Name="ExportInventoryBtn" Content="📦 تصدير المخزون" Width="140" Height="40" 
                    Background="#17A2B8" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="ExportInventoryBtn_Click"/>
            
            <Button Grid.Column="2" x:Name="ExportSalesBtn" Content="💰 تصدير المبيعات" Width="140" Height="40" 
                    Background="#FFC107" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="ExportSalesBtn_Click"/>
            
            <Button Grid.Column="3" x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="40" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,20,0" Click="RefreshBtn_Click"/>
            
            <TextBlock Grid.Column="5" x:Name="StatusTextBlock" Text="جاهز" VerticalAlignment="Center" 
                       FontWeight="Bold" Foreground="#28A745"/>
        </Grid>
        
        <!-- جدول سجلات التصدير -->
        <DataGrid Grid.Row="2" x:Name="ExportsDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14"
                  SelectionChanged="ExportsDataGrid_SelectionChanged">
            
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="الحالة" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Status}" HorizontalAlignment="Center" FontWeight="Bold">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Status}" Value="Success">
                                                <Setter Property="Foreground" Value="#28A745"/>
                                                <Setter Property="Text" Value="✅ نجح"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="Failed">
                                                <Setter Property="Foreground" Value="#DC3545"/>
                                                <Setter Property="Text" Value="❌ فشل"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="InProgress">
                                                <Setter Property="Foreground" Value="#FFC107"/>
                                                <Setter Property="Text" Value="⏳ جاري"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTextColumn Header="اسم الملف" Binding="{Binding ExportName}" Width="200"/>
                <DataGridTextColumn Header="نوع التصدير" Binding="{Binding ExportType}" Width="100"/>
                <DataGridTextColumn Header="نوع البيانات" Binding="{Binding DataType}" Width="120"/>
                <DataGridTextColumn Header="الحجم" Binding="{Binding FileSize}" Width="100"/>
                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" Width="130"/>
                <DataGridTextColumn Header="تاريخ الاكتمال" Binding="{Binding CompletedDate, StringFormat=dd/MM/yyyy HH:mm}" Width="130"/>
                <DataGridTextColumn Header="المنشئ" Binding="{Binding CreatedBy}" Width="100"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="📁" Width="30" Height="25" 
                                        Background="#17A2B8" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="فتح الملف"
                                        Click="OpenFileBtn_Click"/>
                                <Button Content="📂" Width="30" Height="25" 
                                        Background="#6C757D" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="فتح المجلد"
                                        Click="OpenFolderBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteExportBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- نموذج التصدير المخصص -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0">
            
            <StackPanel>
                <TextBlock Text="📝 تصدير مخصص" FontWeight="Bold" Margin="0,0,0,15" Foreground="#495057"/>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- الصف الأول -->
                    <Grid Grid.Row="0" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="نوع البيانات:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Grid.Column="1" x:Name="DataTypeComboBox" Height="30" Margin="0,0,20,0">
                            <ComboBoxItem Content="الأجهزة الطبية" Tag="MedicalDevices"/>
                            <ComboBoxItem Content="المخزون" Tag="Inventory"/>
                            <ComboBoxItem Content="المبيعات" Tag="Sales"/>
                            <ComboBoxItem Content="العملاء" Tag="Customers"/>
                            <ComboBoxItem Content="الموردين" Tag="Suppliers"/>
                            <ComboBoxItem Content="الشحنات" Tag="Shipments"/>
                            <ComboBoxItem Content="الصيانة" Tag="Maintenance"/>
                        </ComboBox>
                        
                        <TextBlock Grid.Column="2" Text="نوع التصدير:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Grid.Column="3" x:Name="ExportTypeComboBox" Height="30" Margin="0,0,20,0">
                            <ComboBoxItem Content="Excel (.xlsx)" Tag="Excel" IsSelected="True"/>
                            <ComboBoxItem Content="PDF (.pdf)" Tag="PDF"/>
                        </ComboBox>
                        
                        <TextBlock Grid.Column="4" Text="اسم الملف:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="5" x:Name="FileNameTextBox" Height="30"
                                 Text="Export_Custom"/>
                    </Grid>
                    
                    <!-- الصف الثاني -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <DatePicker Grid.Column="1" x:Name="FromDatePicker" Height="30" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <DatePicker Grid.Column="3" x:Name="ToDatePicker" Height="30" Margin="0,0,20,0"/>
                        
                        <Button Grid.Column="4" x:Name="CustomExportBtn" Content="📊 تصدير" Width="100" Height="30" 
                                Background="#28A745" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Click="CustomExportBtn_Click"/>
                    </Grid>
                </Grid>
                
                <!-- شريط التقدم -->
                <ProgressBar x:Name="ExportProgressBar" Height="20" Margin="0,15,0,0" 
                             Visibility="Collapsed" IsIndeterminate="True"/>
                
                <!-- معلومات إضافية -->
                <Grid Margin="0,15,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" x:Name="TotalExportsTextBlock" Text="إجمالي التصديرات: 0" 
                               FontWeight="SemiBold" Foreground="#495057"/>
                    <TextBlock Grid.Column="1" x:Name="SuccessfulExportsTextBlock" Text="التصديرات الناجحة: 0" 
                               FontWeight="SemiBold" Foreground="#28A745" HorizontalAlignment="Center"/>
                    <TextBlock Grid.Column="2" x:Name="TotalSizeTextBlock" Text="الحجم الإجمالي: 0 MB" 
                               FontWeight="SemiBold" Foreground="#17A2B8" HorizontalAlignment="Right"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
    

</Window>
