{"format": 1, "restore": {"D:\\AJM\\CERTIFIED_VERSIONS\\MDMS_v6.1_CERTIFIED_20250802_180502\\MedicalDevicesManager.csproj": {}}, "projects": {"D:\\AJM\\CERTIFIED_VERSIONS\\MDMS_v6.1_CERTIFIED_20250802_180502\\MedicalDevicesManager.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AJM\\CERTIFIED_VERSIONS\\MDMS_v6.1_CERTIFIED_20250802_180502\\MedicalDevicesManager.csproj", "projectName": "MedicalDevicesManager", "projectPath": "D:\\AJM\\CERTIFIED_VERSIONS\\MDMS_v6.1_CERTIFIED_20250802_180502\\MedicalDevicesManager.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AJM\\CERTIFIED_VERSIONS\\MDMS_v6.1_CERTIFIED_20250802_180502\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.105.0, )"}, "EPPlus": {"target": "Package", "version": "[6.2.10, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}, "iText7": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303\\RuntimeIdentifierGraph.json"}}}}}