# 🚀 الدليل السريع للمستخدم - نظام إدارة الأجهزة الطبية

## 🎯 **البدء السريع:**

### **1. تشغيل النظام:**
```bash
# الطريقة الأولى: استخدام ملف التشغيل
START.bat

# الطريقة الثانية: من سطر الأوامر
dotnet run --project MedicalDevicesManager.csproj
```

### **2. التحقق من سلامة النظام:**
1. **افتح التطبيق** - يجب أن يفتح بدون أخطاء
2. **اضغط على "🔍 تشخيص النظام"** في الشريط الجانبي
3. **تحقق من النتائج** - يجب أن تظهر:
   - عدد الأجهزة: 3
   - الأرقام التسلسلية: 8 (نشط: 8)
   - أجهزة لها أرقام تسلسلية: 3

---

## 📋 **الوحدات الرئيسية:**

### **🏥 إدارة الأجهزة الطبية:**
- **إضافة جهاز جديد:** زر "➕ إضافة جهاز جديد"
- **عرض التفاصيل:** نقر مزدوج على الجهاز أو زر "👁️"
- **تعديل الجهاز:** زر "✏️ تعديل"
- **حذف الجهاز:** زر "🗑️ حذف"
- **إدارة الأرقام التسلسلية:** زر "🔢" (إذا متوفر)

### **📦 إدارة المخزون:**
- **إضافة عنصر:** زر "➕ إضافة عنصر جديد"
- **إدارة الفئات:** زر "⚙️ إدارة الفئات"
- **اختيار من المخزون:** في نافذة المبيعات
- **تتبع الحركة:** عرض تلقائي لحركات المخزون

### **💰 إدارة المبيعات:**
- **إضافة فاتورة:** زر "➕ إضافة فاتورة جديدة"
- **اختيار من المخزون:** زر "📦 اختيار من المخزون"
- **حساب الخصم:** إدخال نسبة مئوية تلقائية
- **الدفع الجزئي:** نظام متقدم للدفعات

### **👥 إدارة العملاء:**
- **إضافة عميل:** زر "➕ إضافة عميل جديد"
- **إدارة أنواع العملاء:** زر "⚙️ إدارة الأنواع"
- **إدارة المدن:** زر "🏙️ إدارة المدن"

### **🏭 إدارة الموردين:**
- **إضافة مورد:** زر "➕ إضافة مورد جديد"
- **ربط بالمدن:** اختيار من قائمة المدن المدارة

### **🚚 إدارة الشحنات:**
- **إضافة شحنة:** زر "➕ إضافة شحنة جديدة"
- **إدارة المستلمين:** زر "👤 إدارة المستلمين"
- **تتبع الحالة:** تحديث تلقائي لحالة الشحنة

### **🔧 الضمان والصيانة:**
- **إضافة سجل صيانة:** زر "➕ إضافة سجل صيانة جديد"
- **اختيار الجهاز:** نظام محدث مع نافذة اختيار
- **إدارة الفنيين:** زر "👨‍🔧 إدارة الفنيين"
- **رفع المستندات:** إمكانية إرفاق ملفات

### **🔩 إدارة قطع الغيار:**
- **إضافة قطعة:** زر "➕ إضافة قطعة جديدة"
- **ربط بالأجهزة:** اختيار الجهاز المتوافق
- **تتبع المخزون:** عرض الكمية المتاحة

### **⚙️ التركيبات:**
- **إضافة تركيب:** زر "➕ إضافة تركيب جديد"
- **ربط بالعميل:** اختيار العميل والموقع
- **تتبع الحالة:** متابعة مراحل التركيب

---

## 🛠️ **الأدوات المساعدة:**

### **💾 النسخ الاحتياطي:**
- **إنشاء نسخة:** زر "💾 إنشاء نسخة احتياطية"
- **استعادة نسخة:** زر "📥 استعادة من نسخة احتياطية"
- **النسخ التلقائي:** يعمل في الخلفية

### **📤 التصدير:**
- **تصدير Excel:** من أي جدول
- **تصدير PDF:** للتقارير
- **تصدير CSV:** للبيانات الخام

### **📊 التقارير:**
- **تقارير المبيعات:** حسب الفترة والعميل
- **تقارير المخزون:** حالة المخزون والحركة
- **تقارير الصيانة:** سجلات الصيانة والضمان

### **🔍 التشخيص:**
- **تشخيص النظام:** فحص شامل للبيانات
- **فحص قاعدة البيانات:** التحقق من التكامل
- **إحصائيات مفصلة:** عرض تفاصيل النظام

---

## 🎨 **نصائح للاستخدام الأمثل:**

### **⌨️ اختصارات لوحة المفاتيح:**
- **F5:** تحديث البيانات
- **Ctrl+N:** إضافة جديد (في النوافذ المناسبة)
- **Ctrl+S:** حفظ
- **Ctrl+F:** البحث
- **Escape:** إغلاق النافذة الحالية

### **🔍 البحث المتقدم:**
- **البحث السريع:** اكتب في صندوق البحث
- **البحث المتقدم:** استخدم الفلاتر المتعددة
- **البحث في التفاصيل:** يشمل جميع الحقول

### **📊 استخدام الجداول:**
- **ترتيب الأعمدة:** اضغط على رأس العمود
- **تغيير حجم الأعمدة:** اسحب حدود العمود
- **إخفاء/إظهار الأعمدة:** قائمة السياق
- **التصفح:** استخدم أزرار التنقل

### **💡 نصائح مهمة:**
1. **احفظ بانتظام:** استخدم Ctrl+S
2. **راجع البيانات:** قبل الحفظ النهائي
3. **استخدم التشخيص:** عند وجود مشاكل
4. **انشئ نسخ احتياطية:** بشكل دوري

---

## 🚨 **حل المشاكل الشائعة:**

### **المشكلة: التطبيق لا يفتح**
**الحل:**
1. تأكد من تثبيت .NET 6
2. شغل `dotnet build` أولاً
3. تحقق من وجود ملف قاعدة البيانات

### **المشكلة: البيانات لا تظهر**
**الحل:**
1. اضغط "🔍 تشخيص النظام"
2. تحقق من النتائج
3. أعد تشغيل التطبيق إذا لزم الأمر

### **المشكلة: خطأ في الحفظ**
**الحل:**
1. تحقق من صحة البيانات المدخلة
2. تأكد من عدم ترك حقول مطلوبة فارغة
3. راجع رسالة الخطأ بعناية

### **المشكلة: بطء في الأداء**
**الحل:**
1. أغلق النوافذ غير المستخدمة
2. قلل عدد السجلات المعروضة
3. استخدم البحث لتضييق النتائج

---

## 📞 **الحصول على المساعدة:**

### **الموارد المتاحة:**
- **ملفات التوثيق:** في مجلد المشروع
- **أدوات التشخيص:** مدمجة في النظام
- **ملفات الاختبار:** أدلة مفصلة للاختبار

### **عند الحاجة للدعم:**
1. **استخدم أدوات التشخيص** أولاً
2. **راجع ملفات استكشاف الأخطاء**
3. **احفظ رسائل الخطأ** للمراجعة
4. **التقط لقطات شاشة** للمشاكل

---

## 🎉 **مبروك! أنت جاهز للبدء!**

**النظام مصمم ليكون سهل الاستخدام وبديهي.**

**ابدأ بالوحدات الأساسية وتوسع تدريجياً حسب احتياجاتك.**

---
**إعداد:** Augment Agent  
**تاريخ:** 2025-08-02  
**الإصدار:** v6.0 Enhanced
