@echo off
chcp 65001 >nul 2>&1
cls
title Medical Devices Management System v2.1 - Arabic Support

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Medical Devices Management System             ║
echo ║                        Version 2.1                          ║
echo ║                  Quality Certificate Edition                 ║
echo ║                     Arabic Support Fixed                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🏥 Comprehensive Medical Management System:
echo    • Medical Devices and Serial Numbers Management
echo    • Smart Inventory and Sales with Electronic Invoices
echo    • Customers, Suppliers and Tracked Shipments
echo    • Scheduled Maintenance, Warranty and Spare Parts
echo    • Interactive Reports and Automatic Backup
echo.
echo ⚡ Enhanced Features in v2.1:
echo    • Standalone executable (no separate .NET required)
echo    • Enhanced Arabic interface with proper encoding
echo    • Updated database (17 integrated tables)
echo    • Advanced diagnostic and analysis tools
echo    • Quality certificate approved
echo    • Smart search system and advanced filtering
echo.
echo 🚀 Starting the updated system...
echo.

REM Set paths with proper quoting
set "EXE_PATH=%~dp0Distribution\MedicalDevicesManager_v2.1_QualityCertificate\MedicalDevicesManager.exe"
set "WORK_DIR=%~dp0Distribution\MedicalDevicesManager_v2.1_QualityCertificate"

REM Check if executable exists
if not exist "%EXE_PATH%" (
    echo [ERROR] Version v2.1 not found!
    echo.
    echo Expected path: %EXE_PATH%
    echo.
    echo Please make sure Distribution folder contains the updated version
    echo or use the basic version instead
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo [SUCCESS] Found v2.1 version
echo Path: %EXE_PATH%
echo.

REM Check database
if exist "%WORK_DIR%\MedicalDevicesIntegrated.db" (
    echo [SUCCESS] Database available and updated
) else (
    echo [INFO] Database not found - will be created automatically
)
echo.

echo 🎯 Version Information:
echo • Version: v2.1 Quality Certificate Edition
echo • File Type: Standalone Windows Application
echo • Database: Optimized and compressed SQLite
echo • Status: Ready for production and commercial use
echo • Arabic Support: Full encoding support implemented
echo.

echo 💡 Tips for optimal use:
echo • First run may take 10-15 seconds to load data
echo • Use "System Diagnostics" button to verify data integrity
echo • Create regular backups of the database
echo • Review user guide to utilize all features
echo.

echo 🚀 Starting system now...
echo.

REM Change to working directory
pushd "%WORK_DIR%"

REM Start the system
start "" "%EXE_PATH%"

REM Wait a moment to check if it started
timeout /t 3 /nobreak >nul

echo [SUCCESS] System started successfully!
echo.
echo 📊 The system is now running in the background
echo.
echo 🎉 Congratulations! You can now:
echo • Use all 12 integrated system modules
echo • Manage devices, inventory, and sales
echo • Generate reports and invoices
echo • Export data in multiple formats
echo • Use advanced diagnostic tools
echo.
echo 💾 To create desktop shortcut:
echo Run file: Create_Desktop_Shortcut_Arabic.bat
echo Or use: Create-Arabic-Shortcut.ps1
echo.
echo 📞 For technical support:
echo Review documentation files in project folder
echo or use built-in diagnostic tools
echo.

REM Return to original directory
popd

echo ========================================
echo Press any key to close this window...
pause >nul
