<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- خطوط عربية حديثة -->
    <FontFamily x:Key="ArabicFont">Segoe UI Variable, Segoe UI, Tahoma, Arial, Microsoft Sans Serif</FontFamily>
    <FontFamily x:Key="ArabicTitleFont">Segoe UI Variable, Segoe UI, Tahoma, Arial Black, Microsoft Sans Serif</FontFamily>

    <!-- أحجام الخطوط الحديثة -->
    <sys:Double x:Key="SmallFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="NormalFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="LargeFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="TitleFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="HeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>

    <!-- الألوان الحديثة -->
    <SolidColorBrush x:Key="ModernPrimary" Color="#3B82F6"/>
    <SolidColorBrush x:Key="ModernSuccess" Color="#10B981"/>
    <SolidColorBrush x:Key="ModernDanger" Color="#EF4444"/>
    <SolidColorBrush x:Key="ModernText" Color="#374151"/>
    <SolidColorBrush x:Key="ModernMuted" Color="#6B7280"/>
    <SolidColorBrush x:Key="ModernBorder" Color="#E5E7EB"/>

    <!-- أنماط النصوص العربية الحديثة -->
    <Style x:Key="ArabicTextBlock" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="TextAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Foreground" Value="{StaticResource ModernText}"/>
    </Style>

    <Style x:Key="ArabicTitleTextBlock" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicTitleFont}"/>
        <Setter Property="FontSize" Value="{StaticResource TitleFontSize}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <Style x:Key="ArabicHeaderTextBlock" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicTitleFont}"/>
        <Setter Property="FontSize" Value="{StaticResource HeaderFontSize}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- أنماط الحقول النصية العربية الحديثة -->
    <Style x:Key="ArabicTextBox" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="TextAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Padding" Value="12,10"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="BorderBrush" Value="{StaticResource ModernBorder}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Foreground" Value="{StaticResource ModernText}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Focusable="false"
                                    HorizontalScrollBarVisibility="Hidden"
                                    VerticalScrollBarVisibility="Hidden"
                                    Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#9CA3AF"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource ModernPrimary}"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط الأزرار العربية الحديثة -->
    <Style x:Key="ArabicButton" TargetType="Button">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="16,10"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PrimaryArabicButton" TargetType="Button" BasedOn="{StaticResource ArabicButton}">
        <Setter Property="Background" Value="{StaticResource ModernPrimary}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="SuccessArabicButton" TargetType="Button" BasedOn="{StaticResource ArabicButton}">
        <Setter Property="Background" Value="{StaticResource ModernSuccess}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="DangerArabicButton" TargetType="Button" BasedOn="{StaticResource ArabicButton}">
        <Setter Property="Background" Value="{StaticResource ModernDanger}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- أنماط القوائم المنسدلة العربية الحديثة -->
    <Style x:Key="ArabicComboBox" TargetType="ComboBox">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Padding" Value="12,10"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="BorderBrush" Value="{StaticResource ModernBorder}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Foreground" Value="{StaticResource ModernText}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <ToggleButton x:Name="ToggleButton"
                                    Grid.Column="2"
                                    Focusable="false"
                                    IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                    ClickMode="Press"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                            <ToggleButton.Template>
                                <ControlTemplate TargetType="ToggleButton">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="3">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition />
                                                <ColumnDefinition Width="20" />
                                            </Grid.ColumnDefinitions>
                                            <Path x:Name="Arrow" Grid.Column="1"
                                                Fill="#666"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Data="M 0 0 L 4 4 L 8 0 Z"/>
                                        </Grid>
                                    </Border>
                                </ControlTemplate>
                            </ToggleButton.Template>
                        </ToggleButton>
                        <ContentPresenter x:Name="ContentSite"
                                        IsHitTestVisible="False"
                                        Content="{TemplateBinding SelectionBoxItem}"
                                        ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                        ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                        Margin="10,3,23,3"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Right" />
                        <TextBox x:Name="PART_EditableTextBox"
                               Style="{x:Null}"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Center"
                               Margin="3,3,23,3"
                               Focusable="True"
                               Background="Transparent"
                               Visibility="Hidden"
                               IsReadOnly="{TemplateBinding IsReadOnly}"/>
                        <Popup x:Name="Popup"
                             Placement="Bottom"
                             IsOpen="{TemplateBinding IsDropDownOpen}"
                             AllowsTransparency="True"
                             Focusable="False"
                             PopupAnimation="Slide">
                            <Grid x:Name="DropDown"
                                SnapsToDevicePixels="True"
                                MinWidth="{TemplateBinding ActualWidth}"
                                MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                <Border x:Name="DropDownBorder"
                                      Background="White"
                                      BorderThickness="1"
                                      BorderBrush="#CED4DA"
                                      CornerRadius="3"/>
                                <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                </ScrollViewer>
                            </Grid>
                        </Popup>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط التسميات العربية الحديثة -->
    <Style x:Key="ArabicLabel" TargetType="Label">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Foreground" Value="{StaticResource ModernText}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- أنماط الجداول العربية الحديثة -->
    <Style x:Key="ArabicDataGrid" TargetType="DataGrid">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AlternatingRowBackground" Value="#F9FAFB"/>
        <Setter Property="RowBackground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="Transparent"/>
    </Style>

    <!-- أنماط النوافذ العربية الحديثة -->
    <Style x:Key="ArabicWindow" TargetType="Window">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="WindowStartupLocation" Value="CenterScreen"/>
        <Setter Property="Background" Value="#FAFAFA"/>
    </Style>

    <!-- أنماط البطاقات العربية -->
    <Style x:Key="ArabicCard" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource ModernBorder}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="4" Opacity="0.1" BlurRadius="20"/>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
