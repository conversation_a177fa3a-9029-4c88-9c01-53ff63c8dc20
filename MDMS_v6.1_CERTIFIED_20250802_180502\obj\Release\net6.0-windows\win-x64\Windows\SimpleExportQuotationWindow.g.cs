﻿#pragma checksum "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "33B12DCFB5A8BD9615E2A854336B7B88242519EC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// SimpleExportQuotationWindow
    /// </summary>
    public partial class SimpleExportQuotationWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ContentPanel;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuotationTitleText;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuotationDateText;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuotationNumberText;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ItemsCountText;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoriesText;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidityText;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ItemsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtotalText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxText;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GrandTotalText;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/simpleexportquotationwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 43 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 46 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SavePdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 49 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveExcelButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 52 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveWordButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 57 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateQuotationButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.QuotationTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.QuotationDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.QuotationNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ItemsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.CategoriesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ValidityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 15:
            this.SubtotalText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TaxText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.GrandTotalText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            
            #line 188 "..\..\..\..\..\Windows\SimpleExportQuotationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

