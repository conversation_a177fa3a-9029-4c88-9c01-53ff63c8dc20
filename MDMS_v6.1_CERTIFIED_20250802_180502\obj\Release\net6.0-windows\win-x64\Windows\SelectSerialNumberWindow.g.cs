﻿#pragma checksum "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3BE82CFFA57654303360A7ED5DF3B4FF77557123"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// SelectSerialNumberWindow
    /// </summary>
    public partial class SelectSerialNumberWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 21 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameText;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameInfo;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceBrandInfo;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceModelInfo;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceCategoryInfo;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceStatusInfo;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialCountInfo;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl SerialNumbersList;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NoSerialNumbersMessage;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/selectserialnumberwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DeviceNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DeviceNameInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.DeviceBrandInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.DeviceModelInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.DeviceCategoryInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.DeviceStatusInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.SerialCountInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            
            #line 72 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.AllDataOption_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SerialNumbersList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 11:
            this.NoSerialNumbersMessage = ((System.Windows.Controls.Border)(target));
            return;
            case 12:
            
            #line 171 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 10:
            
            #line 99 "..\..\..\..\..\Windows\SelectSerialNumberWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.SerialNumberOption_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

