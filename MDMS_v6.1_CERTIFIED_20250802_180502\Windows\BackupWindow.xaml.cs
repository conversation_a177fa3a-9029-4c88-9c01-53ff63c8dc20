using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using MedicalDevicesManager.Services;
using System.Globalization;
using System.Windows.Data;

namespace MedicalDevicesManager.Windows
{
    public partial class BackupWindow : Window
    {
        private BackupService _backupService;
        private List<BackupRecord> _allBackups;
        
        public BackupWindow()
        {
            InitializeComponent();
            _backupService = new BackupService(App.DatabaseContext);

            // تعيين اسم افتراضي للنسخة الاحتياطية
            BackupNameTextBox.Text = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";

            LoadBackupsAsync();
        }
        
        private async void LoadBackupsAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري التحميل...";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                
                _allBackups = await _backupService.GetAllBackupsAsync();
                BackupsDataGrid.ItemsSource = _allBackups;
                
                // تحديث الإحصائيات
                UpdateStatistics();
                
                StatusTextBlock.Text = "جاهز";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في التحميل";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"خطأ في تحميل النسخ الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateStatistics()
        {
            if (_allBackups != null)
            {
                TotalBackupsTextBlock.Text = $"إجمالي النسخ: {_allBackups.Count}";
                
                var successfulBackups = _allBackups.Where(b => b.Status == "Success").ToList();
                SuccessfulBackupsTextBlock.Text = $"النسخ الناجحة: {successfulBackups.Count}";
                
                var totalSize = successfulBackups.Sum(b => b.FileSize);
                TotalSizeTextBlock.Text = $"الحجم الإجمالي: {FormatFileSize(totalSize)}";
            }
        }
        
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
        
        private async void CreateBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            var createBackupWindow = new CreateBackupWindow();
            if (createBackupWindow.ShowDialog() == true)
            {
                await CreateBackupWithProgress(createBackupWindow.BackupName, createBackupWindow.Description);
            }
        }
        
        private async void AutoBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            await CreateBackupWithProgress(null, "نسخة احتياطية تلقائية", true);
        }
        
        private async void CreateManualBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            var backupName = BackupNameTextBox.Text.Trim();
            var description = DescriptionTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(backupName))
            {
                MessageBox.Show("يرجى إدخال اسم النسخة الاحتياطية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                BackupNameTextBox.Focus();
                return;
            }
            
            await CreateBackupWithProgress(backupName, description);
        }
        
        private async System.Threading.Tasks.Task CreateBackupWithProgress(string backupName, string description, bool isAuto = false)
        {
            try
            {
                // إظهار شريط التقدم
                BackupProgressBar.Visibility = Visibility.Visible;
                StatusTextBlock.Text = "جاري إنشاء النسخة الاحتياطية...";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                
                // تعطيل الأزرار
                CreateBackupBtn.IsEnabled = false;
                AutoBackupBtn.IsEnabled = false;
                CreateManualBackupBtn.IsEnabled = false;
                
                bool success;
                if (isAuto)
                {
                    success = await _backupService.CreateAutomaticBackupAsync();
                }
                else
                {
                    success = await _backupService.CreateManualBackupAsync(backupName, description);
                }
                
                if (success)
                {
                    StatusTextBlock.Text = "تم إنشاء النسخة الاحتياطية بنجاح";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                    MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // مسح النموذج
                    BackupNameTextBox.Text = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                    DescriptionTextBox.Text = "نسخة احتياطية يدوية";
                }
                else
                {
                    StatusTextBlock.Text = "فشل في إنشاء النسخة الاحتياطية";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                    MessageBox.Show("فشل في إنشاء النسخة الاحتياطية!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                
                LoadBackupsAsync();
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في إنشاء النسخة الاحتياطية";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إخفاء شريط التقدم وإعادة تفعيل الأزرار
                BackupProgressBar.Visibility = Visibility.Collapsed;
                CreateBackupBtn.IsEnabled = true;
                AutoBackupBtn.IsEnabled = true;
                CreateManualBackupBtn.IsEnabled = true;
            }
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadBackupsAsync();
        }
        
        private async void CleanupBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد تنظيف النسخ الاحتياطية القديمة؟\nسيتم الاحتفاظ بآخر 10 نسخ فقط.",
                "تأكيد التنظيف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question
            );
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    StatusTextBlock.Text = "جاري التنظيف...";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                    
                    var success = await _backupService.CleanupOldBackupsAsync(10);
                    
                    if (success)
                    {
                        StatusTextBlock.Text = "تم التنظيف بنجاح";
                        StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                        MessageBox.Show("تم تنظيف النسخ الاحتياطية القديمة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        StatusTextBlock.Text = "فشل في التنظيف";
                        StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                        MessageBox.Show("فشل في تنظيف النسخ الاحتياطية!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    
                    LoadBackupsAsync();
                }
                catch (Exception ex)
                {
                    StatusTextBlock.Text = "خطأ في التنظيف";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                    MessageBox.Show($"خطأ في تنظيف النسخ الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private async void RestoreBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var backup = button?.DataContext as BackupRecord;
            
            if (backup != null && backup.Status == "Success")
            {
                var result = MessageBox.Show(
                    $"هل تريد استعادة النسخة الاحتياطية '{backup.BackupName}'؟\n\n⚠️ تحذير: سيتم استبدال قاعدة البيانات الحالية!\nسيتم إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة.",
                    "تأكيد الاستعادة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        StatusTextBlock.Text = "جاري الاستعادة...";
                        StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                        
                        var success = await _backupService.RestoreBackupAsync(backup.Id);
                        
                        if (success)
                        {
                            StatusTextBlock.Text = "تمت الاستعادة بنجاح";
                            StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                            MessageBox.Show(
                                "تمت استعادة النسخة الاحتياطية بنجاح!\n\nيرجى إعادة تشغيل التطبيق لتطبيق التغييرات.",
                                "نجحت الاستعادة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information
                            );

                            // إغلاق التطبيق
                            Application.Current.Shutdown();
                        }
                        else
                        {
                            StatusTextBlock.Text = "فشلت الاستعادة";
                            StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                            MessageBox.Show("فشل في استعادة النسخة الاحتياطية!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        StatusTextBlock.Text = "خطأ في الاستعادة";
                        StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                        MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("لا يمكن استعادة هذه النسخة الاحتياطية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private async void VerifyBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var backup = button?.DataContext as BackupRecord;
            
            if (backup != null)
            {
                try
                {
                    StatusTextBlock.Text = "جاري فحص السلامة...";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                    
                    var isValid = await _backupService.VerifyBackupIntegrityAsync(backup.Id);
                    
                    if (isValid)
                    {
                        StatusTextBlock.Text = "النسخة سليمة";
                        StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                        MessageBox.Show("النسخة الاحتياطية سليمة ويمكن استعادتها!", "فحص السلامة", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        StatusTextBlock.Text = "النسخة تالفة";
                        StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                        MessageBox.Show("النسخة الاحتياطية تالفة ولا يمكن استعادتها!", "فحص السلامة", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    StatusTextBlock.Text = "خطأ في الفحص";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                    MessageBox.Show($"خطأ في فحص النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private void OpenFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var backup = button?.DataContext as BackupRecord;
            
            if (backup != null && File.Exists(backup.BackupPath))
            {
                try
                {
                    var folderPath = Path.GetDirectoryName(backup.BackupPath);
                    Process.Start("explorer.exe", folderPath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح المجلد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("الملف غير موجود", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private async void DeleteBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var backup = button?.DataContext as BackupRecord;
            
            if (backup != null)
            {
                var result = MessageBox.Show(
                    $"هل تريد حذف النسخة الاحتياطية '{backup.BackupName}'؟\n\nلا يمكن التراجع عن هذا الإجراء!",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _backupService.DeleteBackupAsync(backup.Id);
                        
                        if (success)
                        {
                            MessageBox.Show("تم حذف النسخة الاحتياطية بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadBackupsAsync();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف النسخة الاحتياطية!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private void BackupsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا عند تحديد نسخة احتياطية
        }
    }
    
    // محول لتنسيق حجم الملف
    public class FileSizeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is long bytes)
            {
                string[] sizes = { "B", "KB", "MB", "GB" };
                double len = bytes;
                int order = 0;
                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }
                return $"{len:0.##} {sizes[order]}";
            }
            return "0 B";
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
