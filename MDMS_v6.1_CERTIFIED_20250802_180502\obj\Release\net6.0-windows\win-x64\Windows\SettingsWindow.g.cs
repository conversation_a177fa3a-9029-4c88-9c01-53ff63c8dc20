﻿#pragma checksum "..\..\..\..\..\Windows\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C48CAC8C34534D65AB15708696B3CADE9D802DB3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 44 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CommercialRegTextBox;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyWebsiteTextBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TimezoneComboBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DateFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LowStockAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OutOfStockAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExpiryAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LowStockPercentageTextBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpiryWarningDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoReorderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoPricingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultProfitMarginTextBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultReorderQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoicePrefixTextBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberLengthTextBox;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoInvoiceNumberCheckBox;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PrintAfterSaleCheckBox;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VATPercentageTextBox;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxDiscountTextBox;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ApplyVATCheckBox;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RequireDiscountApprovalCheckBox;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BackupFrequencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupRetentionTextBox;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseBackupPathBtn;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BackupStatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastBackupTextBlock;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NextBackupTextBlock;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateBackupBtn;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreBackupBtn;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportDataBtn;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBackupStatusBtn;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenAdvancedInventoryBtn;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenExportBtn;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenNotificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RunDiagnosticBtn;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenSmartSystemBtn;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveSettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetSettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\..\..\Windows\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CompanyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CommercialRegTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CompanyAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.CompanyPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CompanyEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CompanyWebsiteTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.CurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.TimezoneComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.DateFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.LowStockAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.OutOfStockAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.ExpiryAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.LowStockPercentageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ExpiryWarningDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.AutoReorderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.AutoPricingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.DefaultProfitMarginTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.DefaultReorderQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.InvoicePrefixTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.InvoiceNumberLengthTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.AutoInvoiceNumberCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.PrintAfterSaleCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.VATPercentageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.MaxDiscountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.ApplyVATCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.RequireDiscountApprovalCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 28:
            this.AutoBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 252 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.AutoBackupCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AutoBackupCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 252 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.AutoBackupCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AutoBackupCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 29:
            this.BackupFrequencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 263 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.BackupFrequencyComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.BackupFrequencyComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 30:
            this.BackupRetentionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.BackupPathTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 284 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.BackupPathTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.BackupPathTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 32:
            this.BrowseBackupPathBtn = ((System.Windows.Controls.Button)(target));
            
            #line 286 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.BrowseBackupPathBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseBackupPathBtn_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.BackupStatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.LastBackupTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.NextBackupTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.CreateBackupBtn = ((System.Windows.Controls.Button)(target));
            
            #line 305 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.CreateBackupBtn.Click += new System.Windows.RoutedEventHandler(this.CreateBackupBtn_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.RestoreBackupBtn = ((System.Windows.Controls.Button)(target));
            
            #line 306 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.RestoreBackupBtn.Click += new System.Windows.RoutedEventHandler(this.RestoreBackupBtn_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.ExportDataBtn = ((System.Windows.Controls.Button)(target));
            
            #line 307 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.ExportDataBtn.Click += new System.Windows.RoutedEventHandler(this.ExportDataBtn_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.RefreshBackupStatusBtn = ((System.Windows.Controls.Button)(target));
            
            #line 308 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.RefreshBackupStatusBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBackupStatusBtn_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.OpenAdvancedInventoryBtn = ((System.Windows.Controls.Button)(target));
            
            #line 324 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.OpenAdvancedInventoryBtn.Click += new System.Windows.RoutedEventHandler(this.OpenAdvancedInventoryBtn_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.OpenExportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.OpenExportBtn.Click += new System.Windows.RoutedEventHandler(this.OpenExportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.OpenNotificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 358 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.OpenNotificationsBtn.Click += new System.Windows.RoutedEventHandler(this.OpenNotificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.RunDiagnosticBtn = ((System.Windows.Controls.Button)(target));
            
            #line 375 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.RunDiagnosticBtn.Click += new System.Windows.RoutedEventHandler(this.RunDiagnosticBtn_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.OpenSmartSystemBtn = ((System.Windows.Controls.Button)(target));
            
            #line 392 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.OpenSmartSystemBtn.Click += new System.Windows.RoutedEventHandler(this.OpenSmartSystemBtn_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.SaveSettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 407 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.SaveSettingsBtn.Click += new System.Windows.RoutedEventHandler(this.SaveSettingsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.ResetSettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 410 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.ResetSettingsBtn.Click += new System.Windows.RoutedEventHandler(this.ResetSettingsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 413 "..\..\..\..\..\Windows\SettingsWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

