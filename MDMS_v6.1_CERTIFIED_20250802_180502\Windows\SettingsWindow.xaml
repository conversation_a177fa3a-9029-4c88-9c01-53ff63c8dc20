<Window x:Class="MedicalDevicesManager.Windows.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام الشاملة" Height="680" Width="840"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Style="{StaticResource ArabicWindow}">

    <Border Style="{StaticResource ModernCard}" Margin="16">
        <Grid Margin="32">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان المحدث -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,32">
                <Border Background="{StaticResource SecondaryBrush}" CornerRadius="12" Width="48" Height="48" Margin="0,0,16,0">
                    <TextBlock Text="⚙️" FontSize="24" Foreground="White"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="إعدادات النظام الشاملة"
                               Style="{StaticResource ModernTitle}" FontSize="24"/>
                    <TextBlock Text="تخصيص وإعداد النظام حسب احتياجاتك"
                               Style="{StaticResource ModernMutedText}" Margin="0,4,0,0"/>
                </StackPanel>
            </StackPanel>

            <!-- التبويبات المحدثة -->
            <TabControl Grid.Row="1" FontSize="14" Background="White" BorderThickness="0">
                <TabControl.Resources>
                    <Style TargetType="TabItem">
                        <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Padding" Value="20,12"/>
                        <Setter Property="Margin" Value="4,0"/>
                        <Setter Property="Background" Value="#F3F4F6"/>
                        <Setter Property="Foreground" Value="#374151"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabItem">
                                    <Border x:Name="Border" Background="{TemplateBinding Background}"
                                            CornerRadius="8,8,0,0" Margin="{TemplateBinding Margin}">
                                        <ContentPresenter x:Name="ContentSite"
                                                        VerticalAlignment="Center"
                                                        HorizontalAlignment="Center"
                                                        ContentSource="Header"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="#1F2937"/>
                                        </Trigger>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#E5E7EB"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TabControl.Resources>
            
            <!-- تبويب الإعدادات العامة -->
            <TabItem Header="🏢 الإعدادات العامة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <!-- معلومات الشركة -->
                        <GroupBox Header="معلومات الشركة" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الشركة:" Margin="0,0,0,5"/>
                                <TextBox x:Name="CompanyNameTextBox" Grid.Row="0" Grid.Column="0" Height="30" Margin="0,20,0,10" Text="شركة الأجهزة الطبية المتقدمة"/>
                                
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="رقم السجل التجاري:" Margin="0,0,0,5"/>
                                <TextBox x:Name="CommercialRegTextBox" Grid.Row="0" Grid.Column="2" Height="30" Margin="0,20,0,10" Text="1010123456"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="العنوان:" Margin="0,0,0,5"/>
                                <TextBox x:Name="CompanyAddressTextBox" Grid.Row="1" Grid.Column="0" Height="30" Margin="0,20,0,10" Text="الرياض، المملكة العربية السعودية"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="رقم الهاتف:" Margin="0,0,0,5"/>
                                <TextBox x:Name="CompanyPhoneTextBox" Grid.Row="1" Grid.Column="2" Height="30" Margin="0,20,0,10" Text="+966 11 123 4567"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="البريد الإلكتروني:" Margin="0,0,0,5"/>
                                <TextBox x:Name="CompanyEmailTextBox" Grid.Row="2" Grid.Column="0" Height="30" Margin="0,20,0,10" Text="<EMAIL>"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="2" Text="الموقع الإلكتروني:" Margin="0,0,0,5"/>
                                <TextBox x:Name="CompanyWebsiteTextBox" Grid.Row="2" Grid.Column="2" Height="30" Margin="0,20,0,10" Text="www.medicaldevices.sa"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات النظام -->
                        <GroupBox Header="إعدادات النظام" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="العملة الافتراضية:" Margin="0,0,0,5"/>
                                <ComboBox x:Name="CurrencyComboBox" Grid.Row="0" Grid.Column="0" Height="30" Margin="0,20,0,10">
                                    <ComboBoxItem Content="دينار عراقي (IQD)" IsSelected="True"/>
                                    <ComboBoxItem Content="ريال سعودي (SAR)"/>
                                    <ComboBoxItem Content="دولار أمريكي (USD)"/>
                                    <ComboBoxItem Content="يورو (EUR)"/>
                                    <ComboBoxItem Content="دينار كويتي (KWD)"/>
                                    <ComboBoxItem Content="ريال قطري (QAR)"/>
                                    <ComboBoxItem Content="درهم إماراتي (AED)"/>
                                    <ComboBoxItem Content="دينار بحريني (BHD)"/>
                                    <ComboBoxItem Content="ريال عماني (OMR)"/>
                                    <ComboBoxItem Content="دينار أردني (JOD)"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="اللغة:" Margin="0,0,0,5"/>
                                <ComboBox x:Name="LanguageComboBox" Grid.Row="0" Grid.Column="2" Height="30" Margin="0,20,0,10">
                                    <ComboBoxItem Content="العربية" IsSelected="True"/>
                                    <ComboBoxItem Content="English"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="المنطقة الزمنية:" Margin="0,0,0,5"/>
                                <ComboBox x:Name="TimezoneComboBox" Grid.Row="1" Grid.Column="0" Height="30" Margin="0,20,0,10">
                                    <ComboBoxItem Content="توقيت بغداد (GMT+3)" IsSelected="True"/>
                                    <ComboBoxItem Content="توقيت الرياض (GMT+3)"/>
                                    <ComboBoxItem Content="توقيت دبي (GMT+4)"/>
                                    <ComboBoxItem Content="توقيت الكويت (GMT+3)"/>
                                    <ComboBoxItem Content="توقيت الدوحة (GMT+3)"/>
                                    <ComboBoxItem Content="توقيت المنامة (GMT+3)"/>
                                    <ComboBoxItem Content="توقيت مسقط (GMT+4)"/>
                                    <ComboBoxItem Content="توقيت عمان (GMT+3)"/>
                                    <ComboBoxItem Content="توقيت دمشق (GMT+3)"/>
                                    <ComboBoxItem Content="توقيت بيروت (GMT+3)"/>
                                    <ComboBoxItem Content="توقيت القاهرة (GMT+2)"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="تنسيق التاريخ:" Margin="0,0,0,5"/>
                                <ComboBox x:Name="DateFormatComboBox" Grid.Row="1" Grid.Column="2" Height="30" Margin="0,20,0,10">
                                    <ComboBoxItem Content="dd/MM/yyyy" IsSelected="True"/>
                                    <ComboBoxItem Content="MM/dd/yyyy"/>
                                    <ComboBoxItem Content="yyyy-MM-dd"/>
                                </ComboBox>
                            </Grid>
                        </GroupBox>
                        
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- تبويب إعدادات المخزون -->
            <TabItem Header="📦 إعدادات المخزون">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <GroupBox Header="تنبيهات المخزون" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <CheckBox x:Name="LowStockAlertsCheckBox" Content="تفعيل تنبيهات المخزون المنخفض" IsChecked="True" Margin="0,0,0,10"/>
                                <CheckBox x:Name="OutOfStockAlertsCheckBox" Content="تفعيل تنبيهات نفاد المخزون" IsChecked="True" Margin="0,0,0,10"/>
                                <CheckBox x:Name="ExpiryAlertsCheckBox" Content="تفعيل تنبيهات انتهاء الصلاحية" IsChecked="True" Margin="0,0,0,10"/>
                                
                                <Grid Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="نسبة تنبيه المخزون المنخفض (%):" Margin="0,0,0,5"/>
                                        <TextBox x:Name="LowStockPercentageTextBox" Height="30" Text="20"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="أيام تنبيه انتهاء الصلاحية:" Margin="0,0,0,5"/>
                                        <TextBox x:Name="ExpiryWarningDaysTextBox" Height="30" Text="30"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                        
                        <GroupBox Header="إعدادات التقييم التلقائي" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <CheckBox x:Name="AutoReorderCheckBox" Content="تفعيل إعادة الطلب التلقائي" IsChecked="False" Margin="0,0,0,10"/>
                                <CheckBox x:Name="AutoPricingCheckBox" Content="تفعيل التسعير التلقائي" IsChecked="False" Margin="0,0,0,10"/>
                                
                                <Grid Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="هامش الربح الافتراضي (%):" Margin="0,0,0,5"/>
                                        <TextBox x:Name="DefaultProfitMarginTextBox" Height="30" Text="25"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="كمية إعادة الطلب الافتراضية:" Margin="0,0,0,5"/>
                                        <TextBox x:Name="DefaultReorderQuantityTextBox" Height="30" Text="50"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                        
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- تبويب إعدادات المبيعات -->
            <TabItem Header="💰 إعدادات المبيعات">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <GroupBox Header="إعدادات الفواتير" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="بادئة رقم الفاتورة:" Margin="0,0,0,5"/>
                                        <TextBox x:Name="InvoicePrefixTextBox" Height="30" Text="INV"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="طول رقم الفاتورة:" Margin="0,0,0,5"/>
                                        <TextBox x:Name="InvoiceNumberLengthTextBox" Height="30" Text="6"/>
                                    </StackPanel>
                                </Grid>
                                
                                <CheckBox x:Name="AutoInvoiceNumberCheckBox" Content="ترقيم تلقائي للفواتير" IsChecked="True" Margin="0,10,0,10"/>
                                <CheckBox x:Name="PrintAfterSaleCheckBox" Content="طباعة الفاتورة تلقائياً بعد البيع" IsChecked="False" Margin="0,0,0,10"/>
                            </StackPanel>
                        </GroupBox>
                        
                        <GroupBox Header="إعدادات الضرائب والخصومات" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="نسبة ضريبة القيمة المضافة (%):" Margin="0,0,0,5"/>
                                        <TextBox x:Name="VATPercentageTextBox" Height="30" Text="15"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="الحد الأقصى للخصم (%):" Margin="0,0,0,5"/>
                                        <TextBox x:Name="MaxDiscountTextBox" Height="30" Text="20"/>
                                    </StackPanel>
                                </Grid>
                                
                                <CheckBox x:Name="ApplyVATCheckBox" Content="تطبيق ضريبة القيمة المضافة تلقائياً" IsChecked="True" Margin="0,10,0,10"/>
                                <CheckBox x:Name="RequireDiscountApprovalCheckBox" Content="يتطلب موافقة للخصومات الكبيرة" IsChecked="True" Margin="0,0,0,10"/>
                            </StackPanel>
                        </GroupBox>
                        
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- تبويب إعدادات النسخ الاحتياطي -->
            <TabItem Header="💾 النسخ الاحتياطي">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <GroupBox Header="إعدادات النسخ الاحتياطي التلقائي" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <CheckBox x:Name="AutoBackupCheckBox" Content="تفعيل النسخ الاحتياطي التلقائي" IsChecked="True" Margin="0,0,0,10"
                                         Checked="AutoBackupCheckBox_Checked" Unchecked="AutoBackupCheckBox_Unchecked"/>

                                <Grid Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="تكرار النسخ الاحتياطي:" Margin="0,0,0,5"/>
                                        <ComboBox x:Name="BackupFrequencyComboBox" Height="30" SelectionChanged="BackupFrequencyComboBox_SelectionChanged">
                                            <ComboBoxItem Content="يومياً" IsSelected="True"/>
                                            <ComboBoxItem Content="أسبوعياً"/>
                                            <ComboBoxItem Content="شهرياً"/>
                                        </ComboBox>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="عدد النسخ المحفوظة:" Margin="0,0,0,5"/>
                                        <TextBox x:Name="BackupRetentionTextBox" Height="30" Text="7"/>
                                    </StackPanel>
                                </Grid>
                                
                                <StackPanel Margin="0,10,0,0">
                                    <TextBlock Text="مسار النسخ الاحتياطي:" Margin="0,0,0,5"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBox x:Name="BackupPathTextBox" Grid.Column="0" Height="30" Text="C:\Backups\MedicalDevices"
                                                 TextChanged="BackupPathTextBox_TextChanged"/>
                                        <Button x:Name="BrowseBackupPathBtn" Grid.Column="1" Content="📁" Width="30" Height="30" Margin="5,0,0,0"
                                                Click="BrowseBackupPathBtn_Click" ToolTip="اختيار مجلد النسخ الاحتياطي"/>
                                    </Grid>
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>
                        
                        <GroupBox Header="حالة النسخ الاحتياطي" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <TextBlock x:Name="BackupStatusTextBlock" Text="جاري تحميل حالة النسخ الاحتياطي..."
                                          FontSize="12" Foreground="#666" Margin="0,0,0,10"/>
                                <TextBlock x:Name="LastBackupTextBlock" Text="آخر نسخة احتياطية: غير محدد"
                                          FontSize="12" Foreground="#666" Margin="0,0,0,10"/>
                                <TextBlock x:Name="NextBackupTextBlock" Text="النسخة التالية: غير محدد"
                                          FontSize="12" Foreground="#666"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="عمليات النسخ الاحتياطي اليدوي" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Button x:Name="CreateBackupBtn" Content="🔄 إنشاء نسخة احتياطية شاملة الآن" Height="40" Margin="0,0,0,10" Background="#28A745" Foreground="White" BorderThickness="0" Click="CreateBackupBtn_Click"/>
                                <Button x:Name="RestoreBackupBtn" Content="📥 استعادة من نسخة احتياطية" Height="40" Margin="0,0,0,10" Background="#17A2B8" Foreground="White" BorderThickness="0" Click="RestoreBackupBtn_Click"/>
                                <Button x:Name="ExportDataBtn" Content="📤 تصدير البيانات" Height="40" Margin="0,0,0,10" Background="#FFC107" Foreground="White" BorderThickness="0" Click="ExportDataBtn_Click"/>
                                <Button x:Name="RefreshBackupStatusBtn" Content="🔄 تحديث حالة النسخ الاحتياطي" Height="35" Margin="0,10,0,0" Background="#6C757D" Foreground="White" BorderThickness="0" Click="RefreshBackupStatusBtn_Click"/>
                            </StackPanel>
                        </GroupBox>
                        
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب إعدادات المخزون المتقدمة -->
            <TabItem Header="📦 المخزون المتقدم">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="إعدادات المخزون المتقدمة" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Button x:Name="OpenAdvancedInventoryBtn" Content="🔧 فتح إعدادات المخزون المتقدمة"
                                        Height="40" Margin="0,10" Background="#17A2B8" Foreground="White"
                                        Click="OpenAdvancedInventoryBtn_Click"/>
                                <TextBlock Text="إدارة الفئات، الوحدات، والإعدادات المتقدمة للمخزون"
                                          FontSize="12" Foreground="#666" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب تصدير البيانات -->
            <TabItem Header="📊 تصدير البيانات">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="تصدير البيانات" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Button x:Name="OpenExportBtn" Content="📤 فتح نافذة تصدير البيانات"
                                        Height="40" Margin="0,10" Background="#28A745" Foreground="White"
                                        Click="OpenExportBtn_Click"/>
                                <TextBlock Text="تصدير البيانات إلى Excel، PDF، وتنسيقات أخرى"
                                          FontSize="12" Foreground="#666" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب الإشعارات والتنبيهات -->
            <TabItem Header="🔔 الإشعارات">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="إعدادات الإشعارات والتنبيهات" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Button x:Name="OpenNotificationsBtn" Content="🔔 فتح إعدادات الإشعارات"
                                        Height="40" Margin="0,10" Background="#FFC107" Foreground="Black"
                                        Click="OpenNotificationsBtn_Click"/>
                                <TextBlock Text="إدارة التنبيهات، الإشعارات، والتذكيرات"
                                          FontSize="12" Foreground="#666" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب تشخيص النظام -->
            <TabItem Header="🔍 تشخيص النظام">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="تشخيص وصيانة النظام" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Button x:Name="RunDiagnosticBtn" Content="🔍 تشغيل تشخيص النظام"
                                        Height="40" Margin="0,10" Background="#DC3545" Foreground="White"
                                        Click="RunDiagnosticBtn_Click"/>
                                <TextBlock Text="فحص النظام، قاعدة البيانات، والأداء العام"
                                          FontSize="12" Foreground="#666" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب النظام الذكي -->
            <TabItem Header="🧠 النظام الذكي">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="إعدادات النظام الذكي" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <Button x:Name="OpenSmartSystemBtn" Content="🧠 فتح النظام الذكي"
                                        Height="40" Margin="0,10" Background="#6F42C1" Foreground="White"
                                        Click="OpenSmartSystemBtn_Click"/>
                                <TextBlock Text="الذكاء الاصطناعي، التحليلات المتقدمة، والتوصيات"
                                          FontSize="12" Foreground="#666" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

        </TabControl>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveSettingsBtn" Content="💾 حفظ الإعدادات" Width="150" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveSettingsBtn_Click"/>
            <Button x:Name="ResetSettingsBtn" Content="🔄 إعادة تعيين" Width="130" Height="40" 
                    Background="#FFC107" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="ResetSettingsBtn_Click"/>
            <Button x:Name="CloseBtn" Content="❌ إغلاق" Width="120" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CloseBtn_Click"/>
        </StackPanel>
    </Grid>
</Window>
