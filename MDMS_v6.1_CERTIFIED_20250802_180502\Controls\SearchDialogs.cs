using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Input;

namespace MedicalDevicesManager.Controls
{
    /// <summary>
    /// نافذة البحث المتقدم
    /// </summary>
    public class AdvancedSearchDialog : Window
    {
        private Dictionary<string, object> _filters;
        private Dictionary<string, UIElement> _filterControls;
        private StackPanel _filtersPanel;
        private List<SearchField> _searchFields;

        public AdvancedSearchDialog(List<SearchField> searchFields, Dictionary<string, object> currentFilters)
        {
            _searchFields = searchFields ?? new List<SearchField>();
            _filters = new Dictionary<string, object>(currentFilters ?? new Dictionary<string, object>());
            _filterControls = new Dictionary<string, UIElement>();
            
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            Title = "بحث متقدم";
            Width = 600;
            Height = 500;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            ResizeMode = ResizeMode.CanResize;

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // العنوان
            var title = new TextBlock
            {
                Text = "🔍 بحث متقدم",
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(15),
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            Grid.SetRow(title, 0);
            mainGrid.Children.Add(title);

            // منطقة الفلاتر
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Margin = new Thickness(15)
            };

            _filtersPanel = new StackPanel();
            scrollViewer.Content = _filtersPanel;
            Grid.SetRow(scrollViewer, 1);
            mainGrid.Children.Add(scrollViewer);

            // إنشاء فلاتر للحقول
            CreateFilterControls();

            // أزرار الإجراءات
            var buttonsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(15)
            };

            var searchButton = new Button
            {
                Content = "🔍 بحث",
                Width = 100,
                Height = 40,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontSize = 14,
                Cursor = Cursors.Hand
            };
            searchButton.Click += SearchButton_Click;

            var clearButton = new Button
            {
                Content = "🗑️ مسح الكل",
                Width = 100,
                Height = 40,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontSize = 14,
                Cursor = Cursors.Hand
            };
            clearButton.Click += ClearButton_Click;

            var cancelButton = new Button
            {
                Content = "❌ إلغاء",
                Width = 100,
                Height = 40,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontSize = 14,
                Cursor = Cursors.Hand
            };
            cancelButton.Click += (s, e) => DialogResult = false;

            buttonsPanel.Children.Add(searchButton);
            buttonsPanel.Children.Add(clearButton);
            buttonsPanel.Children.Add(cancelButton);

            Grid.SetRow(buttonsPanel, 2);
            mainGrid.Children.Add(buttonsPanel);

            Content = mainGrid;
        }

        private void CreateFilterControls()
        {
            // إضافة فلاتر افتراضية إذا لم تكن محددة
            if (!_searchFields.Any())
            {
                _searchFields.AddRange(new[]
                {
                    new SearchField { Name = "Name", DisplayName = "الاسم", Type = SearchFieldType.Text },
                    new SearchField { Name = "Category", DisplayName = "الفئة", Type = SearchFieldType.Text },
                    new SearchField { Name = "Status", DisplayName = "الحالة", Type = SearchFieldType.Dropdown, 
                        Options = new List<string> { "نشط", "غير نشط", "متاح", "غير متاح" } },
                    new SearchField { Name = "CreatedDate", DisplayName = "تاريخ الإنشاء", Type = SearchFieldType.Date },
                    new SearchField { Name = "Price", DisplayName = "السعر", Type = SearchFieldType.Number },
                    new SearchField { Name = "IsActive", DisplayName = "نشط", Type = SearchFieldType.Boolean }
                });
            }

            foreach (var field in _searchFields)
            {
                CreateFilterControl(field);
            }
        }

        private void CreateFilterControl(SearchField field)
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(0, 10, 0, 10)
            };

            var label = new TextBlock
            {
                Text = field.DisplayName,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 8),
                FontSize = 14
            };
            panel.Children.Add(label);

            UIElement filterControl = CreateFilterControlByType(field);
            if (filterControl is FrameworkElement fe)
                fe.Margin = new Thickness(0, 0, 0, 5);
            
            _filterControls[field.Name] = filterControl;
            panel.Children.Add(filterControl);

            // إضافة خط فاصل
            var separator = new Border
            {
                Height = 1,
                Background = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                Margin = new Thickness(0, 10, 0, 0)
            };
            panel.Children.Add(separator);

            _filtersPanel.Children.Add(panel);
        }

        private UIElement CreateFilterControlByType(SearchField field)
        {
            switch (field.Type)
            {
                case SearchFieldType.Text:
                    var textBox = new TextBox
                    {
                        Height = 35,
                        Padding = new Thickness(10, 5, 10, 5),
                        FontSize = 14,
                        BorderBrush = new SolidColorBrush(Color.FromRgb(206, 212, 218)),
                        BorderThickness = new Thickness(1)
                    };
                    
                    if (_filters.ContainsKey(field.Name))
                    {
                        textBox.Text = _filters[field.Name]?.ToString() ?? "";
                    }
                    
                    return textBox;

                case SearchFieldType.Number:
                    var numericPanel = new Grid();
                    numericPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                    numericPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                    numericPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                    
                    var fromTextBox = new TextBox
                    {
                        Height = 35,
                        Padding = new Thickness(10, 5, 10, 5),
                        FontSize = 14,
                        BorderBrush = new SolidColorBrush(Color.FromRgb(206, 212, 218)),
                        BorderThickness = new Thickness(1)
                    };
                    fromTextBox.SetValue(TextBox.TagProperty, "من");
                    Grid.SetColumn(fromTextBox, 0);
                    
                    var toLabel = new TextBlock
                    {
                        Text = "إلى",
                        VerticalAlignment = VerticalAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(10, 0, 10, 0),
                        FontWeight = FontWeights.SemiBold
                    };
                    Grid.SetColumn(toLabel, 1);
                    
                    var toTextBox = new TextBox
                    {
                        Height = 35,
                        Padding = new Thickness(10, 5, 10, 5),
                        FontSize = 14,
                        BorderBrush = new SolidColorBrush(Color.FromRgb(206, 212, 218)),
                        BorderThickness = new Thickness(1)
                    };
                    toTextBox.SetValue(TextBox.TagProperty, "إلى");
                    Grid.SetColumn(toTextBox, 2);
                    
                    numericPanel.Children.Add(fromTextBox);
                    numericPanel.Children.Add(toLabel);
                    numericPanel.Children.Add(toTextBox);
                    
                    return numericPanel;

                case SearchFieldType.Date:
                    var datePanel = new Grid();
                    datePanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                    datePanel.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                    datePanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                    
                    var fromDatePicker = new DatePicker
                    {
                        Height = 35,
                        FontSize = 14
                    };
                    Grid.SetColumn(fromDatePicker, 0);
                    
                    var dateToLabel = new TextBlock
                    {
                        Text = "إلى",
                        VerticalAlignment = VerticalAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(10, 0, 10, 0),
                        FontWeight = FontWeights.SemiBold
                    };
                    Grid.SetColumn(dateToLabel, 1);
                    
                    var toDatePicker = new DatePicker
                    {
                        Height = 35,
                        FontSize = 14
                    };
                    Grid.SetColumn(toDatePicker, 2);
                    
                    datePanel.Children.Add(fromDatePicker);
                    datePanel.Children.Add(dateToLabel);
                    datePanel.Children.Add(toDatePicker);
                    
                    return datePanel;

                case SearchFieldType.Boolean:
                    var checkBox = new CheckBox
                    {
                        Content = field.DisplayName,
                        FontSize = 14,
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    
                    if (_filters.ContainsKey(field.Name) && _filters[field.Name] is bool boolValue)
                    {
                        checkBox.IsChecked = boolValue;
                    }
                    
                    return checkBox;

                case SearchFieldType.Dropdown:
                    var comboBox = new ComboBox
                    {
                        Height = 35,
                        FontSize = 14,
                        BorderBrush = new SolidColorBrush(Color.FromRgb(206, 212, 218)),
                        BorderThickness = new Thickness(1)
                    };
                    
                    comboBox.Items.Add(""); // خيار فارغ
                    foreach (var option in field.Options)
                    {
                        comboBox.Items.Add(option);
                    }
                    
                    if (_filters.ContainsKey(field.Name))
                    {
                        comboBox.SelectedItem = _filters[field.Name]?.ToString();
                    }
                    else
                    {
                        comboBox.SelectedIndex = 0;
                    }
                    
                    return comboBox;

                default:
                    return new TextBox
                    {
                        Height = 35,
                        Padding = new Thickness(10, 5, 10, 5),
                        FontSize = 14
                    };
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _filters.Clear();
            
            foreach (var kvp in _filterControls)
            {
                var fieldName = kvp.Key;
                var control = kvp.Value;
                var field = _searchFields.FirstOrDefault(f => f.Name == fieldName);
                
                if (field == null) continue;

                object filterValue = null;
                
                switch (field.Type)
                {
                    case SearchFieldType.Text:
                        if (control is TextBox textBox && !string.IsNullOrWhiteSpace(textBox.Text))
                        {
                            filterValue = textBox.Text;
                        }
                        break;

                    case SearchFieldType.Number:
                        if (control is Grid numericGrid)
                        {
                            var fromTextBox = numericGrid.Children.OfType<TextBox>().FirstOrDefault();
                            var toTextBox = numericGrid.Children.OfType<TextBox>().LastOrDefault();
                            
                            if (fromTextBox != null && !string.IsNullOrWhiteSpace(fromTextBox.Text) &&
                                decimal.TryParse(fromTextBox.Text, out decimal fromValue))
                            {
                                filterValue = new { From = fromValue, To = (decimal?)null };
                                
                                if (toTextBox != null && !string.IsNullOrWhiteSpace(toTextBox.Text) &&
                                    decimal.TryParse(toTextBox.Text, out decimal toValue))
                                {
                                    filterValue = new { From = fromValue, To = (decimal?)toValue };
                                }
                            }
                        }
                        break;

                    case SearchFieldType.Date:
                        if (control is Grid dateGrid)
                        {
                            var fromDatePicker = dateGrid.Children.OfType<DatePicker>().FirstOrDefault();
                            var toDatePicker = dateGrid.Children.OfType<DatePicker>().LastOrDefault();
                            
                            if (fromDatePicker?.SelectedDate.HasValue == true)
                            {
                                filterValue = new { From = fromDatePicker.SelectedDate.Value, To = toDatePicker?.SelectedDate };
                            }
                        }
                        break;

                    case SearchFieldType.Boolean:
                        if (control is CheckBox checkBox && checkBox.IsChecked.HasValue)
                        {
                            filterValue = checkBox.IsChecked.Value;
                        }
                        break;

                    case SearchFieldType.Dropdown:
                        if (control is ComboBox comboBox && comboBox.SelectedItem != null && 
                            !string.IsNullOrEmpty(comboBox.SelectedItem.ToString()))
                        {
                            filterValue = comboBox.SelectedItem.ToString();
                        }
                        break;
                }
                
                if (filterValue != null)
                {
                    _filters[fieldName] = filterValue;
                }
            }
            
            DialogResult = true;
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            _filters.Clear();
            
            foreach (var control in _filterControls.Values)
            {
                if (control is TextBox textBox)
                {
                    textBox.Text = "";
                }
                else if (control is ComboBox comboBox)
                {
                    comboBox.SelectedIndex = 0;
                }
                else if (control is CheckBox checkBox)
                {
                    checkBox.IsChecked = false;
                }
                else if (control is Grid grid)
                {
                    foreach (var child in grid.Children)
                    {
                        if (child is TextBox gridTextBox)
                        {
                            gridTextBox.Text = "";
                        }
                        else if (child is DatePicker datePicker)
                        {
                            datePicker.SelectedDate = null;
                        }
                    }
                }
            }
        }

        public Dictionary<string, object> GetFilters()
        {
            return _filters;
        }
    }

    /// <summary>
    /// نافذة حفظ البحث
    /// </summary>
    public class SaveSearchDialog : Window
    {
        private TextBox _searchNameTextBox;
        public string SearchName { get; private set; }

        public SaveSearchDialog()
        {
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            Title = "حفظ البحث";
            Width = 400;
            Height = 200;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            ResizeMode = ResizeMode.NoResize;

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // العنوان
            var title = new TextBlock
            {
                Text = "💾 حفظ البحث",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(15),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            Grid.SetRow(title, 0);
            mainGrid.Children.Add(title);

            // محتوى النافذة
            var contentPanel = new StackPanel
            {
                Margin = new Thickness(20)
            };

            var label = new TextBlock
            {
                Text = "اسم البحث:",
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            contentPanel.Children.Add(label);

            _searchNameTextBox = new TextBox
            {
                Height = 35,
                Padding = new Thickness(10, 5, 10, 5),
                FontSize = 14,
                BorderBrush = new SolidColorBrush(Color.FromRgb(206, 212, 218)),
                BorderThickness = new Thickness(1)
            };
            contentPanel.Children.Add(_searchNameTextBox);

            Grid.SetRow(contentPanel, 1);
            mainGrid.Children.Add(contentPanel);

            // أزرار الإجراءات
            var buttonsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(15)
            };

            var saveButton = new Button
            {
                Content = "💾 حفظ",
                Width = 80,
                Height = 35,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand
            };
            saveButton.Click += SaveButton_Click;

            var cancelButton = new Button
            {
                Content = "❌ إلغاء",
                Width = 80,
                Height = 35,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand
            };
            cancelButton.Click += (s, e) => DialogResult = false;

            buttonsPanel.Children.Add(saveButton);
            buttonsPanel.Children.Add(cancelButton);

            Grid.SetRow(buttonsPanel, 2);
            mainGrid.Children.Add(buttonsPanel);

            Content = mainGrid;

            // التركيز على مربع النص
            _searchNameTextBox.Focus();
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(_searchNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم للبحث", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            SearchName = _searchNameTextBox.Text.Trim();
            DialogResult = true;
        }
    }
}
