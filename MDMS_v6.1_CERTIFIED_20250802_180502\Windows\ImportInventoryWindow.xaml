<Window x:Class="MedicalDevicesManager.Windows.ImportInventoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام استيراد قوائم الأجهزة والملحقات" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">
    
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel>
                <TextBlock Text="📥 نظام استيراد قوائم الأجهزة والملحقات" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="استيراد البيانات من ملفات Excel وPDF وWord وإدراجها في المخزون" 
                          FontSize="16" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Import Panel -->
        <Border Grid.Row="1" Background="White" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- File Selection -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📁 اختيار الملف:" VerticalAlignment="Center" Margin="0,0,15,0" FontWeight="Bold" FontSize="16"/>
                    <TextBox x:Name="FilePathTextBox" Width="400" Padding="8" IsReadOnly="True"
                             Text="لم يتم اختيار ملف..." Background="#F9F9F9"/>
                    <Button Content="📂 استعراض" Padding="15,8" Margin="15,0,0,0"
                            Background="#2196F3" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="BrowseFileButton_Click"/>
                    <Button Content="🔍 معاينة" Padding="15,8" Margin="10,0,0,0"
                            Background="#FF9800" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="PreviewButton_Click" x:Name="PreviewButton" IsEnabled="False"/>
                </StackPanel>
                
                <!-- Import Options -->
                <StackPanel Grid.Row="1" Orientation="Horizontal">
                    <TextBlock Text="⚙️ خيارات الاستيراد:" VerticalAlignment="Center" Margin="0,0,15,0" FontWeight="Bold"/>
                    
                    <CheckBox x:Name="OverwriteExistingCheckBox" Content="استبدال العناصر الموجودة" 
                              Margin="0,0,20,0" VerticalAlignment="Center"/>
                    
                    <CheckBox x:Name="ValidateDataCheckBox" Content="التحقق من صحة البيانات" 
                              IsChecked="True" Margin="0,0,20,0" VerticalAlignment="Center"/>
                    
                    <ComboBox x:Name="ImportModeComboBox" Width="150" Margin="0,0,20,0">
                        <ComboBoxItem Content="أجهزة طبية" IsSelected="True"/>
                        <ComboBoxItem Content="مخزون"/>
                        <ComboBoxItem Content="تلقائي"/>
                    </ComboBox>
                    
                    <Button Content="📥 بدء الاستيراد" Padding="20,8" Margin="20,0,0,0"
                            Background="#4CAF50" Foreground="White" BorderThickness="0"
                            FontWeight="Bold" Click="ImportButton_Click" x:Name="ImportButton" IsEnabled="False"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Content Area -->
        <TabControl Grid.Row="2" Margin="20">
            
            <!-- Preview Tab -->
            <TabItem Header="📋 معاينة البيانات">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Preview Info -->
                    <Border Grid.Row="0" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                            CornerRadius="8" Padding="15" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock x:Name="FileTypeText" Text="📄 نوع الملف: --" FontWeight="Bold"/>
                                <TextBlock x:Name="FileSizeText" Text="📏 حجم الملف: --" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock x:Name="TotalRowsText" Text="📊 إجمالي الصفوف: --" FontWeight="Bold"/>
                                <TextBlock x:Name="ValidRowsText" Text="✅ صفوف صحيحة: --" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock x:Name="ErrorRowsText" Text="❌ صفوف خاطئة: --" FontWeight="Bold"/>
                                <TextBlock x:Name="DuplicateRowsText" Text="🔄 صفوف مكررة: --" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="3">
                                <TextBlock x:Name="EstimatedTimeText" Text="⏱️ الوقت المتوقع: --" FontWeight="Bold"/>
                                <TextBlock x:Name="LastUpdateText" Text="🕒 آخر تحديث: --" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- Preview Data Grid -->
                    <DataGrid Grid.Row="1" x:Name="PreviewDataGrid" AutoGenerateColumns="False" 
                              CanUserAddRows="False" CanUserDeleteRows="False"
                              GridLinesVisibility="All" HeadersVisibility="Column"
                              Background="White" AlternatingRowBackground="#F9F9F9">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                            <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="120"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                            <DataGridTextColumn Header="السعر" Binding="{Binding PriceFormatted}" Width="100"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="الماركة" Binding="{Binding Brand}" Width="100"/>
                            <DataGridTextColumn Header="الموديل" Binding="{Binding Model}" Width="100"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- Import Log Tab -->
            <TabItem Header="📝 سجل الاستيراد">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Log Controls -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Content="🗑️ مسح السجل" Padding="10,5" Margin="0,0,10,0"
                                Background="#F44336" Foreground="White" BorderThickness="0"
                                Click="ClearLogButton_Click"/>
                        <Button Content="💾 حفظ السجل" Padding="10,5" Margin="0,0,10,0"
                                Background="#4CAF50" Foreground="White" BorderThickness="0"
                                Click="SaveLogButton_Click"/>
                        <CheckBox x:Name="AutoScrollCheckBox" Content="تمرير تلقائي" IsChecked="True" 
                                  VerticalAlignment="Center" Margin="20,0,0,0"/>
                    </StackPanel>
                    
                    <!-- Log Text -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" x:Name="LogScrollViewer">
                        <TextBox x:Name="LogTextBox" IsReadOnly="True" TextWrapping="Wrap"
                                 Background="Black" Foreground="Lime" FontFamily="Consolas"
                                 FontSize="12" Padding="10"/>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- Help Tab -->
            <TabItem Header="❓ المساعدة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        
                        <!-- Excel Format -->
                        <Border Background="#E8F5E8" BorderBrush="#4CAF50" BorderThickness="1" 
                                CornerRadius="8" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="📊 تنسيق ملفات Excel" FontSize="16" FontWeight="Bold" 
                                          Foreground="#2E7D32" Margin="0,0,0,10"/>
                                <TextBlock TextWrapping="Wrap">
                                    <Run Text="الأعمدة المطلوبة:"/>
                                    <LineBreak/>
                                    <Run Text="• العمود A: اسم العنصر (مطلوب)"/>
                                    <LineBreak/>
                                    <Run Text="• العمود B: الفئة (مطلوب)"/>
                                    <LineBreak/>
                                    <Run Text="• العمود C: الوصف"/>
                                    <LineBreak/>
                                    <Run Text="• العمود D: السعر (رقم)"/>
                                    <LineBreak/>
                                    <Run Text="• العمود E: الكمية (رقم)"/>
                                    <LineBreak/>
                                    <Run Text="• العمود F: الوحدة"/>
                                    <LineBreak/>
                                    <Run Text="• العمود G: الماركة"/>
                                    <LineBreak/>
                                    <Run Text="• العمود H: الموديل"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                        
                        <!-- PDF Format -->
                        <Border Background="#FFF3E0" BorderBrush="#FF9800" BorderThickness="1" 
                                CornerRadius="8" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="📄 تنسيق ملفات PDF" FontSize="16" FontWeight="Bold" 
                                          Foreground="#F57C00" Margin="0,0,0,10"/>
                                <TextBlock TextWrapping="Wrap">
                                    <Run Text="يجب أن يحتوي الملف على:"/>
                                    <LineBreak/>
                                    <Run Text="• جداول منظمة مع رؤوس أعمدة واضحة"/>
                                    <LineBreak/>
                                    <Run Text="• نص قابل للاستخراج (ليس صورة)"/>
                                    <LineBreak/>
                                    <Run Text="• تنسيق ثابت للبيانات"/>
                                    <LineBreak/>
                                    <Run Text="• فصل البيانات بمسافات أو خطوط"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                        
                        <!-- Word Format -->
                        <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                                CornerRadius="8" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="📝 تنسيق ملفات Word" FontSize="16" FontWeight="Bold" 
                                          Foreground="#1976D2" Margin="0,0,0,10"/>
                                <TextBlock TextWrapping="Wrap">
                                    <Run Text="التنسيقات المدعومة:"/>
                                    <LineBreak/>
                                    <Run Text="• جداول Word مع رؤوس أعمدة"/>
                                    <LineBreak/>
                                    <Run Text="• قوائم منظمة مع فواصل"/>
                                    <LineBreak/>
                                    <Run Text="• نص منسق مع أسطر منفصلة"/>
                                    <LineBreak/>
                                    <Run Text="• ملفات .docx و .doc"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                        
                        <!-- Tips -->
                        <Border Background="#F3E5F5" BorderBrush="#9C27B0" BorderThickness="1" 
                                CornerRadius="8" Padding="15">
                            <StackPanel>
                                <TextBlock Text="💡 نصائح مهمة" FontSize="16" FontWeight="Bold" 
                                          Foreground="#7B1FA2" Margin="0,0,0,10"/>
                                <TextBlock TextWrapping="Wrap">
                                    <Run Text="• تأكد من صحة البيانات قبل الاستيراد"/>
                                    <LineBreak/>
                                    <Run Text="• استخدم خيار 'التحقق من صحة البيانات'"/>
                                    <LineBreak/>
                                    <Run Text="• راجع المعاينة قبل بدء الاستيراد"/>
                                    <LineBreak/>
                                    <Run Text="• احفظ نسخة احتياطية من قاعدة البيانات"/>
                                    <LineBreak/>
                                    <Run Text="• استخدم ملفات صغيرة للاختبار أولاً"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                        
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
        </TabControl>

        <!-- Footer -->
        <Border Grid.Row="3" Background="#1976D2" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="StatusText" Text="جاهز لاستيراد الملفات" 
                              Foreground="White" VerticalAlignment="Center" FontWeight="Bold"/>
                    <ProgressBar x:Name="ImportProgressBar" Width="200" Height="20" 
                                 Margin="20,0,0,0" Visibility="Collapsed"/>
                    <TextBlock x:Name="ProgressText" Text="" 
                              Foreground="White" VerticalAlignment="Center" Margin="10,0,0,0"/>
                </StackPanel>
                
                <Button Grid.Column="1" Content="❌ إغلاق" Padding="15,8"
                        Background="#F44336" Foreground="White" BorderThickness="0"
                        FontWeight="Bold" Click="Close_Click"/>
            </Grid>
        </Border>
    </Grid>
</Window>
