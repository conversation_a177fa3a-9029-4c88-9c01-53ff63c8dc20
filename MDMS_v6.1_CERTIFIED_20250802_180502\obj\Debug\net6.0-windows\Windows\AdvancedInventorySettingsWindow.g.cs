﻿#pragma checksum "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DD52BEC9984AB17457398DB65741755D515B0B66"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// AdvancedInventorySettingsWindow
    /// </summary>
    public partial class AdvancedInventorySettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 30 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyDefaultsBtn;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckAlertsBtn;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InventorySettingsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentStockTextBlock;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinimumStockTextBox;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReorderLevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaximumStockTextBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableLowStockAlertCheckBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableExpiryAlertCheckBox;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpiryAlertDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlertEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveSettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/advancedinventorysettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ApplyDefaultsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            this.ApplyDefaultsBtn.Click += new System.Windows.RoutedEventHandler(this.ApplyDefaultsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CheckAlertsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            this.CheckAlertsBtn.Click += new System.Windows.RoutedEventHandler(this.CheckAlertsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.InventorySettingsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 51 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            this.InventorySettingsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InventorySettingsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SettingsPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.ProductNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CurrentStockTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.MinimumStockTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ReorderLevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.MaximumStockTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.EnableLowStockAlertCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.EnableExpiryAlertCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.ExpiryAlertDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.AlertEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.SaveSettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            this.SaveSettingsBtn.Click += new System.Windows.RoutedEventHandler(this.SaveSettingsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 182 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 6:
            
            #line 87 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditSettingsBtn_Click);
            
            #line default
            #line hidden
            break;
            case 7:
            
            #line 91 "..\..\..\..\Windows\AdvancedInventorySettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSettingsBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

