# PowerShell Script for Creating Desktop Shortcut
# Medical Devices Management System v2.1 - Arabic Support
# Encoding: UTF-8

# Set console encoding for Arabic support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Clear screen and show header
Clear-Host

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    إنشاء اختصار سطح المكتب" -ForegroundColor Yellow
Write-Host "    نظام إدارة الأجهزة الطبية v2.1" -ForegroundColor Yellow
Write-Host "    إصدار شهادة الجودة" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    # Get current directory
    $CurrentPath = Get-Location
    
    # Set paths
    $ExePath = Join-Path $CurrentPath "Distribution\MedicalDevicesManager_v2.1_QualityCertificate\MedicalDevicesManager.exe"
    $WorkingDir = Join-Path $CurrentPath "Distribution\MedicalDevicesManager_v2.1_QualityCertificate"
    
    # Get desktop path
    $DesktopPath = [Environment]::GetFolderPath("Desktop")
    
    Write-Host "المسار الحالي: $CurrentPath" -ForegroundColor Green
    Write-Host "مسار الملف التنفيذي: $ExePath" -ForegroundColor Green
    Write-Host "مجلد العمل: $WorkingDir" -ForegroundColor Green
    Write-Host "مسار سطح المكتب: $DesktopPath" -ForegroundColor Green
    Write-Host ""
    
    # Check if executable exists
    if (-not (Test-Path $ExePath)) {
        Write-Host "❌ خطأ: الملف التنفيذي غير موجود!" -ForegroundColor Red
        Write-Host "المسار المتوقع: $ExePath" -ForegroundColor Red
        Write-Host ""
        Write-Host "تأكد من وجود الإصدار v2.1 في مجلد Distribution" -ForegroundColor Yellow
        Read-Host "اضغط Enter للمتابعة"
        exit 1
    }
    
    Write-Host "✅ تم العثور على الإصدار v2.1" -ForegroundColor Green
    
    # Check working directory
    if (-not (Test-Path $WorkingDir)) {
        Write-Host "❌ خطأ: مجلد العمل غير موجود!" -ForegroundColor Red
        Read-Host "اضغط Enter للمتابعة"
        exit 1
    }
    
    Write-Host "✅ تم التحقق من مجلد العمل" -ForegroundColor Green
    Write-Host ""
    
    # Create shortcut
    Write-Host "جاري إنشاء اختصار سطح المكتب..." -ForegroundColor Yellow
    
    $WshShell = New-Object -ComObject WScript.Shell
    
    # Create shortcut with Arabic name
    $ShortcutPath = Join-Path $DesktopPath "نظام إدارة الأجهزة الطبية v2.1.lnk"
    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    
    # Set shortcut properties
    $Shortcut.TargetPath = $ExePath
    $Shortcut.WorkingDirectory = $WorkingDir
    $Shortcut.Description = "نظام إدارة الأجهزة الطبية v2.1 - إصدار شهادة الجودة"
    $Shortcut.IconLocation = "$ExePath,0"
    
    # Save shortcut
    $Shortcut.Save()
    
    # Verify creation
    if (Test-Path $ShortcutPath) {
        Write-Host ""
        Write-Host "✅ تم إنشاء الاختصار بنجاح!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎉 الاختصار متاح الآن على سطح المكتب باسم:" -ForegroundColor Cyan
        Write-Host "نظام إدارة الأجهزة الطبية v2.1" -ForegroundColor Yellow
        Write-Host ""
        
        # Show version features
        Write-Host "🚀 مزايا الإصدار v2.1:" -ForegroundColor Cyan
        Write-Host "• ملف تنفيذي مستقل - لا يحتاج .NET منفصل" -ForegroundColor White
        Write-Host "• واجهة عربية محسنة مع دعم كامل للترميز" -ForegroundColor White
        Write-Host "• قاعدة بيانات متطورة - 17 جدول متكامل" -ForegroundColor White
        Write-Host "• أداء محسن وسرعة أكبر" -ForegroundColor White
        Write-Host "• شهادة جودة معتمدة للاستخدام التجاري" -ForegroundColor White
        Write-Host "• 12 وحدة متكاملة للإدارة الشاملة" -ForegroundColor White
        Write-Host ""
        
        Write-Host "💡 يمكنك الآن:" -ForegroundColor Cyan
        Write-Host "1. النقر المزدوج على الاختصار لتشغيل النظام" -ForegroundColor White
        Write-Host "2. سحب الاختصار إلى شريط المهام" -ForegroundColor White
        Write-Host "3. إنشاء نسخة من الاختصار في أي مكان" -ForegroundColor White
        Write-Host "4. تشغيل النظام بدون سطر الأوامر" -ForegroundColor White
        Write-Host ""
        
        # Show shortcut details
        Write-Host "📊 تفاصيل الاختصار:" -ForegroundColor Cyan
        Write-Host "الهدف: $ExePath" -ForegroundColor Gray
        Write-Host "مجلد العمل: $WorkingDir" -ForegroundColor Gray
        Write-Host "الوصف: نظام إدارة الأجهزة الطبية v2.1 - إصدار شهادة الجودة" -ForegroundColor Gray
        Write-Host ""
        
        # Test shortcut
        Write-Host "🧪 اختبار الاختصار:" -ForegroundColor Cyan
        Write-Host "هل تريد اختبار تشغيل النظام الآن؟ (y/n): " -ForegroundColor Yellow -NoNewline
        $TestChoice = Read-Host
        
        if ($TestChoice -eq "y" -or $TestChoice -eq "Y" -or $TestChoice -eq "نعم") {
            Write-Host ""
            Write-Host "جاري تشغيل النظام للاختبار..." -ForegroundColor Yellow
            Start-Process -FilePath $ExePath -WorkingDirectory $WorkingDir
            Write-Host "✅ تم تشغيل النظام بنجاح!" -ForegroundColor Green
        }
        
    } else {
        Write-Host ""
        Write-Host "❌ فشل في إنشاء الاختصار!" -ForegroundColor Red
        Write-Host ""
        Write-Host "الأسباب المحتملة:" -ForegroundColor Yellow
        Write-Host "1. عدم وجود صلاحيات كافية" -ForegroundColor White
        Write-Host "2. مشكلة في مسار سطح المكتب" -ForegroundColor White
        Write-Host "3. مشكلة في COM Object" -ForegroundColor White
        Write-Host ""
        
        # Provide manual instructions
        Write-Host "📋 تعليمات الإنشاء اليدوي:" -ForegroundColor Cyan
        Write-Host "1. انقر بالزر الأيمن على سطح المكتب" -ForegroundColor White
        Write-Host "2. اختر 'جديد' ثم 'اختصار'" -ForegroundColor White
        Write-Host "3. انسخ والصق هذا المسار:" -ForegroundColor White
        Write-Host "   `"$ExePath`"" -ForegroundColor Yellow
        Write-Host "4. اضغط 'التالي'" -ForegroundColor White
        Write-Host "5. اكتب الاسم: نظام إدارة الأجهزة الطبية v2.1" -ForegroundColor White
        Write-Host "6. اضغط 'إنهاء'" -ForegroundColor White
        Write-Host ""
        Write-Host "ثم انقر بالزر الأيمن على الاختصار واختر 'خصائص':" -ForegroundColor White
        Write-Host "- في 'بدء في' اكتب: $WorkingDir" -ForegroundColor Yellow
        Write-Host "- في 'تعليق' اكتب: نظام إدارة الأجهزة الطبية v2.1" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ حدث خطأ أثناء إنشاء الاختصار!" -ForegroundColor Red
    Write-Host "تفاصيل الخطأ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "الحلول المقترحة:" -ForegroundColor Yellow
    Write-Host "1. شغل PowerShell كمدير (Run as Administrator)" -ForegroundColor White
    Write-Host "2. تأكد من تفعيل تنفيذ سكريبت PowerShell:" -ForegroundColor White
    Write-Host "   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Gray
    Write-Host "3. استخدم الطريقة اليدوية" -ForegroundColor White
    Write-Host ""
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎯 نصائح للاستخدام الأمثل:" -ForegroundColor Yellow
Write-Host "• استخدم الاختصار بدلاً من الملف مباشرة" -ForegroundColor White
Write-Host "• تأكد من عدم نقل مجلد النظام بعد إنشاء الاختصار" -ForegroundColor White
Write-Host "• أنشئ نسخة احتياطية من الاختصار" -ForegroundColor White
Write-Host "• استخدم أدوات التشخيص في النظام عند الحاجة" -ForegroundColor White
Write-Host ""

Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
