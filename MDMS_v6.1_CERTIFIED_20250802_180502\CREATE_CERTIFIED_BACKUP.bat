@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    🏆 إنشاء نسخة احتياطية للإصدار المعتمد
echo    Creating Certified Version Backup
echo ========================================
echo.

set "BACKUP_DATE=%date:~-4%%date:~3,2%%date:~0,2%"
set "BACKUP_TIME=%time:~0,2%%time:~3,2%%time:~6,2%"
set "BACKUP_TIME=%BACKUP_TIME: =0%"
set "BACKUP_NAME=MDMS_v6.1_CERTIFIED_%BACKUP_DATE%_%BACKUP_TIME%"
set "BACKUP_DIR=..\CERTIFIED_VERSIONS\%BACKUP_NAME%"

echo 📅 تاريخ النسخة: %BACKUP_DATE%
echo ⏰ وقت النسخة: %BACKUP_TIME%
echo 📁 اسم النسخة: %BACKUP_NAME%
echo.

echo 🔧 إنشاء مجلد النسخة الاحتياطية...
if not exist "..\CERTIFIED_VERSIONS" mkdir "..\CERTIFIED_VERSIONS"
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

echo ✅ تم إنشاء المجلد بنجاح
echo.

echo 📋 نسخ الملفات الأساسية...
copy "*.csproj" "%BACKUP_DIR%\" >nul 2>&1
copy "*.sln" "%BACKUP_DIR%\" >nul 2>&1
copy "*.cs" "%BACKUP_DIR%\" >nul 2>&1
copy "*.xaml" "%BACKUP_DIR%\" >nul 2>&1
copy "*.xaml.cs" "%BACKUP_DIR%\" >nul 2>&1
copy "*.config" "%BACKUP_DIR%\" >nul 2>&1
copy "*.json" "%BACKUP_DIR%\" >nul 2>&1
copy "*.bat" "%BACKUP_DIR%\" >nul 2>&1
copy "*.md" "%BACKUP_DIR%\" >nul 2>&1

echo 📁 نسخ المجلدات...
if exist "Windows" (
    xcopy "Windows" "%BACKUP_DIR%\Windows\" /E /I /Q >nul 2>&1
    echo   ✅ مجلد Windows
)

if exist "Controls" (
    xcopy "Controls" "%BACKUP_DIR%\Controls\" /E /I /Q >nul 2>&1
    echo   ✅ مجلد Controls
)

if exist "Services" (
    xcopy "Services" "%BACKUP_DIR%\Services\" /E /I /Q >nul 2>&1
    echo   ✅ مجلد Services
)

if exist "Resources" (
    xcopy "Resources" "%BACKUP_DIR%\Resources\" /E /I /Q >nul 2>&1
    echo   ✅ مجلد Resources
)

if exist "bin" (
    xcopy "bin" "%BACKUP_DIR%\bin\" /E /I /Q >nul 2>&1
    echo   ✅ مجلد bin
)

if exist "obj" (
    xcopy "obj" "%BACKUP_DIR%\obj\" /E /I /Q >nul 2>&1
    echo   ✅ مجلد obj
)

echo 💾 نسخ قاعدة البيانات...
copy "*.db" "%BACKUP_DIR%\" >nul 2>&1
copy "*.db-shm" "%BACKUP_DIR%\" >nul 2>&1
copy "*.db-wal" "%BACKUP_DIR%\" >nul 2>&1
echo   ✅ قاعدة البيانات

echo.
echo 📄 إنشاء ملف معلومات النسخة...
(
echo # 🏆 معلومات الإصدار المعتمد
echo.
echo **اسم الإصدار:** Medical Devices Management System v6.1 - Arabic Enhanced Edition
echo **تاريخ النسخة:** %date%
echo **وقت النسخة:** %time%
echo **رقم البناء:** 6.1.2025.0802
echo **الحالة:** ✅ معتمد للإنتاج
echo.
echo ## 📋 محتويات النسخة:
echo - ✅ جميع الملفات المصدرية
echo - ✅ النوافذ المحسنة للعربية
echo - ✅ مكتبة الأنماط العربية
echo - ✅ قاعدة البيانات مع البيانات التجريبية
echo - ✅ ملفات التشغيل والاختصارات
echo - ✅ التوثيق الشامل
echo.
echo ## 🎯 المزايا:
echo - واجهة عربية احترافية
echo - 12 وحدة متكاملة
echo - 17 جدول قاعدة بيانات
echo - أدوات متقدمة للإدارة
echo.
echo **هذا إصدار معتمد وجاهز للإنتاج**
) > "%BACKUP_DIR%\VERSION_INFO.md"

echo ✅ تم إنشاء ملف معلومات النسخة
echo.

echo 🔍 التحقق من النسخة...
if exist "%BACKUP_DIR%\MedicalDevicesManager.csproj" (
    echo   ✅ ملف المشروع موجود
) else (
    echo   ❌ ملف المشروع مفقود
)

if exist "%BACKUP_DIR%\MainWindow.xaml" (
    echo   ✅ النافذة الرئيسية موجودة
) else (
    echo   ❌ النافذة الرئيسية مفقودة
)

if exist "%BACKUP_DIR%\Resources\ArabicStyles.xaml" (
    echo   ✅ ملف الأنماط العربية موجود
) else (
    echo   ❌ ملف الأنماط العربية مفقود
)

echo.
echo 📊 إحصائيات النسخة:
for /f %%i in ('dir "%BACKUP_DIR%" /s /-c ^| find "File(s)"') do set FILES=%%i
echo   📁 عدد الملفات: %FILES%

for /f %%i in ('dir "%BACKUP_DIR%" /s /-c ^| find "Dir(s)"') do set DIRS=%%i
echo   📂 عدد المجلدات: %DIRS%

echo.
echo 🎉 تم إنشاء النسخة الاحتياطية بنجاح!
echo.
echo 📍 موقع النسخة:
echo %BACKUP_DIR%
echo.
echo 💡 يمكنك الآن استخدام هذه النسخة كإصدار مرجعي معتمد
echo.
echo اضغط أي مفتاح للمتابعة...
pause >nul
