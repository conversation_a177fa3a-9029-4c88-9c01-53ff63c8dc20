{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"MedicalDevicesManager/1.0.0": {"dependencies": {"ClosedXML": "0.105.0", "EPPlus": "6.2.10", "Microsoft.EntityFrameworkCore.Sqlite": "7.0.0", "System.Text.Json": "8.0.5", "itext7": "8.0.2"}, "runtime": {"MedicalDevicesManager.dll": {}}}, "ClosedXML/0.105.0": {"dependencies": {"ClosedXML.Parser": "2.0.0", "DocumentFormat.OpenXml": "3.1.1", "ExcelNumberFormat": "1.1.0", "RBush.Signed": "4.0.0", "SixLabors.Fonts": "1.0.0"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "0.105.0.0", "fileVersion": "0.105.0.0"}}}, "ClosedXML.Parser/2.0.0": {"runtime": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "DocumentFormat.OpenXml/3.1.1": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.1.1"}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}}, "DocumentFormat.OpenXml.Framework/3.1.1": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net6.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}}, "EPPlus/6.2.10": {"dependencies": {"EPPlus.Interfaces": "6.1.1", "EPPlus.System.Drawing": "6.1.1", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.2.1", "System.Security.Cryptography.Pkcs": "6.0.3", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/net6.0/EPPlus.dll": {"assemblyVersion": "6.2.10.0", "fileVersion": "6.2.10.0"}}}, "EPPlus.Interfaces/6.1.1": {"runtime": {"lib/net6.0/EPPlus.Interfaces.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.1.1.0"}}}, "EPPlus.System.Drawing/6.1.1": {"dependencies": {"EPPlus.Interfaces": "6.1.1", "System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/EPPlus.System.Drawing.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.1.1.0"}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "itext/8.0.2": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "1.1.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Collections.NonGeneric": "4.3.0", "System.Diagnostics.Process": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "itext.commons": "8.0.2"}, "runtime": {"lib/netstandard2.0/itext.barcodes.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.bouncy-castle-connector.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.forms.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.io.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.kernel.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.layout.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.pdfa.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.sign.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.styledxmlparser.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.svg.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}}}, "itext.commons/8.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "7.0.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/itext.commons.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}}}, "itext7/8.0.2": {"dependencies": {"itext": "8.0.2"}}, "Microsoft.Data.Sqlite.Core/7.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Microsoft.EntityFrameworkCore/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.0", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "7.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.2"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "7.0.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.IO.RecyclableMemoryStream/2.2.1": {"runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "RBush.Signed/4.0.0": {"runtime": {"lib/netstandard2.0/RBush.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.2", "SQLitePCLRaw.provider.e_sqlite3": "2.1.2"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.core/2.1.2": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"runtimeTargets": {"runtimes/alpine-arm/native/libe_sqlite3.so": {"rid": "alpine-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-arm64/native/libe_sqlite3.so": {"rid": "alpine-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Process/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}}, "System.Formats.Asn1/6.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net6.0/System.IO.Packaging.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Memory/4.5.3": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs/6.0.3": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1823.26907"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.5": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}}}, "libraries": {"MedicalDevicesManager/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ClosedXML/0.105.0": {"type": "package", "serviceable": true, "sha512": "sha512-U0hAdnYyPvF7TqHMFloxrS7pmozab79tFFF4c/bgPtqeelUs7ILpUd3r3c7C0a/DXsUZb3k1n4Pf7Q2LMyMQOg==", "path": "closedxml/0.105.0", "hashPath": "closedxml.0.105.0.nupkg.sha512"}, "ClosedXML.Parser/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ngTqjYreDYNytG1W5d3ewHsw0ukmmrgV7EKnS4/40rXoYZGt07jrBvo+N+GxT49rcageUMUiprV0jYT4nwVBHQ==", "path": "closedxml.parser/2.0.0", "hashPath": "closedxml.parser.2.0.0.nupkg.sha512"}, "DocumentFormat.OpenXml/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2z9QBzeTLNNKWM9SaOSDMegfQk/7hDuElOsmF77pKZMkFRP/GHA/W/4yOAQD9kn15N/FsFxHn3QVYkatuZghiA==", "path": "documentformat.openxml/3.1.1", "hashPath": "documentformat.openxml.3.1.1.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6APEp/ElZV58S/4v8mf4Ke3ONEDORs64MqdD64Z7wWpcHANB9oovQsGIwtqjnKihulOj7T0a6IxHIHOfMqKOng==", "path": "documentformat.openxml.framework/3.1.1", "hashPath": "documentformat.openxml.framework.3.1.1.nupkg.sha512"}, "EPPlus/6.2.10": {"type": "package", "serviceable": true, "sha512": "sha512-U/kj9mKoUMOMUvNIMwbD7xy4R/UTyc9baMVxDWQhYaeIueCviC12feQ3qbcUJZc8yyodC8gAj0BhzBg/wFIjTg==", "path": "epplus/6.2.10", "hashPath": "epplus.6.2.10.nupkg.sha512"}, "EPPlus.Interfaces/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y7dkrOoE1ZR9Vgy1Jf2rEIaTf3SHlUjYt01NklP+F5Qh7S2ruPbzTcpYLRWMeXiG8XL8h2jqX4CyIkFt3NQGZw==", "path": "epplus.interfaces/6.1.1", "hashPath": "epplus.interfaces.6.1.1.nupkg.sha512"}, "EPPlus.System.Drawing/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lRF5gHYrmkHOOiLMI0t6q8zNYjUrzRgAM5BCXumv5xiqXko8fx3AWI+HCNZfhEqVFGOop+42KfR5GiUcCoyoMw==", "path": "epplus.system.drawing/6.1.1", "hashPath": "epplus.system.drawing.6.1.1.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "itext/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-79IRTeZZirzRDEtB1PtUMSpsxaJiFz5rAXY4/VtSipsi0qlQpJ/88jxd2SsfX0IggH+wlrsiAc0Y8X2et2T+Bg==", "path": "itext/8.0.2", "hashPath": "itext.8.0.2.nupkg.sha512"}, "itext.commons/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/F7j7XERA84XgIXDb4j/so1GagAToraVKZIRAdk/ibkvYoU1uwuILU2Ly28sSFf12NN7WIBQHKSk7njJl7Nk8g==", "path": "itext.commons/8.0.2", "hashPath": "itext.commons.8.0.2.nupkg.sha512"}, "itext7/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IuFfSlMSTLqVGCIsxpcDL9kCtEfppSBjq4V+RgfrxnqEC+iNF4tCHiIdl8quyzTDbaHF4vNufxe2NXMJMkPlSw==", "path": "itext7/8.0.2", "hashPath": "itext7.8.0.2.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WC7SANtaFTmQ/WPyhrE96h88MrH28C7kTBenDf5Eo+IR6CbWM0Uw2pR2++tyYr3qe/zSIsIYroEupB1mLg/adw==", "path": "microsoft.data.sqlite.core/7.0.0", "hashPath": "microsoft.data.sqlite.core.7.0.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bl6KYfbFSIW3QIRHAp931iR5h01qHjKghdpAtncwbzNUs0+IUZ+XfwkIU0sQsR33ufGvi3u4dZMIYYFysjpHAA==", "path": "microsoft.dotnet.platformabstractions/1.1.0", "hashPath": "microsoft.dotnet.platformabstractions.1.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9W+IfmAzMrp2ZpKZLhgTlWljSBM9Erldis1us61DAGi+L7Q6vilTbe1G2zDxtYO8F2H0I0Qnupdx5Cp4s2xoZw==", "path": "microsoft.entityframeworkcore/7.0.0", "hashPath": "microsoft.entityframeworkcore.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pfu3Zjj5+d2Gt27oE9dpGiF/VobBB+s5ogrfI9sBsXQE1SG49RqVz5+IyeNnzhyejFrPIQsPDRMchhcojy4Hbw==", "path": "microsoft.entityframeworkcore.abstractions/7.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qkd2H+jLe37o5ku+LjT6qf7kAHY75Yfn2bBDQgqr13DTOLYpEy1Mt93KPFjaZvIu/srEcbfGGMRL7urKm5zN8Q==", "path": "microsoft.entityframeworkcore.analyzers/7.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "path": "microsoft.entityframeworkcore.relational/7.0.0", "hashPath": "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tufYoEHetVeALPRqzCi4YukL+uoS0pBi/6m3uSCuM5NxO/tTbuizvvBRa3qJNYZOvF3K7m4lUmmcCymKr3JSSQ==", "path": "microsoft.entityframeworkcore.sqlite/7.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aUClrz1PT06fPDY+9f2IeDhYXj3/oPxM0r3I6syiyP3Th59hObVI0QsRu5+y7FbJIkO3NgyAi+e2vzWGhmBVbQ==", "path": "microsoft.entityframeworkcore.sqlite.core/7.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.core.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tq2wXyh3fL17EMF2bXgRhU7JrbO3on93MRKYxzz4JzzvuGSA1l0W3GI9/tl8EO89TH+KWEymP7bcFway6z9fXg==", "path": "microsoft.extensions.configuration/6.0.0", "hashPath": "microsoft.extensions.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V4Dth2cYMZpw3HhGw9XUDIijpI6gN+22LDt0AhufIgOppCUfpWX4483OmN+dFXRJkJLc8Tv0Q8QK+1ingT2+KQ==", "path": "microsoft.extensions.configuration.fileextensions/6.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GJGery6QytCzS/BxJ96klgG9in3uH26KcUBbiVG/coNDXCRq6LGVVlUT4vXq34KPuM+R2av+LeYdX9h4IZOCUg==", "path": "microsoft.extensions.configuration.json/6.0.0", "hashPath": "microsoft.extensions.configuration.json.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QvkL7l0nM8udt3gfyu0Vw8bbCXblxaKOl7c2oBfgGy4LCURRaL9XWZX1FWJrQc43oMokVneVxH38iz+bY1sbhg==", "path": "microsoft.extensions.fileproviders.physical/6.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-T5ahjOqWFMTSb9wFHKFNAcGXm35BxbUbwARtAPLSSPPFehcLz5mwDsKO1RR9R2aZ2Lk1BNQC7Ja63onOBE6rpA==", "path": "microsoft.io.recyclablememorystream/2.2.1", "hashPath": "microsoft.io.recyclablememorystream.2.2.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lw1/VwLH1yxz6SfFEjVRCN0pnflLEsWgnV4qsdJ512/HhTwnKXUG+zDQ4yTO3K/EJQemGoNaBHX5InISNKTzUQ==", "path": "microsoft.win32.registry/4.3.0", "hashPath": "microsoft.win32.registry.4.3.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "RBush.Signed/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aP5KQxL5RnFNGW1f0euYVBfCatkLw5iEzMRJcXKq8LWWP4Cp3+qoSq1tDDL2vvJ2rM0ychmVMa2VaEKLS6uX4w==", "path": "rbush.signed/4.0.0", "hashPath": "rbush.signed.4.0.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ilkvNhrTersLmIVAcDwwPqfhUFCg19Z1GVMvCSi3xk6Akq94f4qadLORQCq/T8+9JgMiPs+F/NECw5uauviaNw==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-A8EBepVqY2lnAp3a8jnhbgzF2tlj2S3HcJQGANTYg/TbYbKa8Z5cM1h74An/vy0svhfzT7tVY0sFmUglLgv+2g==", "path": "sqlitepclraw.core/2.1.2", "hashPath": "sqlitepclraw.core.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zibGtku8M4Eea1R3ZCAxc86QbNvyEN17mAcQkvWKBuHvRpMiK2g5anG4R5Be7cWKSd1i6baYz8y4dMMAKcXKPg==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-lxCZarZdvAsMl2zw9bXHrXK6RxVhB4b23iTFhCOdHFhxfbsxLxWf+ocvswJwR/9Wh/E//ddMi+wJGqUKV7VwoA==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.2.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.Process/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "path": "system.diagnostics.process/4.3.0", "hashPath": "system.diagnostics.process.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "path": "system.reflection.typeextensions/4.1.0", "hashPath": "system.reflection.typeextensions.4.1.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-18UT1BdZ4TYFBHk/wuq7JzCdE3X75z81X+C2rXqIlmEnC21Pm60spGV/dKQSzbyomstqYE33EuN5hfnC86VFxA==", "path": "system.security.cryptography.pkcs/6.0.3", "hashPath": "system.security.cryptography.pkcs.6.0.3.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}}}