# إنشاء اختصار نظام إدارة الأجهزة الطبية على سطح المكتب
# Create Medical Devices Management System Desktop Shortcut

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   إنشاء اختصار نظام إدارة الأجهزة الطبية" -ForegroundColor Yellow
Write-Host "   Creating Medical Devices System Shortcut" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔧 جاري إنشاء اختصار على سطح المكتب..." -ForegroundColor Green
Write-Host "Creating desktop shortcut..." -ForegroundColor Green
Write-Host ""

try {
    # الحصول على المسارات
    $currentDir = Get-Location
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = Join-Path $desktopPath "نظام إدارة الأجهزة الطبية.lnk"
    $batFilePath = Join-Path $desktopPath "Medical-Devices-System.bat"
    
    # إنشاء ملف batch محسن
    $batContent = @"
@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    نظام إدارة الأجهزة الطبية المتكامل
echo    Medical Devices Management System
echo ========================================
echo.
echo 🚀 تشغيل النظام مع التحسينات العربية...
echo Starting system with Arabic improvements...
echo.

cd /d "$currentDir"

echo ✅ جاري بناء المشروع...
echo Building project...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo Build failed
    pause
    exit /b 1
)

echo ✅ تم البناء بنجاح - النظام جاهز للتشغيل
echo Build successful - System ready to run
echo.

start "" dotnet run --configuration Release

echo ✅ تم تشغيل النظام بنجاح!
echo System started successfully!
echo.
echo 💡 النظام يعمل الآن في الخلفية
echo System is now running in background
echo.
echo 🎯 المزايا المتاحة:
echo Available features:
echo   • إدارة الأجهزة الطبية
echo   • نظام المبيعات والفواتير
echo   • إدارة المخزون والقطع
echo   • نظام الصيانة والتركيب
echo   • النسخ الاحتياطي التلقائي
echo   • التقارير والإحصائيات
echo.
timeout /t 3 /nobreak >nul
"@

    # كتابة ملف batch
    $batContent | Out-File -FilePath $batFilePath -Encoding UTF8
    
    Write-Host "✅ تم إنشاء ملف التشغيل بنجاح!" -ForegroundColor Green
    Write-Host "✅ Batch file created successfully!" -ForegroundColor Green
    Write-Host ""
    
    # إنشاء اختصار Windows
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($shortcutPath)
    $Shortcut.TargetPath = $batFilePath
    $Shortcut.WorkingDirectory = $currentDir
    $Shortcut.Description = "نظام إدارة الأجهزة الطبية المتكامل - Medical Devices Management System"
    $Shortcut.IconLocation = "shell32.dll,21"  # أيقونة كمبيوتر
    $Shortcut.Save()
    
    Write-Host "✅ تم إنشاء الاختصار بنجاح!" -ForegroundColor Green
    Write-Host "✅ Shortcut created successfully!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📍 موقع الملفات:" -ForegroundColor Cyan
    Write-Host "📍 File locations:" -ForegroundColor Cyan
    Write-Host "   • الاختصار: $shortcutPath" -ForegroundColor White
    Write-Host "   • ملف التشغيل: $batFilePath" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🎯 طرق تشغيل النظام:" -ForegroundColor Yellow
    Write-Host "🎯 Ways to run the system:" -ForegroundColor Yellow
    Write-Host "   1. النقر مرتين على الاختصار في سطح المكتب" -ForegroundColor White
    Write-Host "      Double-click the desktop shortcut" -ForegroundColor Gray
    Write-Host "   2. النقر مرتين على ملف Medical-Devices-System.bat" -ForegroundColor White
    Write-Host "      Double-click the Medical-Devices-System.bat file" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "✨ المزايا الجديدة:" -ForegroundColor Magenta
    Write-Host "✨ New features:" -ForegroundColor Magenta
    Write-Host "   • واجهة عربية كاملة مع اتجاه RTL" -ForegroundColor White
    Write-Host "   • نظام النسخ الاحتياطي التلقائي المحسن" -ForegroundColor White
    Write-Host "   • إدارة متقدمة للمخزون والقطع" -ForegroundColor White
    Write-Host "   • نظام التقارير والإحصائيات الشامل" -ForegroundColor White
    Write-Host "   • واجهات محسنة لجميع العمليات" -ForegroundColor White
    Write-Host ""
    
    # اختبار الاختصار
    if (Test-Path $shortcutPath) {
        Write-Host "🧪 اختبار الاختصار..." -ForegroundColor Yellow
        Write-Host "🧪 Testing shortcut..." -ForegroundColor Yellow
        
        if (Test-Path $batFilePath) {
            Write-Host "✅ جميع الملفات جاهزة للاستخدام!" -ForegroundColor Green
            Write-Host "✅ All files ready for use!" -ForegroundColor Green
        } else {
            Write-Host "⚠️ تحذير: ملف التشغيل غير موجود" -ForegroundColor Yellow
            Write-Host "⚠️ Warning: Batch file not found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ تحذير: الاختصار غير موجود" -ForegroundColor Yellow
        Write-Host "⚠️ Warning: Shortcut not found" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ خطأ في إنشاء الاختصار:" -ForegroundColor Red
    Write-Host "❌ Error creating shortcut:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎉 تم الانتهاء من إنشاء الاختصار!" -ForegroundColor Green
Write-Host "🎉 Shortcut creation completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
