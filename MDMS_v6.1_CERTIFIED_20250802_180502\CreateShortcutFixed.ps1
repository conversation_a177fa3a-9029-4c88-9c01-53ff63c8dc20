# Create Medical Devices Management System Desktop Shortcut

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Medical Devices Management System" -ForegroundColor Yellow
Write-Host "   Creating Desktop Shortcut" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Creating desktop shortcut..." -ForegroundColor Green
Write-Host ""

try {
    # Get paths
    $currentDir = (Get-Location).Path
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = Join-Path $desktopPath "Medical Devices System.lnk"
    $batFilePath = Join-Path $desktopPath "Medical-Devices-System.bat"
    
    # Create enhanced batch file
    $batContent = @"
@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    Medical Devices Management System
echo    نظام إدارة الأجهزة الطبية المتكامل
echo ========================================
echo.
echo 🚀 Starting system with Arabic improvements...
echo بدء تشغيل النظام مع التحسينات العربية...
echo.

cd /d "$currentDir"

echo ✅ Building project...
echo جاري بناء المشروع...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed
    echo فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ Build successful - System ready to run
echo تم البناء بنجاح - النظام جاهز للتشغيل
echo.

start "" dotnet run --configuration Release

echo ✅ System started successfully!
echo تم تشغيل النظام بنجاح!
echo.
echo 💡 System is now running in background
echo النظام يعمل الآن في الخلفية
echo.
echo 🎯 Available features / المزايا المتاحة:
echo   • Medical Devices Management / إدارة الأجهزة الطبية
echo   • Sales and Invoicing System / نظام المبيعات والفواتير
echo   • Inventory and Parts Management / إدارة المخزون والقطع
echo   • Maintenance and Installation / نظام الصيانة والتركيب
echo   • Automatic Backup System / نظام النسخ الاحتياطي التلقائي
echo   • Reports and Statistics / التقارير والإحصائيات
echo.
echo ✨ New improvements / التحسينات الجديدة:
echo   • Complete Arabic RTL interface / واجهة عربية كاملة
echo   • Enhanced backup system / نظام نسخ احتياطي محسن
echo   • Advanced search and filters / بحث وفلاتر متقدمة
echo   • Improved data management / إدارة بيانات محسنة
echo.
timeout /t 5 /nobreak >nul
"@

    # Write batch file
    $batContent | Out-File -FilePath $batFilePath -Encoding UTF8
    
    Write-Host "✅ Batch file created successfully!" -ForegroundColor Green
    Write-Host "   Location: $batFilePath" -ForegroundColor Gray
    Write-Host ""
    
    # Create Windows shortcut
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($shortcutPath)
    $Shortcut.TargetPath = $batFilePath
    $Shortcut.WorkingDirectory = $currentDir
    $Shortcut.Description = "Medical Devices Management System"
    $Shortcut.IconLocation = "shell32.dll,21"
    $Shortcut.Save()
    
    Write-Host "✅ Shortcut created successfully!" -ForegroundColor Green
    Write-Host "   Location: $shortcutPath" -ForegroundColor Gray
    Write-Host ""
    
    # Test files
    if (Test-Path $shortcutPath) {
        Write-Host "✅ Shortcut file exists" -ForegroundColor Green
    } else {
        Write-Host "❌ Shortcut file not found" -ForegroundColor Red
    }
    
    if (Test-Path $batFilePath) {
        Write-Host "✅ Batch file exists" -ForegroundColor Green
    } else {
        Write-Host "❌ Batch file not found" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "🎯 How to use:" -ForegroundColor Yellow
    Write-Host "   1. Double-click 'Medical Devices System' shortcut on desktop" -ForegroundColor White
    Write-Host "   2. Or double-click 'Medical-Devices-System.bat' file on desktop" -ForegroundColor White
    Write-Host ""
    
    Write-Host "📋 System features:" -ForegroundColor Magenta
    Write-Host "   • Complete Arabic interface with RTL support" -ForegroundColor White
    Write-Host "   • Enhanced automatic backup system" -ForegroundColor White
    Write-Host "   • Advanced inventory and parts management" -ForegroundColor White
    Write-Host "   • Comprehensive reports and statistics" -ForegroundColor White
    Write-Host "   • Improved user interfaces for all operations" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "❌ Error creating shortcut:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎉 Shortcut creation completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
