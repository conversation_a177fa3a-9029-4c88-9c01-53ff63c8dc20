# Create Medical Devices Management System Desktop Shortcut

Write-Host "Creating desktop shortcut for Medical Devices Management System..." -ForegroundColor Green

try {
    # Get paths
    $currentDir = (Get-Location).Path
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = Join-Path $desktopPath "Medical Devices System.lnk"
    $batFilePath = Join-Path $desktopPath "Medical-Devices-System.bat"
    
    # Create batch file
    $batContent = @"
@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    Medical Devices Management System
echo ========================================
echo.
echo Starting system...
echo.

cd /d "$currentDir"

echo Building project...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo Build failed
    pause
    exit /b 1
)

echo Build successful - Starting system...
echo.

start "" dotnet run --configuration Release

echo System started successfully!
echo System is now running in background
echo.
echo Available features:
echo   • Medical Devices Management
echo   • Sales and Invoicing System
echo   • Inventory and Parts Management
echo   • Maintenance and Installation System
echo   • Automatic Backup System
echo   • Reports and Statistics
echo.
timeout /t 3 /nobreak >nul
"@

    # Write batch file
    $batContent | Out-File -FilePath $batFilePath -Encoding UTF8
    Write-Host "Batch file created: $batFilePath" -ForegroundColor Green
    
    # Create Windows shortcut
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($shortcutPath)
    $Shortcut.TargetPath = $batFilePath
    $Shortcut.WorkingDirectory = $currentDir
    $Shortcut.Description = "Medical Devices Management System"
    $Shortcut.IconLocation = "shell32.dll,21"
    $Shortcut.Save()
    
    Write-Host "Shortcut created: $shortcutPath" -ForegroundColor Green
    
    # Test files
    if (Test-Path $shortcutPath) {
        Write-Host "SUCCESS: Shortcut file exists" -ForegroundColor Green
    }
    
    if (Test-Path $batFilePath) {
        Write-Host "SUCCESS: Batch file exists" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "How to use:" -ForegroundColor Yellow
    Write-Host "1. Double-click 'Medical Devices System' shortcut on desktop" -ForegroundColor White
    Write-Host "2. Or double-click 'Medical-Devices-System.bat' file on desktop" -ForegroundColor White
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Shortcut creation completed!" -ForegroundColor Green
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
