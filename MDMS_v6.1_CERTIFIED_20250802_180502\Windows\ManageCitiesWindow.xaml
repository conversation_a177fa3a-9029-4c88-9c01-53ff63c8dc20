<Window x:Class="MedicalDevicesManager.Windows.ManageCitiesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المدن" Height="600" Width="800"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        FontSize="14">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="🏙️ إدارة المدن" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- أزرار الإجراءات والفلترة -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" x:Name="AddCityBtn" Content="➕ إضافة مدينة جديدة" Width="150" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="AddCityBtn_Click"/>
            
            <Button Grid.Column="1" x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="35" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,20,0" Click="RefreshBtn_Click"/>
            
            <TextBlock Grid.Column="3" Text="فلترة حسب البلد:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="4" x:Name="CountryFilterComboBox" Width="150" Height="35" 
                      SelectionChanged="CountryFilterComboBox_SelectionChanged"/>
        </Grid>
        
        <!-- جدول المدن -->
        <DataGrid Grid.Row="2" x:Name="CitiesDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="اسم المدينة" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="البلد" Binding="{Binding Country}" Width="150"/>
                <DataGridCheckBoxColumn Header="نشطة" Binding="{Binding IsActive}" Width="80"/>
                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="✏️" Width="30" Height="25" 
                                        Background="#FFC107" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="تعديل"
                                        Click="EditCityBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteCityBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- نموذج إضافة/تعديل المدينة -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0">
            
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="اسم المدينة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1" x:Name="CityNameTextBox" Height="30" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="2" Text="البلد:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="3" x:Name="CountryComboBox" Height="30" Margin="0,0,20,0" IsEditable="True">
                        <ComboBoxItem Content="العراق"/>
                        <ComboBoxItem Content="السعودية"/>
                        <ComboBoxItem Content="الكويت"/>
                        <ComboBoxItem Content="قطر"/>
                        <ComboBoxItem Content="البحرين"/>
                        <ComboBoxItem Content="الإمارات"/>
                        <ComboBoxItem Content="عمان"/>
                        <ComboBoxItem Content="الأردن"/>
                        <ComboBoxItem Content="سوريا"/>
                        <ComboBoxItem Content="لبنان"/>
                        <ComboBoxItem Content="مصر"/>
                        <ComboBoxItem Content="ليبيا"/>
                        <ComboBoxItem Content="تونس"/>
                        <ComboBoxItem Content="الجزائر"/>
                        <ComboBoxItem Content="المغرب"/>
                        <ComboBoxItem Content="اليمن"/>
                    </ComboBox>
                    
                    <StackPanel Grid.Column="4" Orientation="Horizontal">
                        <Button x:Name="SaveCityBtn" Content="💾 حفظ" Width="80" Height="30" 
                                Background="#28A745" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="SaveCityBtn_Click"/>
                        <Button x:Name="CancelBtn" Content="❌ إلغاء" Width="80" Height="30" 
                                Background="#DC3545" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Click="CancelBtn_Click"/>
                    </StackPanel>
                </Grid>
                
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <CheckBox Grid.Column="0" x:Name="IsActiveCheckBox" Content="نشطة" 
                              VerticalAlignment="Center" Margin="0,0,20,0" IsChecked="True"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</Window>
