# PowerShell script to create certified version backup

Write-Host "Creating Certified Version Backup..." -ForegroundColor Green

# Get current date and time
$date = Get-Date -Format "yyyyMMdd"
$time = Get-Date -Format "HHmmss"
$backupName = "MDMS_v6.1_CERTIFIED_$date" + "_$time"
$backupDir = "..\CERTIFIED_VERSIONS\$backupName"

Write-Host "Backup Name: $backupName" -ForegroundColor Yellow
Write-Host "Backup Directory: $backupDir" -ForegroundColor Yellow

# Create backup directory
if (!(Test-Path "..\CERTIFIED_VERSIONS")) {
    New-Item -ItemType Directory -Path "..\CERTIFIED_VERSIONS" -Force | Out-Null
}

if (!(Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
}

Write-Host "Backup directory created successfully" -ForegroundColor Green

# Copy main files
Write-Host "Copying main files..." -ForegroundColor Cyan
<PERSON>-Item "*.csproj" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.sln" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.cs" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.xaml" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.xaml.cs" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.config" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.json" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.bat" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.md" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.ps1" $backupDir -ErrorAction SilentlyContinue

# Copy directories
Write-Host "Copying directories..." -ForegroundColor Cyan

if (Test-Path "Windows") {
    Copy-Item "Windows" "$backupDir\Windows" -Recurse -Force
    Write-Host "  Windows folder copied" -ForegroundColor Green
}

if (Test-Path "Controls") {
    Copy-Item "Controls" "$backupDir\Controls" -Recurse -Force
    Write-Host "  Controls folder copied" -ForegroundColor Green
}

if (Test-Path "Services") {
    Copy-Item "Services" "$backupDir\Services" -Recurse -Force
    Write-Host "  Services folder copied" -ForegroundColor Green
}

if (Test-Path "Resources") {
    Copy-Item "Resources" "$backupDir\Resources" -Recurse -Force
    Write-Host "  Resources folder copied" -ForegroundColor Green
}

if (Test-Path "bin") {
    Copy-Item "bin" "$backupDir\bin" -Recurse -Force
    Write-Host "  bin folder copied" -ForegroundColor Green
}

# Copy database files
Write-Host "Copying database files..." -ForegroundColor Cyan
Copy-Item "*.db" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.db-shm" $backupDir -ErrorAction SilentlyContinue
Copy-Item "*.db-wal" $backupDir -ErrorAction SilentlyContinue
Write-Host "  Database files copied" -ForegroundColor Green

# Create version info file
Write-Host "Creating version info file..." -ForegroundColor Cyan
$versionInfo = "# Certified Version Information`n`n"
$versionInfo += "Version Name: Medical Devices Management System v6.1 - Arabic Enhanced Edition`n"
$versionInfo += "Backup Date: $(Get-Date -Format 'yyyy-MM-dd')`n"
$versionInfo += "Backup Time: $(Get-Date -Format 'HH:mm:ss')`n"
$versionInfo += "Build Number: 6.1.2025.0802`n"
$versionInfo += "Status: Certified for Production`n`n"
$versionInfo += "## Backup Contents:`n"
$versionInfo += "- All source files`n"
$versionInfo += "- Arabic-enhanced windows`n"
$versionInfo += "- Arabic styles library`n"
$versionInfo += "- Database with sample data`n"
$versionInfo += "- Runtime and shortcut files`n"
$versionInfo += "- Complete documentation`n`n"
$versionInfo += "## Features:`n"
$versionInfo += "- Professional Arabic interface`n"
$versionInfo += "- 12 integrated modules`n"
$versionInfo += "- 17 database tables`n"
$versionInfo += "- Advanced management tools`n`n"
$versionInfo += "This is a certified version ready for production"

$versionInfo | Out-File "$backupDir\VERSION_INFO.md" -Encoding UTF8

Write-Host "Version info file created" -ForegroundColor Green

# Verify backup
Write-Host "Verifying backup..." -ForegroundColor Cyan
$projectFile = Test-Path "$backupDir\MedicalDevicesManager.csproj"
$mainWindow = Test-Path "$backupDir\MainWindow.xaml"
$arabicStyles = Test-Path "$backupDir\Resources\ArabicStyles.xaml"

if ($projectFile) { Write-Host "  Project file exists" -ForegroundColor Green }
else { Write-Host "  Project file missing" -ForegroundColor Red }

if ($mainWindow) { Write-Host "  Main window exists" -ForegroundColor Green }
else { Write-Host "  Main window missing" -ForegroundColor Red }

if ($arabicStyles) { Write-Host "  Arabic styles file exists" -ForegroundColor Green }
else { Write-Host "  Arabic styles file missing" -ForegroundColor Red }

# Get statistics
$fileCount = (Get-ChildItem $backupDir -Recurse -File | Measure-Object).Count
$folderCount = (Get-ChildItem $backupDir -Recurse -Directory | Measure-Object).Count

Write-Host ""
Write-Host "Backup Statistics:" -ForegroundColor Cyan
Write-Host "  Files: $fileCount" -ForegroundColor Yellow
Write-Host "  Folders: $folderCount" -ForegroundColor Yellow
Write-Host ""
Write-Host "Certified version backup created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Backup location: $backupDir" -ForegroundColor Yellow
