using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageRecipientsWindow : Window
    {
        private RecipientName _editingRecipient = null;
        
        public ManageRecipientsWindow()
        {
            InitializeComponent();
            LoadRecipientsAsync();
        }
        
        private async void LoadRecipientsAsync()
        {
            try
            {
                var recipients = await App.DatabaseContext.RecipientNames
                    .OrderBy(r => r.Name)
                    .ToListAsync();
                    
                RecipientsDataGrid.ItemsSource = recipients;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أسماء المستلمين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddRecipientBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _editingRecipient = null;
            RecipientNameTextBox.Focus();
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadRecipientsAsync();
            ClearForm();
        }
        
        private void EditRecipientBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var recipient = button?.DataContext as RecipientName;
            
            if (recipient != null)
            {
                _editingRecipient = recipient;
                LoadRecipientToForm(recipient);
            }
        }
        
        private async void DeleteRecipientBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var recipient = button?.DataContext as RecipientName;
            
            if (recipient != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المستلم '{recipient.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.RecipientNames.Remove(recipient);
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف المستلم بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadRecipientsAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المستلم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private async void SaveRecipientBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
                
            try
            {
                if (_editingRecipient == null)
                {
                    // إضافة مستلم جديد
                    var newRecipient = new RecipientName
                    {
                        Name = RecipientNameTextBox.Text.Trim(),
                        Phone = PhoneTextBox.Text.Trim(),
                        Address = AddressTextBox.Text.Trim(),
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.RecipientNames.Add(newRecipient);
                }
                else
                {
                    // تعديل مستلم موجود
                    _editingRecipient.Name = RecipientNameTextBox.Text.Trim();
                    _editingRecipient.Phone = PhoneTextBox.Text.Trim();
                    _editingRecipient.Address = AddressTextBox.Text.Trim();
                    _editingRecipient.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    _editingRecipient.LastUpdated = DateTime.Now;
                    
                    App.DatabaseContext.RecipientNames.Update(_editingRecipient);
                }
                
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _editingRecipient == null ? "تم إضافة المستلم بنجاح!" : "تم تحديث المستلم بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                LoadRecipientsAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المستلم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void LoadRecipientToForm(RecipientName recipient)
        {
            RecipientNameTextBox.Text = recipient.Name;
            PhoneTextBox.Text = recipient.Phone;
            AddressTextBox.Text = recipient.Address;
            IsActiveCheckBox.IsChecked = recipient.IsActive;
        }
        
        private void ClearForm()
        {
            _editingRecipient = null;
            RecipientNameTextBox.Text = "";
            PhoneTextBox.Text = "";
            AddressTextBox.Text = "";
            IsActiveCheckBox.IsChecked = true;
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(RecipientNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستلم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                RecipientNameTextBox.Focus();
                return false;
            }
            
            return true;
        }
    }
}
