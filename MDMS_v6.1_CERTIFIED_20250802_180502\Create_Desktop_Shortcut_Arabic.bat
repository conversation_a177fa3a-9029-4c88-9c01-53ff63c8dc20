@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    Desktop Shortcut Creator
echo    Medical Devices Management System v2.1
echo    Quality Certificate Edition
echo ========================================
echo.

REM Set paths
set "EXE_PATH=%~dp0Distribution\MedicalDevicesManager_v2.1_QualityCertificate\MedicalDevicesManager.exe"
set "WORK_DIR=%~dp0Distribution\MedicalDevicesManager_v2.1_QualityCertificate"

REM Check if executable exists
if not exist "%EXE_PATH%" (
    echo [ERROR] Executable file not found!
    echo Expected path: %EXE_PATH%
    echo.
    echo Please make sure v2.1 exists in Distribution folder
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Found v2.1 version
echo Path: %EXE_PATH%
echo.

REM Get desktop path using PowerShell (more reliable)
for /f "usebackq delims=" %%i in (`powershell -command "[Environment]::GetFolderPath('Desktop')"`) do set "DESKTOP_PATH=%%i"

if "%DESKTOP_PATH%"=="" (
    echo [ERROR] Could not determine desktop path
    echo.
    pause
    exit /b 1
)

echo Desktop path: %DESKTOP_PATH%
echo.

REM Create PowerShell script with proper encoding
echo Creating desktop shortcut...

(
echo # PowerShell script to create shortcut
echo $WshShell = New-Object -ComObject WScript.Shell
echo $ShortcutPath = Join-Path "%DESKTOP_PATH%" "Medical Devices Manager v2.1.lnk"
echo $Shortcut = $WshShell.CreateShortcut^($ShortcutPath^)
echo $Shortcut.TargetPath = "%EXE_PATH%"
echo $Shortcut.WorkingDirectory = "%WORK_DIR%"
echo $Shortcut.Description = "Medical Devices Management System v2.1 - Quality Certificate Edition"
echo $Shortcut.IconLocation = "%EXE_PATH%,0"
echo $Shortcut.Save^(^)
echo Write-Host "Shortcut created successfully at: $ShortcutPath"
) > "%TEMP%\create_shortcut.ps1"

REM Execute PowerShell script
powershell -ExecutionPolicy Bypass -File "%TEMP%\create_shortcut.ps1"
set PS_RESULT=%ERRORLEVEL%

REM Clean up temporary file
del "%TEMP%\create_shortcut.ps1" >nul 2>&1

REM Check result
if %PS_RESULT% EQU 0 (
    echo.
    echo [SUCCESS] Desktop shortcut created successfully!
    echo.
    echo Shortcut name: Medical Devices Manager v2.1
    echo Location: Desktop
    echo.
    echo Features of this version:
    echo - Standalone executable file
    echo - No .NET installation required
    echo - Updated integrated database
    echo - Enhanced Arabic interface
    echo - Quality certificate approved
    echo.
    echo You can now:
    echo 1. Double-click the shortcut to run the system
    echo 2. Drag shortcut to taskbar for quick access
    echo 3. Copy shortcut to other locations
    echo.
) else (
    echo.
    echo [ERROR] Failed to create shortcut automatically
    echo.
    echo Manual creation steps:
    echo 1. Right-click on desktop
    echo 2. Choose "New" then "Shortcut"
    echo 3. Enter this path:
    echo    "%EXE_PATH%"
    echo 4. Click "Next"
    echo 5. Name: Medical Devices Manager v2.1
    echo 6. Click "Finish"
    echo.
    echo Then right-click shortcut, choose Properties:
    echo - Start in: %WORK_DIR%
    echo - Comment: Medical Devices Management System v2.1
    echo.
)

echo ========================================
echo Press any key to exit...
pause >nul
