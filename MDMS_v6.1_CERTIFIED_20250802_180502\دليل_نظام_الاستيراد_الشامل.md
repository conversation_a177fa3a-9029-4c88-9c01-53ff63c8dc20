# 📥 دليل نظام استيراد قوائم الأجهزة والملحقات

## 🎯 نظرة عامة

تم تطوير نظام استيراد شامل ومتقدم يدعم استيراد قوائم الأجهزة والملحقات من ملفات بصيغ متعددة وإدراجها تلقائياً في المخزون مع التحقق من صحة البيانات.

### 🚀 **المزايا الرئيسية:**
- **📄 دعم صيغ متعددة:** Excel، PDF، Word
- **🔍 تحليل ذكي للبيانات** مع استخراج المعلومات تلقائياً
- **✅ التحقق من صحة البيانات** قبل الاستيراد
- **📊 معاينة شاملة** للبيانات المستخرجة
- **📝 سجل مفصل** لعملية الاستيراد
- **⚙️ خيارات مرنة** للاستيراد والتحديث

## 🚀 كيفية الوصول للنظام

### الطريقة الصحيحة:
1. **شغل النظام الرئيسي**
2. **اضغط على "📦 إدارة المخزون"** في القائمة الجانبية
3. **اضغط على "📥 استيراد قوائم الأجهزة"** في شريط الأدوات
4. **ستفتح نافذة الاستيراد الشاملة**

### 💡 **لماذا في إدارة المخزون؟**
- **📦 المكان المنطقي:** الاستيراد يضيف عناصر للمخزون
- **🔄 التكامل المباشر:** مع وظائف إدارة المخزون الأخرى
- **📊 المراقبة الفورية:** رؤية النتائج مباشرة في جداول المخزون
- **⚙️ سهولة الإدارة:** جميع وظائف المخزون في مكان واحد

## 📄 الصيغ المدعومة

### 📊 **ملفات Excel (.xlsx, .xls):**
- **الأعمدة المطلوبة:**
  - العمود A: اسم العنصر (مطلوب)
  - العمود B: الفئة (مطلوب)
  - العمود C: الوصف
  - العمود D: السعر (رقم)
  - العمود E: الكمية (رقم)
  - العمود F: الوحدة
  - العمود G: الماركة
  - العمود H: الموديل

### 📄 **ملفات PDF (.pdf):**
- **المتطلبات:**
  - جداول منظمة مع رؤوس أعمدة واضحة
  - نص قابل للاستخراج (ليس صورة)
  - تنسيق ثابت للبيانات
  - فصل البيانات بمسافات أو خطوط

### 📝 **ملفات Word (.docx, .doc):**
- **التنسيقات المدعومة:**
  - جداول Word مع رؤوس أعمدة
  - قوائم منظمة مع فواصل
  - نص منسق مع أسطر منفصلة
  - ملفات .docx و .doc

## 🛠️ كيفية الاستخدام

### 1️⃣ **اختيار الملف:**
- اضغط على **"📂 استعراض"**
- اختر الملف المطلوب (Excel/PDF/Word)
- ستظهر معلومات الملف (النوع، الحجم)

### 2️⃣ **معاينة البيانات:**
- اضغط على **"🔍 معاينة"**
- سيتم تحليل الملف واستخراج البيانات
- ستظهر النتائج في جدول المعاينة

### 3️⃣ **مراجعة البيانات:**
- راجع البيانات في تبويب **"📋 معاينة البيانات"**
- تحقق من الإحصائيات:
  - إجمالي الصفوف
  - الصفوف الصحيحة
  - الصفوف الخاطئة
  - التحذيرات

### 4️⃣ **تكوين الاستيراد:**
- **استبدال العناصر الموجودة:** لتحديث البيانات الحالية
- **التحقق من صحة البيانات:** للتأكد من صحة البيانات
- **نوع الاستيراد:**
  - أجهزة طبية
  - مخزون
  - تلقائي (كلاهما)

### 5️⃣ **بدء الاستيراد:**
- اضغط على **"📥 بدء الاستيراد"**
- راقب التقدم في شريط التقدم
- راجع السجل في تبويب **"📝 سجل الاستيراد"**

## 📊 واجهة النظام

### 🔍 **تبويب معاينة البيانات:**
- **معلومات الملف:** نوع الملف، الحجم، عدد الصفوف
- **إحصائيات التحليل:** الصفوف الصحيحة، الخاطئة، التحذيرات
- **جدول المعاينة:** عرض البيانات المستخرجة مع الحالة
- **تقدير الوقت:** الوقت المتوقع للاستيراد

### 📝 **تبويب سجل الاستيراد:**
- **سجل مفصل** لجميع العمليات
- **رسائل الأخطاء** والتحذيرات
- **إمكانية حفظ السجل** كملف نصي
- **تمرير تلقائي** لمتابعة العمليات

### ❓ **تبويب المساعدة:**
- **إرشادات مفصلة** لكل صيغة ملف
- **أمثلة على التنسيقات** المطلوبة
- **نصائح مهمة** للاستيراد الناجح

## 🔧 خيارات الاستيراد

### ⚙️ **الخيارات المتاحة:**

#### **استبدال العناصر الموجودة:**
- ✅ **مفعل:** تحديث البيانات الموجودة
- ❌ **معطل:** تخطي العناصر الموجودة

#### **التحقق من صحة البيانات:**
- ✅ **مفعل (موصى):** فحص البيانات قبل الاستيراد
- ❌ **معطل:** استيراد مباشر بدون فحص

#### **نوع الاستيراد:**
- **أجهزة طبية:** إدراج في جدول الأجهزة الطبية
- **مخزون:** إدراج في جدول المخزون
- **تلقائي:** إدراج في كلا الجدولين

## 📈 عملية التحليل الذكي

### 🧠 **للملفات Excel:**
- قراءة الأعمدة حسب الترتيب المحدد
- تحويل الأرقام تلقائياً
- التعامل مع الخلايا الفارغة
- تخطي رأس الجدول تلقائياً

### 🧠 **للملفات PDF:**
- استخراج النص من جميع الصفحات
- تحليل الجداول والقوائم
- البحث عن الأسعار والكميات بالتعبيرات النمطية
- فصل البيانات بالفواصل والمسافات

### 🧠 **للملفات Word:**
- استخراج الجداول أولاً
- تحليل النص المنسق
- البحث عن الأنماط المعروفة
- استخراج المعلومات من السياق

## ✅ التحقق من صحة البيانات

### 🔍 **القواعد المطبقة:**
- **الاسم:** مطلوب وغير فارغ
- **الفئة:** مطلوبة وغير فارغة
- **السعر:** رقم أكبر من صفر
- **الكمية:** رقم صحيح (افتراضي: 1)
- **الوحدة:** نص (افتراضي: "قطعة")

### 📊 **حالات البيانات:**
- **✅ صحيح:** جاهز للاستيراد
- **⚠️ تحذير:** يحتاج مراجعة
- **❌ خطأ:** لا يمكن استيراده

## 🎯 أمثلة عملية

### مثال 1: استيراد من Excel
```
العمود A: جهاز تخطيط القلب 12 قناة
العمود B: أجهزة القلب
العمود C: جهاز متقدم لتخطيط القلب
العمود D: 125000
العمود E: 2
العمود F: قطعة
العمود G: Philips
العمود H: PageWriter TC70
```

### مثال 2: استيراد من PDF
```
جدول في PDF:
الاسم | الفئة | السعر | الكمية
جهاز الأشعة السينية | أجهزة الأشعة | 850000 ريال | 1
جهاز الموجات فوق الصوتية | أجهزة الأشعة | 450000 ريال | 2
```

### مثال 3: استيراد من Word
```
قائمة في Word:
1. جهاز مراقبة المريض - أجهزة المراقبة - 75000 ريال
2. جهاز التنفس الصناعي - أجهزة التنفس - 180000 ريال
3. سرير طبي كهربائي - أثاث طبي - 25000 ريال
```

## 📝 السجل والتتبع

### 📊 **معلومات السجل:**
- **الوقت:** طابع زمني لكل عملية
- **المستوى:** INFO, WARNING, ERROR
- **الرسالة:** تفاصيل العملية
- **النتيجة:** نجاح أو فشل

### 💾 **حفظ السجل:**
- إمكانية حفظ السجل كملف نصي
- تسمية تلقائية بالتاريخ والوقت
- سهولة المراجعة والتحليل

## 🚨 نصائح مهمة

### ✅ **للحصول على أفضل النتائج:**
- **تأكد من صحة البيانات** قبل الاستيراد
- **استخدم ملفات صغيرة** للاختبار أولاً
- **راجع المعاينة** بعناية قبل الاستيراد
- **احفظ نسخة احتياطية** من قاعدة البيانات
- **استخدم التحقق من صحة البيانات** دائماً

### ⚠️ **تجنب:**
- الملفات المحمية بكلمة مرور
- الملفات التالفة أو المعطوبة
- البيانات غير المنظمة
- الاستيراد بدون مراجعة

## 🎉 النتائج المتوقعة

### ✅ **بعد الاستيراد الناجح:**
- **إضافة العناصر الجديدة** إلى المخزون
- **تحديث العناصر الموجودة** (إذا تم تفعيل الخيار)
- **تسجيل جميع العمليات** في السجل
- **إحصائيات مفصلة** عن النتائج

### 📊 **التحديثات في النظام:**
- **قاعدة البيانات محدثة** بالبيانات الجديدة
- **المخزون محدث** بالكميات والأسعار
- **الأجهزة الطبية مضافة** مع التفاصيل الكاملة
- **السجلات موثقة** لأغراض المراجعة

**النظام جاهز لاستيراد قوائم الأجهزة بكفاءة عالية! 🚀**
