﻿#pragma checksum "..\..\..\..\Windows\DeviceDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0CB9A1F441E884A3DEF7913EE55BF7DEF003417B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// DeviceDetailsWindow
    /// </summary>
    public partial class DeviceDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesCountSummary;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceCountSummary;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstallationsCountSummary;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilesCountSummary;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BrandTextBlock;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelTextBlock;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryTextBlock;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescriptionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PurchasePriceTextBlock;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SellingPriceTextBlock;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierTextBlock;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocationTextBlock;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PurchaseDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarrantyStartTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarrantyEndTextBlock;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SpecificationsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshSerialNumbersBtn;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SerialNumbersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumbersCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserManualTextBlock;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenUserManualBtn;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceManualTextBlock;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMaintenanceManualBtn;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OriginCertificateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOriginCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QualityCertificateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenQualityCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficialCertificationsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOfficialCertificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TechnicalInfoBookletTextBlock;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenTechnicalInfoBookletBtn;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImportPapersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenImportPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractPapersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 427 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenContractPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SalesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 483 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid MaintenanceDataGrid;
        
        #line default
        #line hidden
        
        
        #line 499 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 516 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InstallationsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 534 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstallationsCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserManualFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 576 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenUserManualFileBtn;
        
        #line default
        #line hidden
        
        
        #line 582 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceManualFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 584 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMaintenanceManualFileBtn;
        
        #line default
        #line hidden
        
        
        #line 590 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OriginCertificateFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 592 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOriginCertificateFileBtn;
        
        #line default
        #line hidden
        
        
        #line 598 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QualityCertificateFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 600 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenQualityCertificateFileBtn;
        
        #line default
        #line hidden
        
        
        #line 606 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficialCertificationsFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 608 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOfficialCertificationsFileBtn;
        
        #line default
        #line hidden
        
        
        #line 614 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TechnicalInfoBookletFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 616 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenTechnicalInfoBookletFileBtn;
        
        #line default
        #line hidden
        
        
        #line 622 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImportPapersFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 624 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenImportPapersFileBtn;
        
        #line default
        #line hidden
        
        
        #line 630 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractPapersFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 632 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenContractPapersFileBtn;
        
        #line default
        #line hidden
        
        
        #line 640 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportBookletsPanel;
        
        #line default
        #line hidden
        
        
        #line 648 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AdditionalFilesPanel;
        
        #line default
        #line hidden
        
        
        #line 657 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 673 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditDeviceBtn;
        
        #line default
        #line hidden
        
        
        #line 677 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintDetailsBtn;
        
        #line default
        #line hidden
        
        
        #line 681 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/devicedetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DeviceNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SerialNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.SalesCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.MaintenanceCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.InstallationsCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.FilesCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.BrandTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ModelTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CategoryTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.DescriptionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PurchasePriceTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SellingPriceTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.SupplierTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.LocationTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.PurchaseDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.CreatedDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.WarrantyStartTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.WarrantyEndTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.NotesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.SpecificationsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.RefreshSerialNumbersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.RefreshSerialNumbersBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshSerialNumbersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.SerialNumbersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 24:
            this.SerialNumbersCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.UserManualTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.OpenUserManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 310 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenUserManualBtn.Click += new System.Windows.RoutedEventHandler(this.OpenUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.MaintenanceManualTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.OpenMaintenanceManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 327 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenMaintenanceManualBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.OriginCertificateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.OpenOriginCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 344 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOriginCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.QualityCertificateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.OpenQualityCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 361 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenQualityCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.OpenQualityCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.OfficialCertificationsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.OpenOfficialCertificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 378 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOfficialCertificationsBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.TechnicalInfoBookletTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.OpenTechnicalInfoBookletBtn = ((System.Windows.Controls.Button)(target));
            
            #line 395 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenTechnicalInfoBookletBtn.Click += new System.Windows.RoutedEventHandler(this.OpenTechnicalInfoBookletBtn_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.ImportPapersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.OpenImportPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 412 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenImportPapersBtn.Click += new System.Windows.RoutedEventHandler(this.OpenImportPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.ContractPapersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.OpenContractPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 429 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenContractPapersBtn.Click += new System.Windows.RoutedEventHandler(this.OpenContractPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.SalesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 42:
            this.SalesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.MaintenanceDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 44:
            this.MaintenanceCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            this.InstallationsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 46:
            this.InstallationsCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 47:
            this.UserManualFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.OpenUserManualFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 577 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenUserManualFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 49:
            this.MaintenanceManualFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.OpenMaintenanceManualFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 585 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenMaintenanceManualFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.OriginCertificateFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.OpenOriginCertificateFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 593 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOriginCertificateFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            this.QualityCertificateFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.OpenQualityCertificateFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 601 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenQualityCertificateFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenQualityCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.OfficialCertificationsFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 56:
            this.OpenOfficialCertificationsFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 609 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOfficialCertificationsFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.TechnicalInfoBookletFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.OpenTechnicalInfoBookletFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 617 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenTechnicalInfoBookletFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenTechnicalInfoBookletBtn_Click);
            
            #line default
            #line hidden
            return;
            case 59:
            this.ImportPapersFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 60:
            this.OpenImportPapersFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 625 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenImportPapersFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenImportPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.ContractPapersFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 62:
            this.OpenContractPapersFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 633 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenContractPapersFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenContractPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 63:
            this.ReportBookletsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 64:
            this.AdditionalFilesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 65:
            this.FilesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 66:
            this.EditDeviceBtn = ((System.Windows.Controls.Button)(target));
            
            #line 675 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.EditDeviceBtn.Click += new System.Windows.RoutedEventHandler(this.EditDeviceBtn_Click);
            
            #line default
            #line hidden
            return;
            case 67:
            this.PrintDetailsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 679 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.PrintDetailsBtn.Click += new System.Windows.RoutedEventHandler(this.PrintDetailsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 68:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 683 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

