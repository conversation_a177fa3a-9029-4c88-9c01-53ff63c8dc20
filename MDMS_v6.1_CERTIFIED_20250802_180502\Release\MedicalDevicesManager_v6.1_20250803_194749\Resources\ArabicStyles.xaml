<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- خطوط عربية محسنة -->
    <FontFamily x:Key="ArabicFont">Segoe UI, <PERSON><PERSON><PERSON>, Arial, Microsoft Sans Serif</FontFamily>
    <FontFamily x:Key="ArabicTitleFont">Segoe UI, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Microsoft Sans Serif</FontFamily>
    
    <!-- أحجام الخطوط -->
    <sys:Double x:Key="SmallFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="NormalFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="LargeFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="TitleFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="HeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>

    <!-- أنماط النصوص العربية -->
    <Style x:Key="ArabicTextBlock" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="TextAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <Style x:Key="ArabicTitleTextBlock" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicTitleFont}"/>
        <Setter Property="FontSize" Value="{StaticResource TitleFontSize}"/>
        <Setter Property="FontWeight" Value="Bold"/>
    </Style>

    <Style x:Key="ArabicHeaderTextBlock" TargetType="TextBlock" BasedOn="{StaticResource ArabicTextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicTitleFont}"/>
        <Setter Property="FontSize" Value="{StaticResource HeaderFontSize}"/>
        <Setter Property="FontWeight" Value="Bold"/>
    </Style>

    <!-- أنماط الحقول النصية العربية -->
    <Style x:Key="ArabicTextBox" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="TextAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="Height" Value="35"/>
        <Setter Property="BorderBrush" Value="#CED4DA"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- أنماط الأزرار العربية -->
    <Style x:Key="ArabicButton" TargetType="Button">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="Height" Value="35"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="5"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Opacity" Value="0.9"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Opacity" Value="0.8"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PrimaryArabicButton" TargetType="Button" BasedOn="{StaticResource ArabicButton}">
        <Setter Property="Background" Value="#007BFF"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="SuccessArabicButton" TargetType="Button" BasedOn="{StaticResource ArabicButton}">
        <Setter Property="Background" Value="#28A745"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="DangerArabicButton" TargetType="Button" BasedOn="{StaticResource ArabicButton}">
        <Setter Property="Background" Value="#DC3545"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- أنماط القوائم المنسدلة العربية -->
    <Style x:Key="ArabicComboBox" TargetType="ComboBox">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="Height" Value="35"/>
        <Setter Property="BorderBrush" Value="#CED4DA"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <ToggleButton x:Name="ToggleButton"
                                    Grid.Column="2"
                                    Focusable="false"
                                    IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                    ClickMode="Press"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                            <ToggleButton.Template>
                                <ControlTemplate TargetType="ToggleButton">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="3">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition />
                                                <ColumnDefinition Width="20" />
                                            </Grid.ColumnDefinitions>
                                            <Path x:Name="Arrow" Grid.Column="1"
                                                Fill="#666"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Data="M 0 0 L 4 4 L 8 0 Z"/>
                                        </Grid>
                                    </Border>
                                </ControlTemplate>
                            </ToggleButton.Template>
                        </ToggleButton>
                        <ContentPresenter x:Name="ContentSite"
                                        IsHitTestVisible="False"
                                        Content="{TemplateBinding SelectionBoxItem}"
                                        ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                        ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                        Margin="10,3,23,3"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Right" />
                        <TextBox x:Name="PART_EditableTextBox"
                               Style="{x:Null}"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Center"
                               Margin="3,3,23,3"
                               Focusable="True"
                               Background="Transparent"
                               Visibility="Hidden"
                               IsReadOnly="{TemplateBinding IsReadOnly}"/>
                        <Popup x:Name="Popup"
                             Placement="Bottom"
                             IsOpen="{TemplateBinding IsDropDownOpen}"
                             AllowsTransparency="True"
                             Focusable="False"
                             PopupAnimation="Slide">
                            <Grid x:Name="DropDown"
                                SnapsToDevicePixels="True"
                                MinWidth="{TemplateBinding ActualWidth}"
                                MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                <Border x:Name="DropDownBorder"
                                      Background="White"
                                      BorderThickness="1"
                                      BorderBrush="#CED4DA"
                                      CornerRadius="3"/>
                                <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                </ScrollViewer>
                            </Grid>
                        </Popup>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط التسميات العربية -->
    <Style x:Key="ArabicLabel" TargetType="Label">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <!-- أنماط الجداول العربية -->
    <Style x:Key="ArabicDataGrid" TargetType="DataGrid">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
        <Setter Property="RowBackground" Value="White"/>
    </Style>

    <!-- أنماط النوافذ العربية -->
    <Style x:Key="ArabicWindow" TargetType="Window">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="WindowStartupLocation" Value="CenterScreen"/>
    </Style>

</ResourceDictionary>
