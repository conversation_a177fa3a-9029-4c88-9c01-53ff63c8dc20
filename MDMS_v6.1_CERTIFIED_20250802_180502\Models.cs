using System;
using System.ComponentModel.DataAnnotations;

namespace MedicalDevicesManager
{
    public class MedicalDevice
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Brand { get; set; } = "";
        public string Model { get; set; } = "";
        public string SerialNumber { get; set; } = "";
        public string Category { get; set; } = "";
        public string Description { get; set; } = "";
        public decimal PurchasePrice { get; set; }
        public decimal SellingPrice { get; set; }
        public DateTime PurchaseDate { get; set; }
        public DateTime WarrantyStartDate { get; set; }
        public DateTime WarrantyEndDate { get; set; }
        public string Supplier { get; set; } = "";
        public string Location { get; set; } = "";
        public string Status { get; set; } = "";

        // المستندات والملفات
        public string UserManualPath { get; set; } = "";
        public string MaintenanceManualPath { get; set; } = "";
        public string OriginCertificatePath { get; set; } = "";
        public string QualityCertificatePath { get; set; } = "";
        public string OfficialCertificationsPath { get; set; } = "";
        public string TechnicalInfoBookletPath { get; set; } = "";
        public string ImportPapersPath { get; set; } = "";
        public string ContractPapersPath { get; set; } = "";

        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // جدول الأرقام التسلسلية للأجهزة
    public class DeviceSerialNumber
    {
        public int Id { get; set; }
        public int DeviceId { get; set; }
        public string SerialNumber { get; set; } = "";
        public string ComponentName { get; set; } = "";
        public string ComponentType { get; set; } = "";
        public string Description { get; set; } = ""; // للتوافق مع الكود الموجود
        public string Location { get; set; } = ""; // للتوافق مع الكود الموجود
        public DateTime? InstallationDate { get; set; } // للتوافق مع الكود الموجود
        public string Notes { get; set; } = ""; // للتوافق مع الكود الموجود
        public string Status { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }

        // خصائص إضافية للإحصائيات (لا تُحفظ في قاعدة البيانات)
        public int MaintenanceCount { get; set; }
        public int SalesCount { get; set; }

        // Navigation property
        public MedicalDevice MedicalDevice { get; set; } = new MedicalDevice();
    }

    // جدول الفئات المخصص
    public class DeviceCategory
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Icon { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // جدول فئات المخزون
    public class InventoryCategory
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // جدول أنواع العملاء
    public class CustomerType
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // جدول المدن
    public class City
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Country { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // جدول أسماء المستلمين
    public class RecipientName
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Address { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // جدول أسماء الفنيين
    public class TechnicianName
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Specialization { get; set; } = "";
        public string Phone { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // جدول الإشعارات والتنبيهات
    public class Notification
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = ""; // Info, Warning, Error, Success
        public string Category { get; set; } = ""; // Inventory, Maintenance, Sales, etc.
        public int? RelatedItemId { get; set; }
        public string RelatedItemType { get; set; } = ""; // Device, Inventory, Sale, etc.
        public bool IsRead { get; set; } = false;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime? ReadDate { get; set; }
        public string Priority { get; set; } = "Medium"; // Low, Medium, High, Critical
    }

    // جدول النسخ الاحتياطي
    public class BackupRecord
    {
        public int Id { get; set; }
        public string BackupName { get; set; } = "";
        public string BackupPath { get; set; } = "";
        public string BackupType { get; set; } = ""; // Manual, Automatic, Scheduled
        public long FileSize { get; set; }
        public string Status { get; set; } = ""; // Success, Failed, InProgress
        public string Description { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public string ErrorMessage { get; set; } = "";
    }

    // جدول تصدير البيانات
    public class ExportRecord
    {
        public int Id { get; set; }
        public string ExportName { get; set; } = "";
        public string ExportType { get; set; } = ""; // Excel, PDF
        public string DataType { get; set; } = ""; // Devices, Inventory, Sales, etc.
        public string FilePath { get; set; } = "";
        public string Parameters { get; set; } = ""; // JSON string for export parameters
        public string Status { get; set; } = ""; // Success, Failed, InProgress
        public long FileSize { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public string CreatedBy { get; set; } = "";
    }

    // جدول إعدادات المخزون المتقدمة
    public class InventorySettings
    {
        public int Id { get; set; }
        public int InventoryItemId { get; set; }
        public int MinimumStock { get; set; } = 0;
        public int ReorderLevel { get; set; } = 0;
        public int MaximumStock { get; set; } = 0;
        public bool EnableLowStockAlert { get; set; } = true;
        public bool EnableExpiryAlert { get; set; } = true;
        public int ExpiryAlertDays { get; set; } = 30; // تنبيه قبل انتهاء الصلاحية بـ 30 يوم
        public string AlertEmail { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }

        // Navigation property
        public InventoryItem InventoryItem { get; set; } = new InventoryItem();
    }

    public class InventoryItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Category { get; set; } = "";
        public string Description { get; set; } = "";
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public string Unit { get; set; } = "";
        public decimal UnitPrice { get; set; }
        public string Location { get; set; } = "";
        public string Status { get; set; } = "";
        public DateTime? ExpiryDate { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
        
        public bool IsLowStock => CurrentStock <= MinimumStock;
        public decimal TotalValue => CurrentStock * UnitPrice;
    }
    
    public class Customer
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string CustomerType { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string Address { get; set; } = "";
        public string City { get; set; } = "";
        public string PostalCode { get; set; } = "";
        public decimal CreditLimit { get; set; }
        public string Status { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }
    
    public class Supplier
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string Address { get; set; } = "";
        public string City { get; set; } = "";
        public string PostalCode { get; set; } = "";
        public decimal Rating { get; set; }
        public string Status { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // باقي الكلاسات المطلوبة
    public class Sale
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = "";
        public int? MedicalDeviceId { get; set; }
        public int? CustomerId { get; set; }
        public string DeviceName { get; set; } = "";
        public string SerialNumber { get; set; } = ""; // الرقم التسلسلي للجهاز المباع
        public string CustomerName { get; set; } = "";
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Discount { get; set; } // للتوافق مع الكود الموجود
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public string PaymentStatus { get; set; } = "";
        public string PaymentMethod { get; set; } = "";
        public DateTime SaleDate { get; set; }
        public string Notes { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }

        // العلاقات
        public MedicalDevice MedicalDevice { get; set; } = new MedicalDevice();
        public Customer Customer { get; set; } = new Customer();
    }

    public class DeviceInstallation
    {
        public int Id { get; set; }
        public int? DeviceId { get; set; }
        public int? MedicalDeviceId { get; set; } // للتوافق مع الكود الموجود
        public int? CustomerId { get; set; }
        public string DeviceName { get; set; } = "";
        public string CustomerName { get; set; } = "";
        public string SerialNumber { get; set; } = "";
        public string Model { get; set; } = "";
        public string Manufacturer { get; set; } = "";
        public DateTime InstallationDate { get; set; }
        public string InstallationLocation { get; set; } = "";
        public string ContactPersonName { get; set; } = "";
        public string ContactPersonPhone { get; set; } = "";
        public string ContactPersonEmail { get; set; } = ""; // للتوافق مع الكود الموجود
        public string ContactPersonPosition { get; set; } = ""; // للتوافق مع الكود الموجود
        public string TechnicianName { get; set; } = ""; // للتوافق مع الكود الموجود
        public string InstallationNotes { get; set; } = ""; // للتوافق مع الكود الموجود
        public string EquipmentCondition { get; set; } = ""; // للتوافق مع الكود الموجود
        public string SupplierCompany { get; set; } = ""; // الشركة المجهزة
        public decimal InstallationCost { get; set; }
        public int WarrantyYears { get; set; }
        public DateTime WarrantyEndDate { get; set; }
        public string InstallationStatus { get; set; } = "";
        public string Notes { get; set; } = "";
        public string InstallationReportBookletPath { get; set; } = ""; // مسار كتيب تقرير التنصيب
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class Shipment
    {
        public int Id { get; set; }
        public string ShipmentNumber { get; set; } = "";
        public string RecipientName { get; set; } = "";
        public string RecipientPhone { get; set; } = "";
        public string RecipientAddress { get; set; } = "";
        public string DeliveryAddress { get; set; } = ""; // للتوافق مع الكود الموجود
        public string City { get; set; } = "";
        public DateTime ShipmentDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public DateTime? ReceiptDate { get; set; } // تاريخ الاستلام
        public string Status { get; set; } = "";
        public decimal ShippingCost { get; set; }
        public decimal TotalValue { get; set; } // للتوافق مع الكود الموجود
        public string TrackingNumber { get; set; } = ""; // للتوافق مع الكود الموجود
        public string ShipmentDocumentsPath { get; set; } = ""; // أوراق الشحنة
        public string Notes { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class MaintenanceRecord
    {
        public int Id { get; set; }
        public int? DeviceId { get; set; }
        public int? MedicalDeviceId { get; set; } // للتوافق مع الكود الموجود
        public string DeviceName { get; set; } = "";
        public string SerialNumber { get; set; } = "";
        public DateTime MaintenanceDate { get; set; }
        public string MaintenanceType { get; set; } = "";
        public string TechnicianName { get; set; } = "";
        public string Description { get; set; } = "";
        public decimal Cost { get; set; }
        public string Status { get; set; } = "";
        public string Notes { get; set; } = "";
        public string ReportBookletPath { get; set; } = ""; // مسار كتيب تقرير الصيانة
        public string DocumentsPath { get; set; } = ""; // مسار الأوراق والمخاطبات
        public string SupplierCompany { get; set; } = ""; // الشركة المجهزة
        public DateTime? NextMaintenanceDate { get; set; } // تاريخ الصيانة التالية
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // الكلاسات الجديدة للنظام الذكي
    public class SmartAlert
    {
        public int Id { get; set; }
        public string AlertType { get; set; } = "";
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Priority { get; set; } = "";
        public string Status { get; set; } = "";
        public int? RelatedItemId { get; set; }
        public string RelatedItemType { get; set; } = "";
        public string RelatedItemName { get; set; } = "";
        public DateTime AlertDate { get; set; }
        public DateTime? DueDate { get; set; }
        public string ActionRequired { get; set; } = "";
        public bool IsAutoGenerated { get; set; }
        public string Category { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public DateTime? CompletedDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class AlertSettings
    {
        public int Id { get; set; }
        public string AlertType { get; set; } = "";
        public bool IsEnabled { get; set; } = true;
        public int DaysBefore { get; set; }
        public string NotificationMethod { get; set; } = "";
        public string Recipients { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class SearchIndex
    {
        public int Id { get; set; }
        public string ItemType { get; set; } = "";
        public int ItemId { get; set; }
        public string SearchableText { get; set; } = "";
        public string Keywords { get; set; } = "";
        public string Category { get; set; } = "";
        public decimal? NumericValue1 { get; set; }
        public decimal? NumericValue2 { get; set; }
        public DateTime? DateValue1 { get; set; }
        public DateTime? DateValue2 { get; set; }
        public string Status { get; set; } = "";
        public string Tags { get; set; } = "";
        public int SearchCount { get; set; }
        public DateTime? LastSearched { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class LiveStatistics
    {
        public int Id { get; set; }
        public string StatType { get; set; } = "";
        public string StatName { get; set; } = "";
        public decimal CurrentValue { get; set; }
        public decimal PreviousValue { get; set; }
        public decimal ChangeValue { get; set; }
        public decimal ChangePercentage { get; set; }
        public string Trend { get; set; } = "";
        public string Period { get; set; } = "";
        public DateTime CalculatedDate { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // نموذج حركة المخزون
    public class InventoryMovement
    {
        public int Id { get; set; }
        public int InventoryItemId { get; set; }
        public string MovementType { get; set; } = ""; // إضافة، خصم، تعديل
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public string Reference { get; set; } = ""; // مرجع العملية (رقم المبيعة، رقم الشحنة، إلخ)
        public string Notes { get; set; } = "";
        public DateTime MovementDate { get; set; }
        public string CreatedBy { get; set; } = "";

        // خصائص للعرض
        public string MovementTypeDisplay => MovementType switch
        {
            "إضافة" => "➕ إضافة",
            "خصم" => "➖ خصم",
            "تعديل" => "✏️ تعديل",
            _ => MovementType
        };
    }

    // جدول قطع الغيار والملحقات
    public class SparePart
    {
        public int Id { get; set; }
        public string Name { get; set; } = ""; // اسم القطعة
        public string AssociatedDeviceName { get; set; } = ""; // اسم الجهاز المرتبط
        public int AvailableQuantity { get; set; } // العدد المتوفر
        public decimal PurchasePrice { get; set; } // سعر الشراء
        public decimal IndividualSellingPrice { get; set; } // سعر البيع مفرد
        public decimal WholesaleSellingPrice { get; set; } // سعر البيع جملة
        public DateTime PurchaseDate { get; set; } // تاريخ الشراء
        public string WorkshopStorageLocation { get; set; } = ""; // موقع القطعة في مخزن الورشة
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // نموذج عروض الأسعار
    public class Quotation
    {
        public int Id { get; set; }
        public string QuotationNumber { get; set; } = "";
        public string CustomerName { get; set; } = "";
        public string CustomerEmail { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public DateTime QuotationDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public string Status { get; set; } = ""; // مسودة، مرسل، مقبول، مرفوض، منتهي الصلاحية
        public DateTime ValidUntil { get; set; }
        public string Notes { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }

        // خصائص للعرض
        public string StatusDisplay => Status switch
        {
            "مسودة" => "📝 مسودة",
            "مرسل" => "📤 مرسل",
            "مقبول" => "✅ مقبول",
            "مرفوض" => "❌ مرفوض",
            "منتهي الصلاحية" => "⏰ منتهي الصلاحية",
            _ => Status
        };

        public bool IsExpired => DateTime.Now > ValidUntil;
        public int DaysUntilExpiry => (ValidUntil - DateTime.Now).Days;
    }

    // نموذج عناصر عروض الأسعار
    public class QuotationItem
    {
        public int Id { get; set; }
        public int QuotationId { get; set; }
        public string DeviceName { get; set; } = "";
        public string Brand { get; set; } = "";
        public string Model { get; set; } = "";
        public string Description { get; set; } = "";
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal FinalPrice { get; set; }
        public int DeliveryDays { get; set; }
        public double WarrantyYears { get; set; }
        public string Features { get; set; } = ""; // JSON string للمزايا
        public string Status { get; set; } = ""; // متوفر، طلب مسبق، غير متوفر
        public string Supplier { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdated { get; set; }

        // Navigation property
        public Quotation Quotation { get; set; } = new Quotation();

        // خصائص محسوبة
        public decimal TotalWithoutDiscount => Quantity * UnitPrice;
        public decimal CalculatedDiscount => TotalWithoutDiscount * (DiscountPercentage / 100);
        public decimal CalculatedFinalPrice => TotalWithoutDiscount - CalculatedDiscount;
    }

    // نموذج سجل الأنشطة
    public class ActivityLog
    {
        public int Id { get; set; }
        public string ActivityType { get; set; } = "";
        public string Description { get; set; } = "";
        public string Details { get; set; } = "";
        public DateTime ActivityDate { get; set; }
        public string UserName { get; set; } = "";
        public string EntityType { get; set; } = ""; // Device, Customer, Sale, etc.
        public int? EntityId { get; set; }
        public string IPAddress { get; set; } = "";
        public bool IsSuccess { get; set; } = true;
        public string ErrorMessage { get; set; } = "";

        // خصائص للعرض
        public string ActivityTypeDisplay => ActivityType switch
        {
            "إنشاء" => "➕ إنشاء",
            "تعديل" => "✏️ تعديل",
            "حذف" => "🗑️ حذف",
            "عرض" => "👁️ عرض",
            "طباعة" => "🖨️ طباعة",
            "تصدير" => "📤 تصدير",
            "بحث" => "🔍 بحث",
            "تسجيل دخول" => "🔐 تسجيل دخول",
            "تسجيل خروج" => "🚪 تسجيل خروج",
            _ => ActivityType
        };

        public string StatusDisplay => IsSuccess ? "✅ نجح" : "❌ فشل";
    }
}
