<Application x:Class="MedicalDevicesManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <!-- تضمين الأنماط العربية -->
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/ArabicStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- الألوان الحديثة -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#3B82F6"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#6B7280"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#10B981"/>
            <SolidColorBrush x:Key="DangerBrush" Color="#EF4444"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
            <SolidColorBrush x:Key="InfoBrush" Color="#06B6D4"/>
            <SolidColorBrush x:Key="LightBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="DarkBrush" Color="#1F2937"/>
            <SolidColorBrush x:Key="BorderBrush" Color="#E5E7EB"/>
            <SolidColorBrush x:Key="TextBrush" Color="#374151"/>
            <SolidColorBrush x:Key="MutedTextBrush" Color="#6B7280"/>

            <!-- الخطوط الحديثة -->
            <FontFamily x:Key="ModernFont">Segoe UI Variable, Segoe UI, Tahoma, Arial</FontFamily>

            <!-- أنماط الأزرار الحديثة -->
            <Style x:Key="ModernButton" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="16,10"/>
                <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    CornerRadius="8"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
                <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
            </Style>

            <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
                <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
            </Style>

            <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
                <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
            </Style>

            <!-- أنماط حقول النص الحديثة -->
            <Style x:Key="ModernTextBox" TargetType="TextBox">
                <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Padding" Value="12,10"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TextBox">
                            <Border x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="8">
                                <ScrollViewer x:Name="PART_ContentHost"
                                            Focusable="false"
                                            HorizontalScrollBarVisibility="Hidden"
                                            VerticalScrollBarVisibility="Hidden"
                                            Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="BorderBrush" Value="#9CA3AF"/>
                                </Trigger>
                                <Trigger Property="IsFocused" Value="True">
                                    <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                    <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- أنماط القوائم المنسدلة الحديثة -->
            <Style x:Key="ModernComboBox" TargetType="ComboBox">
                <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Padding" Value="12,10"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            </Style>

            <!-- أنماط البطاقات الحديثة -->
            <Style x:Key="ModernCard" TargetType="Border">
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="12"/>
                <Setter Property="Padding" Value="24"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="4" Opacity="0.1" BlurRadius="20"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- أنماط النصوص الحديثة -->
            <Style x:Key="ModernTitle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                <Setter Property="FontSize" Value="20"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            </Style>

            <Style x:Key="ModernSubtitle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            </Style>

            <Style x:Key="ModernLabel" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            </Style>

            <Style x:Key="ModernMutedText" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ModernFont}"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Foreground" Value="{StaticResource MutedTextBrush}"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
