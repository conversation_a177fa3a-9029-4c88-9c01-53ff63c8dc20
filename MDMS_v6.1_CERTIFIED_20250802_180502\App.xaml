<Application x:Class="MedicalDevicesManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <!-- تضمين الأنماط العربية -->
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/ArabicStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- الألوان الأساسية -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#007BFF"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#6C757D"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#28A745"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#DC3545"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FFC107"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#17A2B8"/>
        <SolidColorBrush x:Key="LightBrush" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="DarkBrush" Color="#343A40"/>
        
        <!-- أنماط الأزرار -->
        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center" 
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#0056B3"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004085"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- أنماط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
