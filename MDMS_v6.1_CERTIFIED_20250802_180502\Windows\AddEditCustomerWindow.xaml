<Window x:Class="MedicalDevicesManager.Windows.AddEditCustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل عميل" Height="650" Width="600"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize"
        FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="إضافة عميل جديد" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- اسم العميل -->
                <TextBlock Text="اسم العميل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- نوع العميل -->
                <TextBlock Text="نوع العميل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0" x:Name="CustomerTypeComboBox" Height="35" Padding="10,8" FontSize="14"
                              IsEditable="True" Margin="0,0,5,0"/>

                    <Button Grid.Column="1" x:Name="ManageCustomerTypesBtn" Content="⚙️" Width="35" Height="35"
                            Background="#6C757D" Foreground="White" BorderThickness="0"
                            ToolTip="إدارة أنواع العملاء" Click="ManageCustomerTypesBtn_Click"/>
                </Grid>
                
                <!-- معلومات الاتصال -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الهاتف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PhoneTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="البريد الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="EmailTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- العنوان -->
                <TextBlock Text="العنوان *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AddressTextBox" Height="60" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- المدينة والرمز البريدي -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المدينة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox Grid.Column="0" x:Name="CityComboBox" Height="35" Padding="10,8" FontSize="14"
                                      IsEditable="True" Margin="0,0,5,0"/>

                            <Button Grid.Column="1" x:Name="ManageCitiesBtn" Content="⚙️" Width="35" Height="35"
                                    Background="#6C757D" Foreground="White" BorderThickness="0"
                                    ToolTip="إدارة المدن" Click="ManageCitiesBtn_Click"/>
                        </Grid>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الرمز البريدي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PostalCodeTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- الحد الائتماني -->
                <TextBlock Text="الحد الائتماني *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="CreditLimitTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الحالة -->
                <TextBlock Text="الحالة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="StatusComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15">
                    <ComboBoxItem Content="نشط"/>
                    <ComboBoxItem Content="معلق"/>
                    <ComboBoxItem Content="محظور"/>
                </ComboBox>
                
                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox" Height="80" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- معلومات إضافية -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات إضافية" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="تاريخ الإنشاء:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="CreatedDateTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="آخر تحديث:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="LastUpdatedTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ العميل" Width="140" Height="40"
                    Background="#28A745" Foreground="White" BorderThickness="0"
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40"
                    Background="#DC3545" Foreground="White" BorderThickness="0"
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
