<Window x:Class="MedicalDevicesManager.Windows.AddEditMaintenanceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل سجل صيانة" Height="650" Width="600"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="إضافة سجل صيانة جديد" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- الجهاز والرقم التسلسلي -->
                <TextBlock Text="الجهاز والرقم التسلسلي *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- حقل عرض الجهاز المختار -->
                    <TextBox x:Name="SelectedDeviceTextBox" Grid.Column="0" Height="35" Padding="10,8" FontSize="14"
                             IsReadOnly="True" Background="#F8F9FA"
                             Text="اضغط على زر الاختيار لتحديد الجهاز..."
                             ToolTip="الجهاز المختار والرقم التسلسلي"/>

                    <!-- زر اختيار الجهاز -->
                    <Button x:Name="SelectDeviceButton" Grid.Column="1" Width="120" Height="35" Margin="10,0,0,0"
                            Content="🔍 اختيار الجهاز" FontSize="12" FontWeight="SemiBold"
                            Background="#007BFF" Foreground="White" BorderThickness="0"
                            Click="SelectDeviceButton_Click"
                            ToolTip="فتح قائمة الأجهزة لاختيار الجهاز المراد صيانته"/>
                </Grid>

                <!-- ملاحظة توضيحية -->
                <TextBlock Text="💡 اختر الجهاز المراد صيانته من قائمة الأجهزة المضافة"
                          FontSize="11" Foreground="Gray" Margin="0,0,0,15"/>

                <!-- نوع الصيانة -->
                <TextBlock Text="نوع الصيانة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="MaintenanceTypeComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15">
                    <ComboBoxItem Content="صيانة دورية"/>
                    <ComboBoxItem Content="إصلاح"/>
                    <ComboBoxItem Content="فحص ضمان"/>
                    <ComboBoxItem Content="صيانة طارئة"/>
                    <ComboBoxItem Content="استبدال قطع"/>
                    <ComboBoxItem Content="معايرة"/>
                    <ComboBoxItem Content="تنظيف شامل"/>
                    <ComboBoxItem Content="فحص أمان"/>
                </ComboBox>
                
                <!-- تاريخ الصيانة -->
                <TextBlock Text="تاريخ الصيانة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <DatePicker x:Name="MaintenanceDatePicker" Height="35" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الحالة -->
                <TextBlock Text="حالة الصيانة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="StatusComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"
                          SelectionChanged="StatusComboBox_SelectionChanged">
                    <ComboBoxItem Content="مجدولة"/>
                    <ComboBoxItem Content="قيد التنفيذ"/>
                    <ComboBoxItem Content="مكتملة"/>
                    <ComboBoxItem Content="مؤجلة"/>
                    <ComboBoxItem Content="ملغية"/>
                    <ComboBoxItem Content="تحتاج قطع غيار"/>
                </ComboBox>

                <!-- تاريخ الصيانة القادم (يظهر فقط عند اختيار مجدولة) -->
                <StackPanel x:Name="NextMaintenanceDatePanel" Visibility="Collapsed" Margin="0,0,0,15">
                    <TextBlock Text="تاريخ الصيانة القادم" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="NextMaintenanceDatePicker" Height="35" FontSize="14"/>
                </StackPanel>

                <!-- التكلفة واسم الفني -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="التكلفة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CostTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="اسم الفني *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox Grid.Column="0" x:Name="TechnicianComboBox" Height="35" Padding="10,8" FontSize="14"
                                      IsEditable="True" Margin="0,0,5,0"
                                      DisplayMemberPath="Name" SelectedValuePath="Name"/>

                            <Button Grid.Column="1" x:Name="ManageTechniciansBtn" Content="⚙️" Width="35" Height="35"
                                    Background="#6C757D" Foreground="White" BorderThickness="0"
                                    ToolTip="إدارة أسماء الفنيين" Click="ManageTechniciansBtn_Click"/>
                        </Grid>
                    </StackPanel>
                </Grid>

                <!-- الشركة المجهزة -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="الشركة المجهزة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="SupplierCompanyTextBox" Height="35" Padding="10,8" FontSize="14"
                             ToolTip="اسم الشركة المجهزة للجهاز"/>
                </StackPanel>

                <!-- الوصف -->
                <TextBlock Text="وصف الصيانة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" Height="100" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- ملاحظات إضافية -->
                <TextBlock Text="ملاحظات إضافية" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox" Height="80" Padding="10,8" FontSize="14"
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>

                <!-- كتيب تقرير الصيانة -->
                <TextBlock Text="كتيب تقرير الصيانة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0" x:Name="ReportBookletPathTextBox" Height="35" Padding="10,8"
                             FontSize="14" IsReadOnly="True" Background="#F8F9FA"
                             Text="لم يتم اختيار ملف" Margin="0,0,5,0"/>

                    <Button Grid.Column="1" x:Name="BrowseReportBtn" Content="📁 استعراض" Width="100" Height="35"
                            Background="#17A2B8" Foreground="White" BorderThickness="0"
                            FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="BrowseReportBtn_Click"/>

                    <Button Grid.Column="2" x:Name="ViewReportBtn" Content="👁️ عرض" Width="80" Height="35"
                            Background="#28A745" Foreground="White" BorderThickness="0"
                            FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="ViewReportBtn_Click"
                            IsEnabled="False"/>

                    <Button Grid.Column="3" x:Name="RemoveReportBtn" Content="🗑️ حذف" Width="80" Height="35"
                            Background="#DC3545" Foreground="White" BorderThickness="0"
                            FontSize="12" FontWeight="SemiBold" Click="RemoveReportBtn_Click"
                            IsEnabled="False"/>
                </Grid>

                <!-- الأوراق والمخاطبات -->
                <TextBlock Text="الأوراق والمخاطبات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0" x:Name="DocumentsTextBox" Height="35" Padding="10,8" FontSize="14"
                             IsReadOnly="True" Background="#F8F9FA" Margin="0,0,5,0"
                             Text="لا توجد ملفات مرفقة"/>

                    <Button Grid.Column="1" x:Name="AddDocumentBtn" Content="📎 إرفاق ملف" Height="35" Width="100"
                            Background="#17A2B8" Foreground="White" BorderThickness="0"
                            FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="AddDocumentBtn_Click"/>

                    <Button Grid.Column="2" x:Name="RemoveDocumentBtn" Content="🗑️ حذف" Height="35" Width="80"
                            Background="#DC3545" Foreground="White" BorderThickness="0"
                            FontSize="12" FontWeight="SemiBold" Click="RemoveDocumentBtn_Click"
                            IsEnabled="False"/>
                </Grid>

                <!-- معلومات الجهاز -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات الجهاز" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <TextBlock x:Name="DeviceInfoTextBlock" Text="اختر جهازاً لعرض المعلومات" FontSize="12" Foreground="#6C757D"/>
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="تاريخ الإنشاء:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="CreatedDateTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="آخر تحديث:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="LastUpdatedTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ سجل الصيانة" Width="160" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
