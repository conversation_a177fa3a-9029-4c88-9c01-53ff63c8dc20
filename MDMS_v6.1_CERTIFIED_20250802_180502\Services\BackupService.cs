using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Collections.Generic;

namespace MedicalDevicesManager.Services
{
    public class BackupService
    {
        private readonly MedicalDevicesContext _context;
        private readonly string _backupDirectory;

        public BackupService(MedicalDevicesContext context)
        {
            _context = context;
            _backupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
            
            // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }
        }

        // إنشاء نسخة احتياطية يدوية
        public async Task<bool> CreateManualBackupAsync(string backupName = null, string description = "")
        {
            return await CreateBackupAsync("Manual", backupName, description);
        }

        // إنشاء نسخة احتياطية تلقائية
        public async Task<bool> CreateAutomaticBackupAsync()
        {
            var backupName = $"AutoBackup_{DateTime.Now:yyyyMMdd_HHmmss}";
            return await CreateBackupAsync("Automatic", backupName, "نسخة احتياطية تلقائية");
        }

        // إنشاء نسخة احتياطية مجدولة
        public async Task<bool> CreateScheduledBackupAsync()
        {
            var backupName = $"ScheduledBackup_{DateTime.Now:yyyyMMdd_HHmmss}";
            return await CreateBackupAsync("Scheduled", backupName, "نسخة احتياطية مجدولة");
        }

        // الدالة الأساسية لإنشاء النسخة الاحتياطية
        private async Task<bool> CreateBackupAsync(string backupType, string backupName = null, string description = "")
        {
            var backupRecord = new BackupRecord();
            
            try
            {
                // إنشاء اسم النسخة الاحتياطية
                if (string.IsNullOrEmpty(backupName))
                {
                    backupName = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                }

                var sourceDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "MedicalDevicesIntegrated.db");
                var backupFileName = $"{backupName}.db";
                var backupPath = Path.Combine(_backupDirectory, backupFileName);

                // إنشاء سجل النسخة الاحتياطية
                backupRecord = new BackupRecord
                {
                    BackupName = backupName,
                    BackupPath = backupPath,
                    BackupType = backupType,
                    Status = "InProgress",
                    Description = description,
                    CreatedDate = DateTime.Now
                };

                _context.BackupRecords.Add(backupRecord);
                await _context.SaveChangesAsync();

                // التحقق من وجود قاعدة البيانات المصدر
                if (!File.Exists(sourceDbPath))
                {
                    throw new FileNotFoundException("قاعدة البيانات المصدر غير موجودة");
                }

                // نسخ قاعدة البيانات
                await Task.Run(() => File.Copy(sourceDbPath, backupPath, true));

                // الحصول على حجم الملف
                var fileInfo = new FileInfo(backupPath);
                backupRecord.FileSize = fileInfo.Length;
                backupRecord.Status = "Success";
                backupRecord.CompletedDate = DateTime.Now;

                await _context.SaveChangesAsync();

                // إضافة إشعار نجاح
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "نجح إنشاء النسخة الاحتياطية",
                    $"تم إنشاء النسخة الاحتياطية '{backupName}' بنجاح. الحجم: {FormatFileSize(fileInfo.Length)}",
                    "Success",
                    "Backup",
                    backupRecord.Id,
                    "BackupRecord",
                    "Medium"
                );

                return true;
            }
            catch (Exception ex)
            {
                // تحديث حالة الفشل
                if (backupRecord.Id > 0)
                {
                    backupRecord.Status = "Failed";
                    backupRecord.ErrorMessage = ex.Message;
                    backupRecord.CompletedDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                }

                // إضافة إشعار فشل
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "فشل في إنشاء النسخة الاحتياطية",
                    $"فشل في إنشاء النسخة الاحتياطية: {ex.Message}",
                    "Error",
                    "Backup",
                    backupRecord.Id,
                    "BackupRecord",
                    "High"
                );

                return false;
            }
        }

        // استعادة نسخة احتياطية
        public async Task<bool> RestoreBackupAsync(int backupId)
        {
            try
            {
                var backupRecord = await _context.BackupRecords.FindAsync(backupId);
                if (backupRecord == null || backupRecord.Status != "Success")
                {
                    throw new InvalidOperationException("النسخة الاحتياطية غير صالحة أو غير موجودة");
                }

                if (!File.Exists(backupRecord.BackupPath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود");
                }

                var currentDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "MedicalDevicesIntegrated.db");
                var tempBackupPath = Path.Combine(_backupDirectory, $"BeforeRestore_{DateTime.Now:yyyyMMdd_HHmmss}.db");

                // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
                if (File.Exists(currentDbPath))
                {
                    File.Copy(currentDbPath, tempBackupPath, true);
                }

                // استعادة النسخة الاحتياطية
                File.Copy(backupRecord.BackupPath, currentDbPath, true);

                // إضافة إشعار نجاح
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "نجحت استعادة النسخة الاحتياطية",
                    $"تم استعادة النسخة الاحتياطية '{backupRecord.BackupName}' بنجاح. تم حفظ نسخة من البيانات السابقة.",
                    "Success",
                    "Backup",
                    backupId,
                    "BackupRecord",
                    "High"
                );

                return true;
            }
            catch (Exception ex)
            {
                // إضافة إشعار فشل
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "فشل في استعادة النسخة الاحتياطية",
                    $"فشل في استعادة النسخة الاحتياطية: {ex.Message}",
                    "Error",
                    "Backup",
                    backupId,
                    "BackupRecord",
                    "Critical"
                );

                return false;
            }
        }

        // حذف نسخة احتياطية
        public async Task<bool> DeleteBackupAsync(int backupId)
        {
            try
            {
                var backupRecord = await _context.BackupRecords.FindAsync(backupId);
                if (backupRecord == null)
                {
                    return false;
                }

                // حذف الملف إذا كان موجوداً
                if (File.Exists(backupRecord.BackupPath))
                {
                    File.Delete(backupRecord.BackupPath);
                }

                // حذف السجل من قاعدة البيانات
                _context.BackupRecords.Remove(backupRecord);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        // الحصول على جميع النسخ الاحتياطية
        public async Task<List<BackupRecord>> GetAllBackupsAsync()
        {
            return await _context.BackupRecords
                .OrderByDescending(b => b.CreatedDate)
                .ToListAsync();
        }

        // الحصول على النسخ الاحتياطية الناجحة فقط
        public async Task<List<BackupRecord>> GetSuccessfulBackupsAsync()
        {
            return await _context.BackupRecords
                .Where(b => b.Status == "Success")
                .OrderByDescending(b => b.CreatedDate)
                .ToListAsync();
        }

        // تنظيف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 10 نسخ)
        public async Task<bool> CleanupOldBackupsAsync(int keepCount = 10)
        {
            try
            {
                var allBackups = await _context.BackupRecords
                    .Where(b => b.Status == "Success")
                    .OrderByDescending(b => b.CreatedDate)
                    .ToListAsync();

                if (allBackups.Count <= keepCount)
                {
                    return true; // لا حاجة للتنظيف
                }

                var backupsToDelete = allBackups.Skip(keepCount).ToList();
                
                foreach (var backup in backupsToDelete)
                {
                    await DeleteBackupAsync(backup.Id);
                }

                // إضافة إشعار
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "تم تنظيف النسخ الاحتياطية القديمة",
                    $"تم حذف {backupsToDelete.Count} نسخة احتياطية قديمة. تم الاحتفاظ بآخر {keepCount} نسخ.",
                    "Info",
                    "Backup",
                    null,
                    "",
                    "Low"
                );

                return true;
            }
            catch (Exception ex)
            {
                // إضافة إشعار فشل
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "فشل في تنظيف النسخ الاحتياطية",
                    $"فشل في تنظيف النسخ الاحتياطية القديمة: {ex.Message}",
                    "Error",
                    "Backup",
                    null,
                    "",
                    "Medium"
                );

                return false;
            }
        }

        // تنسيق حجم الملف
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        // فحص سلامة النسخة الاحتياطية
        public async Task<bool> VerifyBackupIntegrityAsync(int backupId)
        {
            try
            {
                var backupRecord = await _context.BackupRecords.FindAsync(backupId);
                if (backupRecord == null || !File.Exists(backupRecord.BackupPath))
                {
                    return false;
                }

                // فحص بسيط - التحقق من إمكانية فتح قاعدة البيانات
                var connectionString = $"Data Source={backupRecord.BackupPath}";
                var options = new DbContextOptionsBuilder<MedicalDevicesContext>()
                    .UseSqlite(connectionString)
                    .Options;

                using (var testContext = new MedicalDevicesContext())
                {
                    // محاولة الاتصال وتنفيذ استعلام بسيط
                    await testContext.Database.OpenConnectionAsync();
                    var count = await testContext.MedicalDevices.CountAsync();
                    await testContext.Database.CloseConnectionAsync();
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
