<Window x:Class="MedicalDevicesManager.Windows.SimpleQuotationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="عروض الأسعار - أجهزة تخطيط القلب 12 قناة" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">
    
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel>
                <TextBlock Text="🏥 عروض الأسعار المتاحة" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="أجهزة تخطيط القلب 12 قناة - عدد 3 أجهزة" 
                          FontSize="16" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                
                <!-- العرض الأول -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                        CornerRadius="8" Margin="10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📊 العرض الأول - شيلر AT-102" 
                                  FontSize="18" FontWeight="Bold" 
                                  Foreground="#1976D2" Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="🏷️ المواصفات:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• الماركة: Schiller" Margin="10,0,0,0"/>
                                <TextBlock Text="• الموديل: AT-102" Margin="10,0,0,0"/>
                                <TextBlock Text="• 12 قناة رقمية" Margin="10,0,0,0"/>
                                <TextBlock Text="• شاشة ملونة 7 بوصة" Margin="10,0,0,0"/>
                                <TextBlock Text="• طباعة حرارية عالية الجودة" Margin="10,0,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="💰 التفاصيل المالية:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• سعر الوحدة: 30,000 ريال" Margin="10,0,0,0"/>
                                <TextBlock Text="• العدد المطلوب: 3 أجهزة" Margin="10,0,0,0"/>
                                <TextBlock Text="• المجموع الفرعي: 90,000 ريال" Margin="10,0,0,0"/>
                                <TextBlock Text="• خصم الكمية (5%): -4,500 ريال" Margin="10,0,0,0" Foreground="Green"/>
                                <TextBlock Text="• المجموع النهائي: 85,500 ريال" Margin="10,0,0,0" FontWeight="Bold" FontSize="16" Foreground="#1976D2"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- العرض الثاني -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                        CornerRadius="8" Margin="10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📊 العرض الثاني - فيليبس PageWriter TC70" 
                                  FontSize="18" FontWeight="Bold" 
                                  Foreground="#1976D2" Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="🏷️ المواصفات:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• الماركة: Philips" Margin="10,0,0,0"/>
                                <TextBlock Text="• الموديل: PageWriter TC70" Margin="10,0,0,0"/>
                                <TextBlock Text="• 12 قناة رقمية متقدمة" Margin="10,0,0,0"/>
                                <TextBlock Text="• شاشة لمس 10 بوصة" Margin="10,0,0,0"/>
                                <TextBlock Text="• اتصال لاسلكي WiFi" Margin="10,0,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="💰 التفاصيل المالية:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• سعر الوحدة: 45,000 ريال" Margin="10,0,0,0"/>
                                <TextBlock Text="• العدد المطلوب: 3 أجهزة" Margin="10,0,0,0"/>
                                <TextBlock Text="• المجموع الفرعي: 135,000 ريال" Margin="10,0,0,0"/>
                                <TextBlock Text="• خصم الكمية (8%): -10,800 ريال" Margin="10,0,0,0" Foreground="Green"/>
                                <TextBlock Text="• المجموع النهائي: 124,200 ريال" Margin="10,0,0,0" FontWeight="Bold" FontSize="16" Foreground="#1976D2"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- العرض الثالث -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                        CornerRadius="8" Margin="10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📊 العرض الثالث - جنرال إلكتريك MAC 2000" 
                                  FontSize="18" FontWeight="Bold" 
                                  Foreground="#1976D2" Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="🏷️ المواصفات:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• الماركة: GE Healthcare" Margin="10,0,0,0"/>
                                <TextBlock Text="• الموديل: MAC 2000" Margin="10,0,0,0"/>
                                <TextBlock Text="• 12 قناة عالية الدقة" Margin="10,0,0,0"/>
                                <TextBlock Text="• شاشة ملونة 8 بوصة" Margin="10,0,0,0"/>
                                <TextBlock Text="• طباعة ليزر عالية الجودة" Margin="10,0,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="💰 التفاصيل المالية:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• سعر الوحدة: 38,000 ريال" Margin="10,0,0,0"/>
                                <TextBlock Text="• العدد المطلوب: 3 أجهزة" Margin="10,0,0,0"/>
                                <TextBlock Text="• المجموع الفرعي: 114,000 ريال" Margin="10,0,0,0"/>
                                <TextBlock Text="• خصم الكمية (7%): -7,980 ريال" Margin="10,0,0,0" Foreground="Green"/>
                                <TextBlock Text="• المجموع النهائي: 106,020 ريال" Margin="10,0,0,0" FontWeight="Bold" FontSize="16" Foreground="#1976D2"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- ملخص المقارنة -->
                <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                        CornerRadius="8" Margin="10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📋 ملخص المقارنة والتوصية" 
                                  FontSize="18" FontWeight="Bold" 
                                  Foreground="#1976D2" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="🥉 الأفضل اقتصادياً" FontWeight="Bold" Foreground="#FF9800" HorizontalAlignment="Center"/>
                                <TextBlock Text="شيلر AT-102" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="85,500 ريال" FontSize="16" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="🥇 الأفضل تقنياً" FontWeight="Bold" Foreground="#4CAF50" HorizontalAlignment="Center"/>
                                <TextBlock Text="فيليبس PageWriter TC70" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="124,200 ريال" FontSize="16" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="🥈 الأفضل توازناً" FontWeight="Bold" Foreground="#2196F3" HorizontalAlignment="Center"/>
                                <TextBlock Text="جنرال إلكتريك MAC 2000" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="106,020 ريال" FontSize="16" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#1976D2" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="📄 طباعة العرض" Click="PrintQuotation_Click" 
                        Background="#4CAF50" Foreground="White" BorderThickness="0"
                        Padding="15,8" Margin="10,0" FontWeight="Bold"/>
                <Button Content="📧 إرسال بالبريد" Click="EmailQuotation_Click" 
                        Background="#FF9800" Foreground="White" BorderThickness="0"
                        Padding="15,8" Margin="10,0" FontWeight="Bold"/>
                <Button Content="💾 حفظ العرض" Click="SaveQuotation_Click" 
                        Background="#2196F3" Foreground="White" BorderThickness="0"
                        Padding="15,8" Margin="10,0" FontWeight="Bold"/>
                <Button Content="❌ إغلاق" Click="Close_Click" 
                        Background="#F44336" Foreground="White" BorderThickness="0"
                        Padding="15,8" Margin="10,0" FontWeight="Bold"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
