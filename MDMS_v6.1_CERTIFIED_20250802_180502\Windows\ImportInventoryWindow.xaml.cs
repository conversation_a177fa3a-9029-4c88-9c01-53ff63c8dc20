using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using System.Text;
using System.Text.RegularExpressions;
using ClosedXML.Excel;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser;
using iText.Kernel.Pdf.Canvas.Parser.Listener;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace MedicalDevicesManager.Windows
{
    public partial class ImportInventoryWindow : Window
    {
        private string _selectedFilePath = "";
        private List<ImportItemData> _previewData;
        private bool _isImporting = false;

        public ImportInventoryWindow()
        {
            InitializeComponent();
            _previewData = new List<ImportItemData>();
            LogMessage("نظام الاستيراد جاهز للاستخدام");
        }

        private void BrowseFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Title = "اختيار ملف للاستيراد",
                    Filter = "جميع الملفات المدعومة|*.xlsx;*.xls;*.pdf;*.docx;*.doc|" +
                            "ملفات Excel|*.xlsx;*.xls|" +
                            "ملفات PDF|*.pdf|" +
                            "ملفات Word|*.docx;*.doc|" +
                            "جميع الملفات|*.*",
                    FilterIndex = 1
                };

                if (openDialog.ShowDialog() == true)
                {
                    _selectedFilePath = openDialog.FileName;
                    FilePathTextBox.Text = _selectedFilePath;
                    
                    var fileInfo = new FileInfo(_selectedFilePath);
                    var fileExtension = fileInfo.Extension.ToLower();
                    var fileSize = FormatFileSize(fileInfo.Length);
                    
                    FileTypeText.Text = $"📄 نوع الملف: {GetFileTypeDescription(fileExtension)}";
                    FileSizeText.Text = $"📏 حجم الملف: {fileSize}";
                    
                    PreviewButton.IsEnabled = true;
                    ImportButton.IsEnabled = false;
                    
                    LogMessage($"تم اختيار الملف: {fileInfo.Name} ({fileSize})");
                    StatusText.Text = "تم اختيار الملف - اضغط معاينة للمتابعة";
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في اختيار الملف: {ex.Message}", "ERROR");
                MessageBox.Show($"خطأ في اختيار الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_selectedFilePath) || !File.Exists(_selectedFilePath))
            {
                MessageBox.Show("يرجى اختيار ملف صحيح أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                StatusText.Text = "جاري تحليل الملف...";
                PreviewButton.IsEnabled = false;
                
                await Task.Run(() => ProcessFile(_selectedFilePath));
                
                UpdatePreviewDisplay();
                ImportButton.IsEnabled = _previewData.Any(d => d.Status == "✅ صحيح");
                
                StatusText.Text = $"تم تحليل الملف - {_previewData.Count} عنصر";
                LogMessage($"تم تحليل الملف بنجاح - العناصر المكتشفة: {_previewData.Count}");
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في تحليل الملف: {ex.Message}", "ERROR");
                MessageBox.Show($"خطأ في تحليل الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في تحليل الملف";
            }
            finally
            {
                PreviewButton.IsEnabled = true;
            }
        }

        private void ProcessFile(string filePath)
        {
            var fileExtension = Path.GetExtension(filePath).ToLower();
            
            Dispatcher.Invoke(() => LogMessage($"بدء تحليل الملف: {Path.GetFileName(filePath)}"));
            
            switch (fileExtension)
            {
                case ".xlsx":
                case ".xls":
                    ProcessExcelFile(filePath);
                    break;
                case ".pdf":
                    ProcessPdfFile(filePath);
                    break;
                case ".docx":
                case ".doc":
                    ProcessWordFile(filePath);
                    break;
                default:
                    throw new NotSupportedException($"نوع الملف غير مدعوم: {fileExtension}");
            }
        }

        private void ProcessExcelFile(string filePath)
        {
            try
            {
                _previewData.Clear();
                
                using (var workbook = new XLWorkbook(filePath))
                {
                    var worksheet = workbook.Worksheet(1);
                    var rows = worksheet.RowsUsed().Skip(1); // تخطي رأس الجدول
                    
                    Dispatcher.Invoke(() => LogMessage($"معالجة ملف Excel - الصفوف: {rows.Count()}"));
                    
                    foreach (var row in rows)
                    {
                        try
                        {
                            var item = new ImportItemData
                            {
                                Name = row.Cell(1).GetString().Trim(),
                                Category = row.Cell(2).GetString().Trim(),
                                Description = row.Cell(3).GetString().Trim(),
                                Unit = row.Cell(6).GetString().Trim(),
                                Brand = row.Cell(7).GetString().Trim(),
                                Model = row.Cell(8).GetString().Trim()
                            };
                            
                            // محاولة تحويل السعر
                            if (decimal.TryParse(row.Cell(4).GetString(), out decimal price))
                                item.Price = price;
                            
                            // محاولة تحويل الكمية
                            if (int.TryParse(row.Cell(5).GetString(), out int quantity))
                                item.Quantity = quantity;
                            
                            ValidateItem(item);
                            _previewData.Add(item);
                        }
                        catch (Exception ex)
                        {
                            var errorItem = new ImportItemData
                            {
                                Name = $"خطأ في الصف {row.RowNumber()}",
                                Status = "❌ خطأ",
                                Notes = ex.Message
                            };
                            _previewData.Add(errorItem);
                        }
                    }
                }
                
                Dispatcher.Invoke(() => LogMessage($"تم معالجة ملف Excel - العناصر: {_previewData.Count}"));
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => LogMessage($"خطأ في معالجة ملف Excel: {ex.Message}", "ERROR"));
                throw;
            }
        }

        private void ProcessPdfFile(string filePath)
        {
            try
            {
                _previewData.Clear();
                
                using (var pdfReader = new PdfReader(filePath))
                using (var pdfDocument = new PdfDocument(pdfReader))
                {
                    var strategy = new SimpleTextExtractionStrategy();
                    var text = "";
                    
                    for (int i = 1; i <= pdfDocument.GetNumberOfPages(); i++)
                    {
                        text += PdfTextExtractor.GetTextFromPage(pdfDocument.GetPage(i), strategy);
                    }
                    
                    Dispatcher.Invoke(() => LogMessage($"استخراج النص من PDF - الأحرف: {text.Length}"));
                    
                    ParseTextData(text, "PDF");
                }
                
                Dispatcher.Invoke(() => LogMessage($"تم معالجة ملف PDF - العناصر: {_previewData.Count}"));
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => LogMessage($"خطأ في معالجة ملف PDF: {ex.Message}", "ERROR"));
                throw;
            }
        }

        private void ProcessWordFile(string filePath)
        {
            try
            {
                _previewData.Clear();
                
                using (var document = WordprocessingDocument.Open(filePath, false))
                {
                    var body = document.MainDocumentPart.Document.Body;
                    var text = body.InnerText;
                    
                    Dispatcher.Invoke(() => LogMessage($"استخراج النص من Word - الأحرف: {text.Length}"));
                    
                    // محاولة استخراج الجداول أولاً
                    var tables = body.Elements<DocumentFormat.OpenXml.Wordprocessing.Table>();
                    if (tables.Any())
                    {
                        ProcessWordTables(tables);
                    }
                    else
                    {
                        ParseTextData(text, "Word");
                    }
                }
                
                Dispatcher.Invoke(() => LogMessage($"تم معالجة ملف Word - العناصر: {_previewData.Count}"));
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => LogMessage($"خطأ في معالجة ملف Word: {ex.Message}", "ERROR"));
                throw;
            }
        }

        private void ProcessWordTables(IEnumerable<DocumentFormat.OpenXml.Wordprocessing.Table> tables)
        {
            foreach (var table in tables)
            {
                var rows = table.Elements<TableRow>().Skip(1); // تخطي رأس الجدول
                
                foreach (var row in rows)
                {
                    try
                    {
                        var cells = row.Elements<TableCell>().ToList();
                        if (cells.Count >= 2)
                        {
                            var item = new ImportItemData
                            {
                                Name = cells.ElementAtOrDefault(0)?.InnerText?.Trim() ?? "",
                                Category = cells.ElementAtOrDefault(1)?.InnerText?.Trim() ?? "",
                                Description = cells.ElementAtOrDefault(2)?.InnerText?.Trim() ?? "",
                                Unit = cells.ElementAtOrDefault(5)?.InnerText?.Trim() ?? "",
                                Brand = cells.ElementAtOrDefault(6)?.InnerText?.Trim() ?? "",
                                Model = cells.ElementAtOrDefault(7)?.InnerText?.Trim() ?? ""
                            };
                            
                            // محاولة تحويل السعر
                            if (cells.Count > 3 && decimal.TryParse(ExtractNumbers(cells[3].InnerText), out decimal price))
                                item.Price = price;
                            
                            // محاولة تحويل الكمية
                            if (cells.Count > 4 && int.TryParse(ExtractNumbers(cells[4].InnerText), out int quantity))
                                item.Quantity = quantity;
                            
                            ValidateItem(item);
                            _previewData.Add(item);
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorItem = new ImportItemData
                        {
                            Name = "خطأ في صف الجدول",
                            Status = "❌ خطأ",
                            Notes = ex.Message
                        };
                        _previewData.Add(errorItem);
                    }
                }
            }
        }

        private void ParseTextData(string text, string source)
        {
            var lines = text.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var line in lines)
            {
                if (string.IsNullOrWhiteSpace(line) || line.Length < 10)
                    continue;
                
                try
                {
                    // محاولة تحليل السطر كعنصر
                    var parts = line.Split(new[] { '\t', '|', ';', ',' }, StringSplitOptions.RemoveEmptyEntries);
                    
                    if (parts.Length >= 2)
                    {
                        var item = new ImportItemData
                        {
                            Name = parts[0].Trim(),
                            Category = parts.Length > 1 ? parts[1].Trim() : "غير محدد",
                            Description = parts.Length > 2 ? parts[2].Trim() : "",
                            Unit = parts.Length > 5 ? parts[5].Trim() : "قطعة"
                        };
                        
                        // محاولة استخراج السعر من النص
                        var priceMatch = Regex.Match(line, @"(\d+(?:\.\d+)?)\s*ريال");
                        if (priceMatch.Success && decimal.TryParse(priceMatch.Groups[1].Value, out decimal price))
                            item.Price = price;
                        
                        // محاولة استخراج الكمية
                        var quantityMatch = Regex.Match(line, @"كمية\s*:?\s*(\d+)|(\d+)\s*قطعة");
                        if (quantityMatch.Success)
                        {
                            var qtyStr = quantityMatch.Groups[1].Success ? quantityMatch.Groups[1].Value : quantityMatch.Groups[2].Value;
                            if (int.TryParse(qtyStr, out int quantity))
                                item.Quantity = quantity;
                        }
                        
                        ValidateItem(item);
                        _previewData.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    // تجاهل الأسطر التي لا يمكن تحليلها
                    Dispatcher.Invoke(() => LogMessage($"تخطي سطر غير قابل للتحليل من {source}: {ex.Message}", "WARNING"));
                }
            }
        }

        private void ValidateItem(ImportItemData item)
        {
            var errors = new List<string>();
            
            if (string.IsNullOrWhiteSpace(item.Name))
                errors.Add("الاسم مطلوب");
            
            if (string.IsNullOrWhiteSpace(item.Category))
                errors.Add("الفئة مطلوبة");
            
            if (item.Price <= 0)
                errors.Add("السعر غير صحيح");
            
            if (item.Quantity <= 0)
                item.Quantity = 1; // قيمة افتراضية
            
            if (errors.Any())
            {
                item.Status = "⚠️ تحذير";
                item.Notes = string.Join(", ", errors);
            }
            else
            {
                item.Status = "✅ صحيح";
                item.Notes = "جاهز للاستيراد";
            }
        }

        private void UpdatePreviewDisplay()
        {
            Dispatcher.Invoke(() =>
            {
                PreviewDataGrid.ItemsSource = _previewData;
                
                var totalRows = _previewData.Count;
                var validRows = _previewData.Count(d => d.Status == "✅ صحيح");
                var errorRows = _previewData.Count(d => d.Status == "❌ خطأ");
                var warningRows = _previewData.Count(d => d.Status == "⚠️ تحذير");
                
                TotalRowsText.Text = $"📊 إجمالي الصفوف: {totalRows}";
                ValidRowsText.Text = $"✅ صفوف صحيحة: {validRows}";
                ErrorRowsText.Text = $"❌ صفوف خاطئة: {errorRows}";
                DuplicateRowsText.Text = $"⚠️ تحذيرات: {warningRows}";
                
                var estimatedTime = Math.Max(1, validRows / 10); // تقدير 10 عناصر في الثانية
                EstimatedTimeText.Text = $"⏱️ الوقت المتوقع: {estimatedTime} ثانية";
                LastUpdateText.Text = $"🕒 آخر تحديث: {DateTime.Now:HH:mm:ss}";
            });
        }

        private string GetFileTypeDescription(string extension)
        {
            return extension switch
            {
                ".xlsx" or ".xls" => "Excel Spreadsheet",
                ".pdf" => "PDF Document",
                ".docx" or ".doc" => "Word Document",
                _ => "Unknown"
            };
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private string ExtractNumbers(string text)
        {
            return Regex.Replace(text, @"[^\d.]", "");
        }

        private void LogMessage(string message, string level = "INFO")
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                var logEntry = $"[{timestamp}] [{level}] {message}\n";
                LogTextBox.AppendText(logEntry);
                
                if (AutoScrollCheckBox.IsChecked == true)
                {
                    LogScrollViewer.ScrollToEnd();
                }
            });
        }

        private void ClearLogButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Clear();
            LogMessage("تم مسح السجل");
        }

        private void SaveLogButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Text Files (*.txt)|*.txt",
                    FileName = $"import_log_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    File.WriteAllText(saveDialog.FileName, LogTextBox.Text, Encoding.UTF8);
                    MessageBox.Show($"تم حفظ السجل في:\n{saveDialog.FileName}", "حفظ السجل", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السجل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isImporting)
                return;

            var validItems = _previewData.Where(d => d.Status == "✅ صحيح").ToList();
            if (!validItems.Any())
            {
                MessageBox.Show("لا توجد عناصر صحيحة للاستيراد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل تريد استيراد {validItems.Count} عنصر إلى المخزون؟\n\n" +
                "تأكد من مراجعة البيانات قبل المتابعة.",
                "تأكيد الاستيراد",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes)
                return;

            try
            {
                _isImporting = true;
                ImportButton.IsEnabled = false;
                ImportProgressBar.Visibility = Visibility.Visible;
                ImportProgressBar.Maximum = validItems.Count;
                ImportProgressBar.Value = 0;

                StatusText.Text = "جاري الاستيراد...";
                LogMessage($"بدء استيراد {validItems.Count} عنصر");

                await ImportItemsToDatabase(validItems);

                MessageBox.Show(
                    $"تم استيراد {validItems.Count} عنصر بنجاح!",
                    "اكتمال الاستيراد",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                StatusText.Text = "تم الاستيراد بنجاح";
                LogMessage("اكتمل الاستيراد بنجاح");
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في الاستيراد: {ex.Message}", "ERROR");
                MessageBox.Show($"خطأ في الاستيراد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في الاستيراد";
            }
            finally
            {
                _isImporting = false;
                ImportButton.IsEnabled = true;
                ImportProgressBar.Visibility = Visibility.Collapsed;
                ProgressText.Text = "";
            }
        }

        private async Task ImportItemsToDatabase(List<ImportItemData> items)
        {
            var importMode = ImportModeComboBox.SelectedIndex;
            var overwriteExisting = OverwriteExistingCheckBox.IsChecked == true;
            
            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                
                try
                {
                    if (importMode == 0 || importMode == 2) // أجهزة طبية أو تلقائي
                    {
                        await ImportAsMedicalDevice(item, overwriteExisting);
                    }
                    
                    if (importMode == 1 || importMode == 2) // مخزون أو تلقائي
                    {
                        await ImportAsInventoryItem(item, overwriteExisting);
                    }
                    
                    Dispatcher.Invoke(() =>
                    {
                        ImportProgressBar.Value = i + 1;
                        ProgressText.Text = $"{i + 1}/{items.Count}";
                    });
                    
                    LogMessage($"تم استيراد: {item.Name}");
                }
                catch (Exception ex)
                {
                    LogMessage($"خطأ في استيراد {item.Name}: {ex.Message}", "ERROR");
                }
            }
            
            await App.DatabaseContext.SaveChangesAsync();
        }

        private async Task ImportAsMedicalDevice(ImportItemData item, bool overwriteExisting)
        {
            var existing = await App.DatabaseContext.MedicalDevices
                .FirstOrDefaultAsync(d => d.Name == item.Name);
            
            if (existing != null && !overwriteExisting)
                return;
            
            if (existing == null)
            {
                existing = new MedicalDevice();
                App.DatabaseContext.MedicalDevices.Add(existing);
            }
            
            existing.Name = item.Name;
            existing.Category = item.Category;
            existing.Description = item.Description;
            existing.Brand = item.Brand ?? "غير محدد";
            existing.Model = item.Model ?? "غير محدد";
            existing.SellingPrice = item.Price;
            existing.Status = "متاح";
            existing.LastUpdated = DateTime.Now;
        }

        private async Task ImportAsInventoryItem(ImportItemData item, bool overwriteExisting)
        {
            var existing = await App.DatabaseContext.InventoryItems
                .FirstOrDefaultAsync(i => i.Name == item.Name);
            
            if (existing != null && !overwriteExisting)
                return;
            
            if (existing == null)
            {
                existing = new InventoryItem();
                App.DatabaseContext.InventoryItems.Add(existing);
            }
            
            existing.Name = item.Name;
            existing.Category = item.Category;
            existing.Description = item.Description;
            existing.UnitPrice = item.Price;
            existing.CurrentStock = item.Quantity;
            existing.Unit = item.Unit ?? "قطعة";
            existing.Status = "متاح";
            existing.LastUpdated = DateTime.Now;
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            if (_isImporting)
            {
                var result = MessageBox.Show(
                    "عملية الاستيراد جارية. هل تريد إلغاءها والإغلاق؟",
                    "تأكيد الإغلاق",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                if (result != MessageBoxResult.Yes)
                    return;
            }
            
            this.Close();
        }
    }

    // فئة بيانات العنصر المستورد
    public class ImportItemData
    {
        public string Status { get; set; } = "⏳ معلق";
        public string Name { get; set; } = "";
        public string Category { get; set; } = "";
        public string Description { get; set; } = "";
        public decimal Price { get; set; }
        public int Quantity { get; set; } = 1;
        public string Unit { get; set; } = "قطعة";
        public string Brand { get; set; } = "";
        public string Model { get; set; } = "";
        public string Notes { get; set; } = "";
        public string PriceFormatted => $"{Price:N0} ريال";
    }
}
