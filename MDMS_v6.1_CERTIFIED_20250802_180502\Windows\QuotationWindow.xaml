<Window x:Class="MedicalDevicesManager.Windows.QuotationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="عروض الأسعار - أجهزة تخطيط القلب 12 قناة" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        
        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style TargetType="Border" x:Key="CardStyle">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel>
                <TextBlock Text="🏥 عروض الأسعار المتاحة" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="أجهزة تخطيط القلب 12 قناة - عدد 3 أجهزة" 
                          FontSize="16" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                
                <!-- العرض الأول -->
                <Border Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="📊 العرض الأول - شيلر AT-102" 
                                      FontSize="18" FontWeight="Bold" 
                                      Foreground="#1976D2" Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="🏷️ المواصفات:" FontWeight="Bold" Foreground="#333"/>
                                    <TextBlock Text="• الماركة: Schiller" Margin="10,0,0,0"/>
                                    <TextBlock Text="• الموديل: AT-102" Margin="10,0,0,0"/>
                                    <TextBlock Text="• 12 قناة رقمية" Margin="10,0,0,0"/>
                                    <TextBlock Text="• شاشة ملونة 7 بوصة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• طباعة حرارية عالية الجودة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• ذاكرة تخزين 1000 تخطيط" Margin="10,0,0,0"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="💰 التفاصيل المالية:" FontWeight="Bold" Foreground="#333"/>
                                    <TextBlock Text="• سعر الوحدة: 30,000 ريال" Margin="10,0,0,0"/>
                                    <TextBlock Text="• العدد المطلوب: 3 أجهزة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• المجموع الفرعي: 90,000 ريال" Margin="10,0,0,0"/>
                                    <TextBlock Text="• خصم الكمية (5%): -4,500 ريال" Margin="10,0,0,0" Foreground="Green"/>
                                    <TextBlock Text="• المجموع النهائي: 85,500 ريال" Margin="10,0,0,0" FontWeight="Bold" FontSize="16" Foreground="#1976D2"/>
                                </StackPanel>
                            </Grid>
                            
                            <StackPanel Margin="0,10,0,0">
                                <TextBlock Text="🎁 المزايا الإضافية:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• ضمان شامل لمدة سنتين" Margin="10,0,0,0"/>
                                <TextBlock Text="• تدريب مجاني للفريق الطبي" Margin="10,0,0,0"/>
                                <TextBlock Text="• صيانة مجانية لمدة 6 أشهر" Margin="10,0,0,0"/>
                                <TextBlock Text="• توصيل وتركيب مجاني" Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="✅ متوفر" FontSize="16" FontWeight="Bold" 
                                      Foreground="Green" HorizontalAlignment="Center"/>
                            <TextBlock Text="التسليم خلال 7 أيام" FontSize="12" 
                                      Foreground="#666" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- العرض الثاني -->
                <Border Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="📊 العرض الثاني - فيليبس PageWriter TC70" 
                                      FontSize="18" FontWeight="Bold" 
                                      Foreground="#1976D2" Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="🏷️ المواصفات:" FontWeight="Bold" Foreground="#333"/>
                                    <TextBlock Text="• الماركة: Philips" Margin="10,0,0,0"/>
                                    <TextBlock Text="• الموديل: PageWriter TC70" Margin="10,0,0,0"/>
                                    <TextBlock Text="• 12 قناة رقمية متقدمة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• شاشة لمس 10 بوصة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• اتصال لاسلكي WiFi" Margin="10,0,0,0"/>
                                    <TextBlock Text="• تحليل تلقائي للنتائج" Margin="10,0,0,0"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="💰 التفاصيل المالية:" FontWeight="Bold" Foreground="#333"/>
                                    <TextBlock Text="• سعر الوحدة: 45,000 ريال" Margin="10,0,0,0"/>
                                    <TextBlock Text="• العدد المطلوب: 3 أجهزة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• المجموع الفرعي: 135,000 ريال" Margin="10,0,0,0"/>
                                    <TextBlock Text="• خصم الكمية (8%): -10,800 ريال" Margin="10,0,0,0" Foreground="Green"/>
                                    <TextBlock Text="• المجموع النهائي: 124,200 ريال" Margin="10,0,0,0" FontWeight="Bold" FontSize="16" Foreground="#1976D2"/>
                                </StackPanel>
                            </Grid>
                            
                            <StackPanel Margin="0,10,0,0">
                                <TextBlock Text="🎁 المزايا الإضافية:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• ضمان شامل لمدة 3 سنوات" Margin="10,0,0,0"/>
                                <TextBlock Text="• تدريب متقدم للفريق الطبي" Margin="10,0,0,0"/>
                                <TextBlock Text="• صيانة مجانية لمدة سنة كاملة" Margin="10,0,0,0"/>
                                <TextBlock Text="• دعم فني 24/7" Margin="10,0,0,0"/>
                                <TextBlock Text="• برنامج إدارة البيانات مجاني" Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="✅ متوفر" FontSize="16" FontWeight="Bold" 
                                      Foreground="Green" HorizontalAlignment="Center"/>
                            <TextBlock Text="التسليم خلال 10 أيام" FontSize="12" 
                                      Foreground="#666" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- العرض الثالث -->
                <Border Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="📊 العرض الثالث - جنرال إلكتريك MAC 2000" 
                                      FontSize="18" FontWeight="Bold" 
                                      Foreground="#1976D2" Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="🏷️ المواصفات:" FontWeight="Bold" Foreground="#333"/>
                                    <TextBlock Text="• الماركة: GE Healthcare" Margin="10,0,0,0"/>
                                    <TextBlock Text="• الموديل: MAC 2000" Margin="10,0,0,0"/>
                                    <TextBlock Text="• 12 قناة عالية الدقة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• شاشة ملونة 8 بوصة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• طباعة ليزر عالية الجودة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• ذاكرة تخزين 2000 تخطيط" Margin="10,0,0,0"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="💰 التفاصيل المالية:" FontWeight="Bold" Foreground="#333"/>
                                    <TextBlock Text="• سعر الوحدة: 38,000 ريال" Margin="10,0,0,0"/>
                                    <TextBlock Text="• العدد المطلوب: 3 أجهزة" Margin="10,0,0,0"/>
                                    <TextBlock Text="• المجموع الفرعي: 114,000 ريال" Margin="10,0,0,0"/>
                                    <TextBlock Text="• خصم الكمية (7%): -7,980 ريال" Margin="10,0,0,0" Foreground="Green"/>
                                    <TextBlock Text="• المجموع النهائي: 106,020 ريال" Margin="10,0,0,0" FontWeight="Bold" FontSize="16" Foreground="#1976D2"/>
                                </StackPanel>
                            </Grid>
                            
                            <StackPanel Margin="0,10,0,0">
                                <TextBlock Text="🎁 المزايا الإضافية:" FontWeight="Bold" Foreground="#333"/>
                                <TextBlock Text="• ضمان شامل لمدة سنتين ونصف" Margin="10,0,0,0"/>
                                <TextBlock Text="• تدريب شامل للفريق الطبي" Margin="10,0,0,0"/>
                                <TextBlock Text="• صيانة مجانية لمدة 8 أشهر" Margin="10,0,0,0"/>
                                <TextBlock Text="• توصيل وتركيب مجاني" Margin="10,0,0,0"/>
                                <TextBlock Text="• قطع غيار مجانية لسنة" Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="⏳ طلب مسبق" FontSize="16" FontWeight="Bold" 
                                      Foreground="Orange" HorizontalAlignment="Center"/>
                            <TextBlock Text="التسليم خلال 14 يوم" FontSize="12" 
                                      Foreground="#666" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- ملخص المقارنة -->
                <Border Style="{StaticResource CardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Text="📋 ملخص المقارنة والتوصية" 
                                  FontSize="18" FontWeight="Bold" 
                                  Foreground="#1976D2" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="🥉 الأفضل اقتصادياً" FontWeight="Bold" Foreground="#FF9800" HorizontalAlignment="Center"/>
                                <TextBlock Text="شيلر AT-102" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="85,500 ريال" FontSize="16" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="🥇 الأفضل تقنياً" FontWeight="Bold" Foreground="#4CAF50" HorizontalAlignment="Center"/>
                                <TextBlock Text="فيليبس PageWriter TC70" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="124,200 ريال" FontSize="16" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="🥈 الأفضل توازناً" FontWeight="Bold" Foreground="#2196F3" HorizontalAlignment="Center"/>
                                <TextBlock Text="جنرال إلكتريك MAC 2000" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="106,020 ريال" FontSize="16" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#1976D2" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="📄 طباعة العرض" Click="PrintQuotation_Click" Margin="10,0"/>
                <Button Content="📧 إرسال بالبريد" Click="EmailQuotation_Click" Margin="10,0"/>
                <Button Content="💾 حفظ العرض" Click="SaveQuotation_Click" Margin="10,0"/>
                <Button Content="❌ إغلاق" Click="Close_Click" Background="#F44336" Margin="10,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
