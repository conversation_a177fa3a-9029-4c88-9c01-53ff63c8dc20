@echo off
chcp 65001 >nul 2>&1
cls

echo ========================================
echo    نظام إدارة الأجهزة الطبية المتكامل
echo    Medical Devices Management System
echo ========================================
echo.
echo 🚀 بدء تشغيل النظام مع التحسينات العربية الجديدة...
echo Starting system with new Arabic improvements...
echo.

REM التحقق من وجود .NET
where dotnet >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET غير مثبت على النظام
    echo Please install .NET 6.0 or later
    pause
    exit /b 1
)

REM التحقق من وجود ملف المشروع
if not exist "MedicalDevicesManager.csproj" (
    echo ❌ ملف المشروع غير موجود
    echo Project file not found
    pause
    exit /b 1
)

echo ✅ جاري بناء المشروع...
echo Building project...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo Build failed
    pause
    exit /b 1
)

echo ✅ تم البناء بنجاح
echo Build successful
echo.
echo 🎨 التحسينات الجديدة المطبقة:
echo New improvements applied:
echo   • واجهة عربية كاملة مع اتجاه RTL
echo   • خطوط عربية محسنة
echo   • تخطيط محسن للنماذج والجداول
echo   • أزرار وقوائم منسدلة محسنة
echo.
echo 🚀 تشغيل النظام...
echo Starting system...
echo.

start "" dotnet run --configuration Release

echo ✅ تم تشغيل النظام بنجاح!
echo System started successfully!
echo.
echo 💡 نصائح للاستخدام:
echo Usage tips:
echo   • استخدم زر "تشخيص النظام" للتحقق من البيانات
echo   • جرب النوافذ المختلفة لرؤية التحسينات العربية
echo   • استخدم اختصارات لوحة المفاتيح للتنقل السريع
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
