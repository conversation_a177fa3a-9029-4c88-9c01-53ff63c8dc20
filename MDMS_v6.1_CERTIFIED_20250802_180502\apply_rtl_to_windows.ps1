# سكريبت لتطبيق FlowDirection="RightToLeft" على جميع نوافذ XAML
# Script to apply FlowDirection="RightToLeft" to all XAML windows

Write-Host "🔄 بدء تطبيق التحسينات العربية على النوافذ..." -ForegroundColor Green
Write-Host "Starting Arabic improvements on windows..." -ForegroundColor Green

# قائمة النوافذ التي تحتاج إلى تحسين
$windowFiles = @(
    "Windows\AddEditInventoryWindow.xaml",
    "Windows\AddEditMaintenanceWindow.xaml", 
    "Windows\AddEditShipmentWindow.xaml",
    "Windows\AddEditSparePartWindow.xaml",
    "Windows\AddEditSupplierWindow.xaml",
    "Windows\AddEditInstallationWindow.xaml",
    "Windows\BackupWindow.xaml",
    "Windows\CreateBackupWindow.xaml",
    "Windows\DeviceDetailsWindow.xaml",
    "Windows\DeviceSerialNumbersWindow.xaml",
    "Windows\ExportWindow.xaml",
    "Windows\InventoryItemReportWindow.xaml",
    "Windows\InventorySelectionWindow.xaml",
    "Windows\MaintenanceDetailsWindow.xaml",
    "Windows\ManageCategoriesWindow.xaml",
    "Windows\ManageCitiesWindow.xaml",
    "Windows\ManageCustomerTypesWindow.xaml",
    "Windows\ManageInventoryCategoriesWindow.xaml",
    "Windows\ManageRecipientsWindow.xaml",
    "Windows\ManageSerialNumbersWindow.xaml",
    "Windows\ManageTechniciansWindow.xaml",
    "Windows\NotificationsWindow.xaml",
    "Windows\ReportsWindow.xaml",
    "Windows\SelectDeviceWindow.xaml",
    "Windows\SmartSystemWindow.xaml",
    "Windows\AdvancedInventorySettingsWindow.xaml"
)

$processedCount = 0
$errorCount = 0

foreach ($file in $windowFiles) {
    try {
        if (Test-Path $file) {
            Write-Host "⚡ معالجة: $file" -ForegroundColor Yellow
            
            # قراءة محتوى الملف
            $content = Get-Content $file -Raw -Encoding UTF8
            
            # التحقق من وجود FlowDirection مسبقاً
            if ($content -match 'FlowDirection="RightToLeft"') {
                Write-Host "✅ الملف يحتوي بالفعل على FlowDirection" -ForegroundColor Green
                continue
            }
            
            # البحث عن نمط Window tag وإضافة FlowDirection
            if ($content -match '(<Window[^>]*?)(\s*>)') {
                $newContent = $content -replace '(<Window[^>]*?)(\s*>)', '$1 FlowDirection="RightToLeft"$2'
                
                # حفظ الملف المحدث
                Set-Content $file -Value $newContent -Encoding UTF8
                Write-Host "✅ تم تطبيق FlowDirection بنجاح" -ForegroundColor Green
                $processedCount++
            } else {
                Write-Host "⚠️ لم يتم العثور على Window tag" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ الملف غير موجود: $file" -ForegroundColor Red
            $errorCount++
        }
    }
    catch {
        Write-Host "❌ خطأ في معالجة $file : $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host ""
Write-Host "📊 تقرير النتائج:" -ForegroundColor Cyan
Write-Host "✅ تم معالجة: $processedCount ملف" -ForegroundColor Green
Write-Host "❌ أخطاء: $errorCount ملف" -ForegroundColor Red
Write-Host ""
Write-Host "🎉 اكتمل تطبيق التحسينات العربية!" -ForegroundColor Green
