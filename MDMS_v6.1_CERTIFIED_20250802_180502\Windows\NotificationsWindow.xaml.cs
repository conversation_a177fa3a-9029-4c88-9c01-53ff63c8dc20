using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using MedicalDevicesManager.Services;

namespace MedicalDevicesManager.Windows
{
    public partial class NotificationsWindow : Window
    {
        private NotificationService _notificationService;
        private List<Notification> _allNotifications;
        
        public NotificationsWindow()
        {
            InitializeComponent();
            _notificationService = new NotificationService(App.DatabaseContext);
            LoadNotificationsAsync();
        }
        
        private async void LoadNotificationsAsync()
        {
            try
            {
                _allNotifications = await _notificationService.GetActiveNotificationsAsync();
                NotificationsDataGrid.ItemsSource = _allNotifications;
                
                // تحديث عداد الإشعارات غير المقروءة
                var unreadCount = await _notificationService.GetUnreadCountAsync();
                UnreadCountTextBlock.Text = $"الإشعارات غير المقروءة: {unreadCount}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإشعارات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadNotificationsAsync();
        }
        
        private async void MarkAllReadBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد تحديد جميع الإشعارات كمقروءة؟",
                    "تأكيد",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    await _notificationService.MarkAllAsReadAsync();
                    LoadNotificationsAsync();
                    MessageBox.Show("تم تحديد جميع الإشعارات كمقروءة", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد الإشعارات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void CheckSystemBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CheckSystemBtn.IsEnabled = false;
                CheckSystemBtn.Content = "🔄 جاري الفحص...";
                
                // فحص المخزون المنخفض
                await _notificationService.CheckLowStockAsync();
                
                // فحص انتهاء الصلاحية
                await _notificationService.CheckExpiryDatesAsync();
                
                // فحص الصيانة المستحقة
                await _notificationService.CheckMaintenanceDueAsync();
                
                CheckSystemBtn.Content = "🔍 فحص النظام";
                CheckSystemBtn.IsEnabled = true;
                
                LoadNotificationsAsync();
                MessageBox.Show("تم فحص النظام وإنشاء التنبيهات اللازمة", "اكتمل الفحص", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                CheckSystemBtn.Content = "🔍 فحص النظام";
                CheckSystemBtn.IsEnabled = true;
                MessageBox.Show($"خطأ في فحص النظام: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications == null) return;
            
            var selectedFilter = (FilterComboBox.SelectedItem as ComboBoxItem)?.Content.ToString();
            List<Notification> filteredNotifications;
            
            switch (selectedFilter)
            {
                case "غير مقروءة فقط":
                    filteredNotifications = _allNotifications.Where(n => !n.IsRead).ToList();
                    break;
                case "تنبيهات المخزون":
                    filteredNotifications = _allNotifications.Where(n => n.Category == "LowStock").ToList();
                    break;
                case "تنبيهات الصيانة":
                    filteredNotifications = _allNotifications.Where(n => n.Category == "Maintenance").ToList();
                    break;
                case "تنبيهات انتهاء الصلاحية":
                    filteredNotifications = _allNotifications.Where(n => n.Category == "Expiry").ToList();
                    break;
                case "النسخ الاحتياطي":
                    filteredNotifications = _allNotifications.Where(n => n.Category == "Backup").ToList();
                    break;
                case "التصدير":
                    filteredNotifications = _allNotifications.Where(n => n.Category == "Export").ToList();
                    break;
                default:
                    filteredNotifications = _allNotifications;
                    break;
            }
            
            NotificationsDataGrid.ItemsSource = filteredNotifications;
        }
        
        private void NotificationsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (NotificationsDataGrid.SelectedItem is Notification selectedNotification)
            {
                NotificationDetailsPanel.Visibility = Visibility.Visible;
                
                var details = $"العنوان: {selectedNotification.Title}\n" +
                             $"الرسالة: {selectedNotification.Message}\n" +
                             $"النوع: {selectedNotification.Type}\n" +
                             $"الفئة: {selectedNotification.Category}\n" +
                             $"الأولوية: {selectedNotification.Priority}\n" +
                             $"التاريخ: {selectedNotification.CreatedDate:dd/MM/yyyy HH:mm}\n" +
                             $"مقروء: {(selectedNotification.IsRead ? "نعم" : "لا")}";
                
                if (selectedNotification.ReadDate.HasValue)
                {
                    details += $"\nتاريخ القراءة: {selectedNotification.ReadDate.Value:dd/MM/yyyy HH:mm}";
                }
                
                NotificationDetailsTextBlock.Text = details;
                
                // إظهار زر العرض إذا كان هناك عنصر مرتبط
                ViewRelatedItemBtn.Visibility = selectedNotification.RelatedItemId.HasValue ? 
                    Visibility.Visible : Visibility.Collapsed;
            }
            else
            {
                NotificationDetailsPanel.Visibility = Visibility.Collapsed;
            }
        }
        
        private async void MarkReadBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var notification = button?.DataContext as Notification;
            
            if (notification != null && !notification.IsRead)
            {
                try
                {
                    await _notificationService.MarkAsReadAsync(notification.Id);
                    LoadNotificationsAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديد الإشعار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private async void DeleteNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var notification = button?.DataContext as Notification;
            
            if (notification != null)
            {
                var result = MessageBox.Show(
                    $"هل تريد حذف الإشعار '{notification.Title}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _notificationService.DeleteNotificationAsync(notification.Id);
                        LoadNotificationsAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الإشعار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private void ViewRelatedItemBtn_Click(object sender, RoutedEventArgs e)
        {
            if (NotificationsDataGrid.SelectedItem is Notification selectedNotification && 
                selectedNotification.RelatedItemId.HasValue)
            {
                try
                {
                    // فتح النافذة المناسبة حسب نوع العنصر المرتبط
                    switch (selectedNotification.RelatedItemType)
                    {
                        case "InventoryItem":
                            // فتح نافذة المخزون
                            MessageBox.Show("سيتم فتح نافذة المخزون", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                            break;

                        case "MedicalDevice":
                            // فتح نافذة الأجهزة الطبية
                            MessageBox.Show("سيتم فتح نافذة الأجهزة الطبية", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                            break;
                            
                        case "BackupRecord":
                            // فتح نافذة النسخ الاحتياطي
                            var backupWindow = new BackupWindow();
                            backupWindow.Show();
                            break;
                            
                        case "ExportRecord":
                            // فتح نافذة التصدير
                            var exportWindow = new ExportWindow();
                            exportWindow.Show();
                            break;
                            
                        default:
                            MessageBox.Show("نوع العنصر المرتبط غير مدعوم", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح العنصر المرتبط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }
}
