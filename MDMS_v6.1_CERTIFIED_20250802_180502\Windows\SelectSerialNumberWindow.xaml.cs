using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class SelectSerialNumberWindow : Window
    {
        private MedicalDevice _device;
        private List<DeviceSerialNumber> _serialNumbers;
        public DeviceSerialNumber SelectedSerialNumber { get; private set; }
        public bool ShowAllData { get; private set; }

        public SelectSerialNumberWindow(MedicalDevice device)
        {
            InitializeComponent();
            _device = device;
            LoadDeviceInfo();
            LoadSerialNumbersAsync();
        }

        private void LoadDeviceInfo()
        {
            try
            {
                DeviceNameText.Text = $"اختيار الرقم التسلسلي - {_device.Name}";
                DeviceNameInfo.Text = $"اسم الجهاز: {_device.Name}";
                DeviceBrandInfo.Text = $"الماركة: {_device.Brand ?? "غير محدد"}";
                DeviceModelInfo.Text = $"الموديل: {_device.Model ?? "غير محدد"}";
                DeviceCategoryInfo.Text = $"الفئة: {_device.Category ?? "غير محدد"}";
                DeviceStatusInfo.Text = $"الحالة: {_device.Status ?? "غير محدد"}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadSerialNumbersAsync()
        {
            try
            {
                // تحميل الأرقام التسلسلية بدون الأعمدة الإضافية
                var serialNumbersQuery = App.DatabaseContext.DeviceSerialNumbers
                    .Where(s => s.DeviceId == _device.Id && s.IsActive)
                    .Select(s => new DeviceSerialNumber
                    {
                        Id = s.Id,
                        DeviceId = s.DeviceId,
                        SerialNumber = s.SerialNumber,
                        ComponentName = s.ComponentName,
                        ComponentType = s.ComponentType,
                        Status = s.Status,
                        IsActive = s.IsActive,
                        CreatedDate = s.CreatedDate,
                        LastUpdated = s.LastUpdated
                    })
                    .OrderBy(s => s.SerialNumber);

                _serialNumbers = await serialNumbersQuery.ToListAsync();

                SerialCountInfo.Text = $"عدد الأرقام التسلسلية: {_serialNumbers.Count}";

                if (_serialNumbers.Any())
                {
                    // تحميل إحصائيات لكل رقم تسلسلي
                    await LoadSerialNumberStatistics();

                    SerialNumbersList.ItemsSource = _serialNumbers;
                    NoSerialNumbersMessage.Visibility = Visibility.Collapsed;
                }
                else
                {
                    SerialNumbersList.ItemsSource = null;
                    NoSerialNumbersMessage.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSerialNumberStatistics()
        {
            try
            {
                foreach (var serial in _serialNumbers)
                {
                    // عدد سجلات الصيانة لهذا الرقم التسلسلي
                    var maintenanceCount = await App.DatabaseContext.MaintenanceRecords
                        .CountAsync(m => m.SerialNumber == serial.SerialNumber);

                    // عدد المبيعات لهذا الرقم التسلسلي (تحقق من وجود العمود أولاً)
                    int salesCount = 0;
                    try
                    {
                        salesCount = await App.DatabaseContext.Sales
                            .CountAsync(s => s.SerialNumber == serial.SerialNumber);
                    }
                    catch
                    {
                        // العمود غير موجود، استخدم 0
                        salesCount = 0;
                    }

                    // حفظ الإحصائيات في خصائص إضافية
                    serial.MaintenanceCount = maintenanceCount;
                    serial.SalesCount = salesCount;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AllDataOption_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                ShowAllData = true;
                SelectedSerialNumber = null;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SerialNumberOption_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                if (sender is Border border && border.Tag is DeviceSerialNumber serialNumber)
                {
                    ShowAllData = false;
                    SelectedSerialNumber = serialNumber;
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
