using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using OfficeOpenXml;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;

namespace MedicalDevicesManager.Services
{
    public class ExportService
    {
        private readonly MedicalDevicesContext _context;
        private readonly string _exportDirectory;

        public ExportService(MedicalDevicesContext context)
        {
            _context = context;
            _exportDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Exports");
            
            // إنشاء مجلد التصدير إذا لم يكن موجوداً
            if (!Directory.Exists(_exportDirectory))
            {
                Directory.CreateDirectory(_exportDirectory);
            }

            // تعيين ترخيص EPPlus للاستخدام غير التجاري
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        // تصدير الأجهزة الطبية إلى Excel
        public async Task<bool> ExportDevicesToExcelAsync(string fileName = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var exportRecord = new ExportRecord();
            
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"MedicalDevices_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                }

                var filePath = Path.Combine(_exportDirectory, fileName);

                // إنشاء سجل التصدير
                exportRecord = new ExportRecord
                {
                    ExportName = fileName,
                    ExportType = "Excel",
                    DataType = "MedicalDevices",
                    FilePath = filePath,
                    Status = "InProgress",
                    CreatedDate = DateTime.Now,
                    CreatedBy = "System",
                    Parameters = JsonSerializer.Serialize(new { fromDate, toDate })
                };

                _context.ExportRecords.Add(exportRecord);
                await _context.SaveChangesAsync();

                // الحصول على البيانات
                var query = _context.MedicalDevices.AsQueryable();
                
                if (fromDate.HasValue)
                    query = query.Where(d => d.CreatedDate >= fromDate.Value);
                
                if (toDate.HasValue)
                    query = query.Where(d => d.CreatedDate <= toDate.Value);

                var devices = await query.OrderBy(d => d.Name).ToListAsync();

                // إنشاء ملف Excel
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("الأجهزة الطبية");

                    // إعداد الرؤوس
                    var headers = new[]
                    {
                        "الرقم", "اسم الجهاز", "الماركة", "الموديل", "الرقم التسلسلي", "الفئة",
                        "الوصف", "سعر الشراء", "سعر البيع", "تاريخ الشراء", "بداية الضمان",
                        "نهاية الضمان", "المورد", "الموقع", "الحالة", "تاريخ الإنشاء"
                    };

                    for (int i = 0; i < headers.Length; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = headers[i];
                        worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                    }

                    // إضافة البيانات
                    for (int i = 0; i < devices.Count; i++)
                    {
                        var device = devices[i];
                        var row = i + 2;

                        worksheet.Cells[row, 1].Value = device.Id;
                        worksheet.Cells[row, 2].Value = device.Name;
                        worksheet.Cells[row, 3].Value = device.Brand;
                        worksheet.Cells[row, 4].Value = device.Model;
                        worksheet.Cells[row, 5].Value = device.SerialNumber;
                        worksheet.Cells[row, 6].Value = device.Category;
                        worksheet.Cells[row, 7].Value = device.Description;
                        worksheet.Cells[row, 8].Value = device.PurchasePrice;
                        worksheet.Cells[row, 9].Value = device.SellingPrice;
                        worksheet.Cells[row, 10].Value = device.PurchaseDate.ToString("dd/MM/yyyy");
                        worksheet.Cells[row, 11].Value = device.WarrantyStartDate.ToString("dd/MM/yyyy");
                        worksheet.Cells[row, 12].Value = device.WarrantyEndDate.ToString("dd/MM/yyyy");
                        worksheet.Cells[row, 13].Value = device.Supplier;
                        worksheet.Cells[row, 14].Value = device.Location;
                        worksheet.Cells[row, 15].Value = device.Status;
                        worksheet.Cells[row, 16].Value = device.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                    }

                    // تنسيق الأعمدة
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // حفظ الملف
                    await package.SaveAsAsync(new FileInfo(filePath));
                }

                // تحديث سجل التصدير
                var fileInfo = new FileInfo(filePath);
                exportRecord.FileSize = fileInfo.Length;
                exportRecord.Status = "Success";
                exportRecord.CompletedDate = DateTime.Now;
                await _context.SaveChangesAsync();

                // إضافة إشعار نجاح
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "نجح تصدير الأجهزة الطبية",
                    $"تم تصدير {devices.Count} جهاز طبي إلى ملف Excel بنجاح",
                    "Success",
                    "Export",
                    exportRecord.Id,
                    "ExportRecord",
                    "Medium"
                );

                return true;
            }
            catch (Exception ex)
            {
                // تحديث حالة الفشل
                if (exportRecord.Id > 0)
                {
                    exportRecord.Status = "Failed";
                    exportRecord.CompletedDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                }

                // إضافة إشعار فشل
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "فشل في تصدير الأجهزة الطبية",
                    $"فشل في تصدير الأجهزة الطبية: {ex.Message}",
                    "Error",
                    "Export",
                    exportRecord.Id,
                    "ExportRecord",
                    "High"
                );

                return false;
            }
        }

        // تصدير المخزون إلى Excel
        public async Task<bool> ExportInventoryToExcelAsync(string fileName = null)
        {
            var exportRecord = new ExportRecord();
            
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"Inventory_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                }

                var filePath = Path.Combine(_exportDirectory, fileName);

                // إنشاء سجل التصدير
                exportRecord = new ExportRecord
                {
                    ExportName = fileName,
                    ExportType = "Excel",
                    DataType = "Inventory",
                    FilePath = filePath,
                    Status = "InProgress",
                    CreatedDate = DateTime.Now,
                    CreatedBy = "System"
                };

                _context.ExportRecords.Add(exportRecord);
                await _context.SaveChangesAsync();

                // الحصول على البيانات
                var inventoryItems = await _context.InventoryItems
                    .OrderBy(i => i.Name)
                    .ToListAsync();

                // إنشاء ملف Excel
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("المخزون");

                    // إعداد الرؤوس
                    var headers = new[]
                    {
                        "الرقم", "اسم المنتج", "الفئة", "الوصف", "المخزون الحالي", "الحد الأدنى",
                        "سعر الوحدة", "الوحدة", "الموقع", "تاريخ الانتهاء", "الحالة", "تاريخ الإنشاء"
                    };

                    for (int i = 0; i < headers.Length; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = headers[i];
                        worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGreen);
                    }

                    // إضافة البيانات
                    for (int i = 0; i < inventoryItems.Count; i++)
                    {
                        var item = inventoryItems[i];
                        var row = i + 2;

                        worksheet.Cells[row, 1].Value = item.Id;
                        worksheet.Cells[row, 2].Value = item.Name;
                        worksheet.Cells[row, 3].Value = item.Category;
                        worksheet.Cells[row, 4].Value = item.Description;
                        worksheet.Cells[row, 5].Value = item.CurrentStock;
                        worksheet.Cells[row, 6].Value = item.MinimumStock;
                        worksheet.Cells[row, 7].Value = item.UnitPrice;
                        worksheet.Cells[row, 8].Value = item.Unit;
                        worksheet.Cells[row, 9].Value = item.Location;
                        worksheet.Cells[row, 10].Value = item.ExpiryDate?.ToString("dd/MM/yyyy") ?? "";
                        worksheet.Cells[row, 11].Value = item.Status;
                        worksheet.Cells[row, 12].Value = item.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                    }

                    // تنسيق الأعمدة
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // حفظ الملف
                    await package.SaveAsAsync(new FileInfo(filePath));
                }

                // تحديث سجل التصدير
                var fileInfo = new FileInfo(filePath);
                exportRecord.FileSize = fileInfo.Length;
                exportRecord.Status = "Success";
                exportRecord.CompletedDate = DateTime.Now;
                await _context.SaveChangesAsync();

                // إضافة إشعار نجاح
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "نجح تصدير المخزون",
                    $"تم تصدير {inventoryItems.Count} عنصر مخزون إلى ملف Excel بنجاح",
                    "Success",
                    "Export",
                    exportRecord.Id,
                    "ExportRecord",
                    "Medium"
                );

                return true;
            }
            catch (Exception ex)
            {
                // تحديث حالة الفشل
                if (exportRecord.Id > 0)
                {
                    exportRecord.Status = "Failed";
                    exportRecord.CompletedDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                }

                // إضافة إشعار فشل
                var notificationService = new NotificationService(_context);
                await notificationService.AddNotificationAsync(
                    "فشل في تصدير المخزون",
                    $"فشل في تصدير المخزون: {ex.Message}",
                    "Error",
                    "Export",
                    exportRecord.Id,
                    "ExportRecord",
                    "High"
                );

                return false;
            }
        }

        // الحصول على جميع سجلات التصدير
        public async Task<List<ExportRecord>> GetAllExportsAsync()
        {
            return await _context.ExportRecords
                .OrderByDescending(e => e.CreatedDate)
                .ToListAsync();
        }

        // حذف ملف تصدير
        public async Task<bool> DeleteExportAsync(int exportId)
        {
            try
            {
                var exportRecord = await _context.ExportRecords.FindAsync(exportId);
                if (exportRecord == null)
                {
                    return false;
                }

                // حذف الملف إذا كان موجوداً
                if (File.Exists(exportRecord.FilePath))
                {
                    File.Delete(exportRecord.FilePath);
                }

                // حذف السجل من قاعدة البيانات
                _context.ExportRecords.Remove(exportRecord);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
