<Window x:Class="MedicalDevicesManager.Windows.DeviceSerialNumbersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الأرقام التسلسلية" Height="600" Width="900"
        WindowStartupLocation="CenterScreen" FlowDirection="RightToLeft"
        Background="#F8F9FA" FontFamily="Segoe UI" FontSize="14">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <Border Grid.Row="0" Background="#007BFF" CornerRadius="8" Padding="15" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitle" Text="إدارة الأرقام التسلسلية" 
                          FontSize="18" FontWeight="Bold" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- نموذج إضافة رقم تسلسلي -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20"
                BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الرقم التسلسلي -->
                <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                    <TextBlock Text="الرقم التسلسلي *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="SerialNumberTextBox" Height="35" Padding="10,8"
                            BorderBrush="#CED4DA" BorderThickness="1" FontSize="14"/>
                </StackPanel>

                <!-- اسم المكون -->
                <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                    <TextBlock Text="اسم المكون *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ComponentNameTextBox" Height="35" Padding="10,8"
                            BorderBrush="#CED4DA" BorderThickness="1" FontSize="14"/>
                </StackPanel>

                <!-- الملاحظات -->
                <StackPanel Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2" Margin="0,0,10,0">
                    <TextBlock Text="الملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="NotesTextBox" Height="35" Padding="10,8"
                            BorderBrush="#CED4DA" BorderThickness="1" FontSize="14"/>
                </StackPanel>

                <!-- أزرار العمليات -->
                <StackPanel Grid.Column="2" Grid.Row="0" Grid.RowSpan="2" 
                           Orientation="Vertical" VerticalAlignment="Center" Margin="10,0,0,0">
                    <Button x:Name="AddBtn" Content="➕ إضافة" Width="100" Height="35" 
                           Background="#28A745" Foreground="White" BorderThickness="0" 
                           FontSize="14" FontWeight="SemiBold" Margin="0,0,0,10" 
                           Click="AddBtn_Click"/>
                    <Button x:Name="ClearBtn" Content="🔄 مسح" Width="100" Height="35" 
                           Background="#6C757D" Foreground="White" BorderThickness="0" 
                           FontSize="14" FontWeight="SemiBold" 
                           Click="ClearBtn_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- جدول الأرقام التسلسلية -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="15"
                BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <TextBlock Grid.Row="0" Text="📋 قائمة الأرقام التسلسلية" 
                          FontSize="16" FontWeight="Bold" Margin="0,0,0,15"
                          Foreground="#495057"/>

                <!-- الجدول -->
                <DataGrid Grid.Row="1" x:Name="SerialNumbersDataGrid" 
                         AutoGenerateColumns="False" CanUserAddRows="False" 
                         CanUserDeleteRows="False" IsReadOnly="True"
                         GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                         Background="White" RowBackground="White" 
                         AlternatingRowBackground="#F8F9FA"
                         BorderThickness="0" FontSize="13"
                         SelectionMode="Single" SelectionUnit="FullRow">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم التسلسلي" Binding="{Binding SerialNumber}" 
                                           Width="200" HeaderStyle="{StaticResource DataGridHeaderStyle}"/>
                        <DataGridTextColumn Header="اسم المكون" Binding="{Binding ComponentName}" 
                                           Width="200" HeaderStyle="{StaticResource DataGridHeaderStyle}"/>
                        <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" 
                                           Width="*" HeaderStyle="{StaticResource DataGridHeaderStyle}"/>
                        <DataGridTextColumn Header="تاريخ الإضافة" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                           Width="150" HeaderStyle="{StaticResource DataGridHeaderStyle}"/>
                    </DataGrid.Columns>

                    <DataGrid.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="🗑️ حذف" Click="DeleteMenuItem_Click"/>
                        </ContextMenu>
                    </DataGrid.ContextMenu>
                </DataGrid>
            </Grid>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <TextBlock x:Name="CountTextBlock" Text="العدد: 0" 
                      FontSize="14" FontWeight="SemiBold" 
                      VerticalAlignment="Center" Margin="0,0,20,0"/>
            <Button x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="35" 
                   Background="#17A2B8" Foreground="White" BorderThickness="0" 
                   FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" 
                   Click="RefreshBtn_Click"/>
            <Button x:Name="CloseBtn" Content="❌ إغلاق" Width="100" Height="35" 
                   Background="#DC3545" Foreground="White" BorderThickness="0" 
                   FontSize="14" FontWeight="SemiBold" 
                   Click="CloseBtn_Click"/>
        </StackPanel>
    </Grid>

    <Window.Resources>
        <Style x:Key="DataGridHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#007BFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>
</Window>
