using System;
using System.IO;
using System.Windows;
using Microsoft.Win32;
using System.Threading.Tasks;
using System.Windows.Threading;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace MedicalDevicesManager.Windows
{
    public partial class SettingsWindow : Window
    {
        private DispatcherTimer _backupTimer;
        private DateTime _lastBackupTime;

        public SettingsWindow()
        {
            InitializeComponent();
            LoadSettings();
            InitializeBackupTimer();
        }
        
        private void LoadSettings()
        {
            try
            {
                // تحميل الإعدادات المحفوظة (يمكن تطويرها لاحقاً لحفظ الإعدادات في ملف أو قاعدة البيانات)
                // هنا نضع القيم الافتراضية

                // تحميل آخر وقت نسخ احتياطي
                LoadLastBackupTime();

                // تحميل مسار النسخ الاحتياطي المحفوظ
                LoadBackupPath();

                // تحديث حالة النسخ الاحتياطي
                UpdateBackupStatus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeBackupTimer()
        {
            try
            {
                _backupTimer = new DispatcherTimer();
                _backupTimer.Interval = TimeSpan.FromHours(1); // فحص كل ساعة
                _backupTimer.Tick += BackupTimer_Tick;
                _backupTimer.Start();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة مؤقت النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BackupTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                if (AutoBackupCheckBox.IsChecked == true)
                {
                    await CheckAndPerformAutoBackup();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في النسخ الاحتياطي التلقائي: {ex.Message}");
            }
        }

        private void LoadLastBackupTime()
        {
            try
            {
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "MedicalDevicesManager");
                var backupSettingsFile = Path.Combine(settingsPath, "backup_settings.txt");

                if (File.Exists(backupSettingsFile))
                {
                    var content = File.ReadAllText(backupSettingsFile);
                    if (DateTime.TryParse(content, out DateTime lastBackup))
                    {
                        _lastBackupTime = lastBackup;
                    }
                }
                else
                {
                    _lastBackupTime = DateTime.MinValue;
                }
            }
            catch
            {
                _lastBackupTime = DateTime.MinValue;
            }
        }

        private void SaveLastBackupTime()
        {
            try
            {
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "MedicalDevicesManager");
                if (!Directory.Exists(settingsPath))
                {
                    Directory.CreateDirectory(settingsPath);
                }

                var backupSettingsFile = Path.Combine(settingsPath, "backup_settings.txt");
                File.WriteAllText(backupSettingsFile, DateTime.Now.ToString());
                _lastBackupTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ وقت النسخ الاحتياطي: {ex.Message}");
            }
        }

        private void LoadBackupPath()
        {
            try
            {
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "MedicalDevicesManager");
                var backupPathFile = Path.Combine(settingsPath, "backup_path.txt");

                if (File.Exists(backupPathFile))
                {
                    var savedPath = File.ReadAllText(backupPathFile).Trim();
                    if (!string.IsNullOrEmpty(savedPath) && Directory.Exists(savedPath))
                    {
                        BackupPathTextBox.Text = savedPath;
                    }
                    else
                    {
                        // إذا كان المسار المحفوظ غير صالح، استخدم المسار الافتراضي
                        SetDefaultBackupPath();
                    }
                }
                else
                {
                    // إذا لم يكن هناك مسار محفوظ، استخدم المسار الافتراضي
                    SetDefaultBackupPath();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل مسار النسخ الاحتياطي: {ex.Message}");
                SetDefaultBackupPath();
            }
        }

        private void SetDefaultBackupPath()
        {
            try
            {
                var defaultPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "MedicalDevicesBackups");
                BackupPathTextBox.Text = defaultPath;

                // إنشاء المجلد إذا لم يكن موجوداً
                if (!Directory.Exists(defaultPath))
                {
                    Directory.CreateDirectory(defaultPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعيين المسار الافتراضي: {ex.Message}");
                BackupPathTextBox.Text = @"C:\Backups\MedicalDevices";
            }
        }

        private void SaveBackupPath()
        {
            try
            {
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "MedicalDevicesManager");
                if (!Directory.Exists(settingsPath))
                {
                    Directory.CreateDirectory(settingsPath);
                }

                var backupPathFile = Path.Combine(settingsPath, "backup_path.txt");
                File.WriteAllText(backupPathFile, BackupPathTextBox.Text);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ مسار النسخ الاحتياطي: {ex.Message}");
            }
        }
        
        private void SaveSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateSettings())
                    return;
                
                // حفظ إعدادات الشركة
                var companySettings = new
                {
                    CompanyName = CompanyNameTextBox.Text,
                    CommercialReg = CommercialRegTextBox.Text,
                    Address = CompanyAddressTextBox.Text,
                    Phone = CompanyPhoneTextBox.Text,
                    Email = CompanyEmailTextBox.Text,
                    Website = CompanyWebsiteTextBox.Text
                };
                
                // حفظ إعدادات النظام
                var systemSettings = new
                {
                    Currency = CurrencyComboBox.Text,
                    Language = LanguageComboBox.Text,
                    Timezone = TimezoneComboBox.Text,
                    DateFormat = DateFormatComboBox.Text
                };
                
                // حفظ إعدادات المخزون
                var inventorySettings = new
                {
                    LowStockAlerts = LowStockAlertsCheckBox.IsChecked,
                    OutOfStockAlerts = OutOfStockAlertsCheckBox.IsChecked,
                    ExpiryAlerts = ExpiryAlertsCheckBox.IsChecked,
                    LowStockPercentage = LowStockPercentageTextBox.Text,
                    ExpiryWarningDays = ExpiryWarningDaysTextBox.Text,
                    AutoReorder = AutoReorderCheckBox.IsChecked,
                    AutoPricing = AutoPricingCheckBox.IsChecked,
                    DefaultProfitMargin = DefaultProfitMarginTextBox.Text,
                    DefaultReorderQuantity = DefaultReorderQuantityTextBox.Text
                };
                
                // حفظ إعدادات المبيعات
                var salesSettings = new
                {
                    InvoicePrefix = InvoicePrefixTextBox.Text,
                    InvoiceNumberLength = InvoiceNumberLengthTextBox.Text,
                    AutoInvoiceNumber = AutoInvoiceNumberCheckBox.IsChecked,
                    PrintAfterSale = PrintAfterSaleCheckBox.IsChecked,
                    VATPercentage = VATPercentageTextBox.Text,
                    MaxDiscount = MaxDiscountTextBox.Text,
                    ApplyVAT = ApplyVATCheckBox.IsChecked,
                    RequireDiscountApproval = RequireDiscountApprovalCheckBox.IsChecked
                };
                
                // حفظ إعدادات النسخ الاحتياطي
                var backupSettings = new
                {
                    AutoBackup = AutoBackupCheckBox.IsChecked,
                    BackupFrequency = BackupFrequencyComboBox.Text,
                    BackupRetention = BackupRetentionTextBox.Text,
                    BackupPath = BackupPathTextBox.Text
                };
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private bool ValidateSettings()
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(CompanyNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الشركة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(LowStockPercentageTextBox.Text, out decimal lowStockPercentage) || 
                lowStockPercentage < 0 || lowStockPercentage > 100)
            {
                MessageBox.Show("يرجى إدخال نسبة صحيحة لتنبيه المخزون المنخفض (0-100)", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!int.TryParse(ExpiryWarningDaysTextBox.Text, out int expiryDays) || expiryDays < 0)
            {
                MessageBox.Show("يرجى إدخال عدد أيام صحيح لتنبيه انتهاء الصلاحية", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(VATPercentageTextBox.Text, out decimal vatPercentage) || 
                vatPercentage < 0 || vatPercentage > 100)
            {
                MessageBox.Show("يرجى إدخال نسبة صحيحة لضريبة القيمة المضافة (0-100)", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void ResetSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                "تأكيد إعادة التعيين",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question
            );
            
            if (result == MessageBoxResult.Yes)
            {
                ResetToDefaults();
                MessageBox.Show("تم إعادة تعيين الإعدادات إلى القيم الافتراضية", "تم إعادة التعيين", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        
        private void ResetToDefaults()
        {
            // إعادة تعيين إعدادات الشركة
            CompanyNameTextBox.Text = "شركة الأجهزة الطبية المتقدمة";
            CommercialRegTextBox.Text = "**********";
            CompanyAddressTextBox.Text = "الرياض، المملكة العربية السعودية";
            CompanyPhoneTextBox.Text = "+966 11 123 4567";
            CompanyEmailTextBox.Text = "<EMAIL>";
            CompanyWebsiteTextBox.Text = "www.medicaldevices.sa";
            
            // إعادة تعيين إعدادات النظام
            CurrencyComboBox.SelectedIndex = 0;
            LanguageComboBox.SelectedIndex = 0;
            TimezoneComboBox.SelectedIndex = 0;
            DateFormatComboBox.SelectedIndex = 0;
            
            // إعادة تعيين إعدادات المخزون
            LowStockAlertsCheckBox.IsChecked = true;
            OutOfStockAlertsCheckBox.IsChecked = true;
            ExpiryAlertsCheckBox.IsChecked = true;
            LowStockPercentageTextBox.Text = "20";
            ExpiryWarningDaysTextBox.Text = "30";
            AutoReorderCheckBox.IsChecked = false;
            AutoPricingCheckBox.IsChecked = false;
            DefaultProfitMarginTextBox.Text = "25";
            DefaultReorderQuantityTextBox.Text = "50";
            
            // إعادة تعيين إعدادات المبيعات
            InvoicePrefixTextBox.Text = "INV";
            InvoiceNumberLengthTextBox.Text = "6";
            AutoInvoiceNumberCheckBox.IsChecked = true;
            PrintAfterSaleCheckBox.IsChecked = false;
            VATPercentageTextBox.Text = "15";
            MaxDiscountTextBox.Text = "20";
            ApplyVATCheckBox.IsChecked = true;
            RequireDiscountApprovalCheckBox.IsChecked = true;
            
            // إعادة تعيين إعدادات النسخ الاحتياطي
            AutoBackupCheckBox.IsChecked = true;
            BackupFrequencyComboBox.SelectedIndex = 0;
            BackupRetentionTextBox.Text = "7";
            BackupPathTextBox.Text = "C:\\Backups\\MedicalDevices";
        }
        
        private async Task<bool> CheckAndPerformAutoBackup()
        {
            try
            {
                if (AutoBackupCheckBox.IsChecked != true)
                    return false;

                var frequency = BackupFrequencyComboBox.Text;
                var shouldBackup = false;

                switch (frequency)
                {
                    case "يومياً":
                        shouldBackup = (DateTime.Now - _lastBackupTime).TotalDays >= 1;
                        break;
                    case "أسبوعياً":
                        shouldBackup = (DateTime.Now - _lastBackupTime).TotalDays >= 7;
                        break;
                    case "شهرياً":
                        shouldBackup = (DateTime.Now - _lastBackupTime).TotalDays >= 30;
                        break;
                }

                if (shouldBackup)
                {
                    return await CreateBackupAsync(true);
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص النسخ الاحتياطي التلقائي: {ex.Message}");
                return false;
            }
        }

        private async void CreateBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            await CreateBackupAsync(false);
        }

        private async Task<bool> CreateBackupAsync(bool isAutomatic = false)
        {
            try
            {
                var backupPath = BackupPathTextBox.Text;
                if (string.IsNullOrEmpty(backupPath))
                {
                    backupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "MedicalDevicesBackups");
                    BackupPathTextBox.Text = backupPath;
                }

                if (!Directory.Exists(backupPath))
                {
                    Directory.CreateDirectory(backupPath);
                }

                // إنشاء نسخة احتياطية شاملة
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFolderName = $"MedicalDevices_Backup_{timestamp}";
                var fullBackupPath = Path.Combine(backupPath, backupFolderName);
                Directory.CreateDirectory(fullBackupPath);

                // نسخ قاعدة البيانات - البحث في أماكن متعددة
                var currentDbPath = FindDatabaseFile();
                if (!string.IsNullOrEmpty(currentDbPath) && File.Exists(currentDbPath))
                {
                    var backupDbPath = Path.Combine(fullBackupPath, "MedicalDevices.db");
                    File.Copy(currentDbPath, backupDbPath, true);

                    if (!isAutomatic)
                    {
                        System.Diagnostics.Debug.WriteLine($"تم نسخ قاعدة البيانات من: {currentDbPath}");
                    }
                }
                else
                {
                    var errorMessage = $"تحذير: لم يتم العثور على ملف قاعدة البيانات!\n\nتم البحث في المواقع التالية:\n{GetSearchLocationsInfo()}";

                    if (!isAutomatic)
                    {
                        MessageBox.Show(errorMessage, "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    System.Diagnostics.Debug.WriteLine(errorMessage);
                }

                // نسخ ملفات المستندات إذا وجدت
                await BackupDocumentsAsync(fullBackupPath);

                // إنشاء ملف معلومات النسخة الاحتياطية
                await CreateBackupInfoFileAsync(fullBackupPath);

                // تنظيف النسخ القديمة
                await CleanOldBackupsAsync(backupPath);

                // حفظ وقت النسخ الاحتياطي
                SaveLastBackupTime();

                if (!isAutomatic)
                {
                    // فحص محتويات النسخة الاحتياطية
                    var backupDbPath = Path.Combine(fullBackupPath, "MedicalDevices.db");
                    var documentsPath = Path.Combine(fullBackupPath, "Documents");
                    var infoPath = Path.Combine(fullBackupPath, "backup_info.txt");

                    var contentsList = new List<string>();

                    if (File.Exists(backupDbPath))
                    {
                        var dbSize = new FileInfo(backupDbPath).Length;
                        contentsList.Add($"✅ قاعدة البيانات: MedicalDevices.db ({FormatFileSize(dbSize)})");
                    }
                    else
                    {
                        contentsList.Add($"❌ قاعدة البيانات: غير موجودة");
                    }

                    if (Directory.Exists(documentsPath))
                    {
                        var docCount = Directory.GetFiles(documentsPath, "*", SearchOption.AllDirectories).Length;
                        contentsList.Add($"✅ المستندات: مجلد Documents/ ({docCount} ملف)");
                    }
                    else
                    {
                        contentsList.Add($"⚠️ المستندات: لا توجد مستندات");
                    }

                    if (File.Exists(infoPath))
                    {
                        contentsList.Add($"✅ معلومات النسخة: backup_info.txt");
                    }

                    var successMessage = $"تم إنشاء النسخة الاحتياطية!\n\n" +
                                       $"📁 المسار: {fullBackupPath}\n\n" +
                                       $"محتويات النسخة الاحتياطية:\n" +
                                       string.Join("\n", contentsList) + "\n\n" +
                                       $"يمكنك استعادة هذه النسخة من خلال:\n" +
                                       $"الإعدادات → النسخ الاحتياطي → استعادة من نسخة احتياطية";

                    MessageBox.Show(successMessage, "تقرير النسخ الاحتياطي", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                return true;
            }
            catch (Exception ex)
            {
                var message = $"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}";
                if (!isAutomatic)
                {
                    MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                System.Diagnostics.Debug.WriteLine(message);
                return false;
            }
        }
        
        private async void RestoreBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // اختيار نوع الاستعادة
                var choice = MessageBox.Show(
                    "اختر نوع النسخة الاحتياطية:\n\n" +
                    "نعم: استعادة من مجلد نسخة احتياطية شاملة\n" +
                    "لا: استعادة من ملف قاعدة بيانات فقط\n" +
                    "إلغاء: إلغاء العملية",
                    "نوع الاستعادة",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question
                );

                if (choice == MessageBoxResult.Cancel)
                    return;

                if (choice == MessageBoxResult.Yes)
                {
                    // استعادة من مجلد نسخة احتياطية شاملة
                    await RestoreFromFullBackup();
                }
                else
                {
                    // استعادة من ملف قاعدة بيانات فقط
                    RestoreFromDatabaseFile();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RestoreFromFullBackup()
        {
            try
            {
                var folderDialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "اختر مجلد النسخة الاحتياطية الشاملة"
                };

                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    var backupFolder = folderDialog.SelectedPath;
                    var dbPath = Path.Combine(backupFolder, "MedicalDevices.db");
                    var documentsPath = Path.Combine(backupFolder, "Documents");
                    var infoPath = Path.Combine(backupFolder, "backup_info.txt");

                    // تشخيص محتويات المجلد
                    var folderContents = new System.Text.StringBuilder();
                    folderContents.AppendLine($"مسار المجلد المختار: {backupFolder}");
                    folderContents.AppendLine($"مسار قاعدة البيانات المتوقع: {dbPath}");
                    folderContents.AppendLine();
                    folderContents.AppendLine("محتويات المجلد:");

                    try
                    {
                        var files = Directory.GetFiles(backupFolder);
                        var directories = Directory.GetDirectories(backupFolder);

                        foreach (var file in files)
                        {
                            folderContents.AppendLine($"  📄 {Path.GetFileName(file)}");
                        }

                        foreach (var dir in directories)
                        {
                            folderContents.AppendLine($"  📁 {Path.GetFileName(dir)}/");
                        }
                    }
                    catch (Exception ex)
                    {
                        folderContents.AppendLine($"خطأ في قراءة محتويات المجلد: {ex.Message}");
                    }

                    // التحقق من صحة النسخة الاحتياطية
                    if (!File.Exists(dbPath))
                    {
                        // البحث عن ملفات قاعدة البيانات في المجلد
                        var dbFiles = Directory.GetFiles(backupFolder, "*.db");

                        if (dbFiles.Length > 0)
                        {
                            var foundDbFiles = string.Join("\n", dbFiles.Select(f => $"  • {Path.GetFileName(f)}"));
                            var choice = MessageBox.Show(
                                $"لم يتم العثور على ملف 'MedicalDevices.db' لكن تم العثور على ملفات قاعدة بيانات أخرى:\n\n{foundDbFiles}\n\nهل تريد استخدام أول ملف قاعدة بيانات موجود؟",
                                "ملفات قاعدة بيانات موجودة",
                                MessageBoxButton.YesNo,
                                MessageBoxImage.Question
                            );

                            if (choice == MessageBoxResult.Yes)
                            {
                                dbPath = dbFiles[0];
                            }
                            else
                            {
                                return;
                            }
                        }
                        else
                        {
                            var message = $"لم يتم العثور على أي ملف قاعدة بيانات في المجلد المختار.\n\n{folderContents}\n\nيجب أن يحتوي المجلد على ملف قاعدة بيانات بامتداد .db";
                            MessageBox.Show(message, "تشخيص النسخة الاحتياطية", MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }
                    }

                    // عرض معلومات النسخة الاحتياطية
                    if (File.Exists(infoPath))
                    {
                        var info = await File.ReadAllTextAsync(infoPath);
                        var result = MessageBox.Show($"معلومات النسخة الاحتياطية:\n\n{info}\n\nهل تريد المتابعة؟",
                            "معلومات النسخة الاحتياطية", MessageBoxButton.YesNo, MessageBoxImage.Information);

                        if (result != MessageBoxResult.Yes)
                            return;
                    }

                    // تأكيد الاستعادة
                    var confirmResult = MessageBox.Show(
                        "تحذير: سيتم استبدال جميع البيانات والمستندات الحالية.\nهل أنت متأكد؟",
                        "تأكيد الاستعادة الشاملة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning
                    );

                    if (confirmResult == MessageBoxResult.Yes)
                    {
                        // استعادة قاعدة البيانات
                        var currentDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "MedicalDevices.db");
                        File.Copy(dbPath, currentDbPath, true);

                        // استعادة المستندات
                        if (Directory.Exists(documentsPath))
                        {
                            await RestoreDocumentsAsync(documentsPath);
                        }

                        MessageBox.Show("تم استعادة النسخة الاحتياطية الشاملة بنجاح!\nيرجى إعادة تشغيل البرنامج.",
                            "نجحت الاستعادة", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية الشاملة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RestoreFromDatabaseFile()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "Database files (*.db)|*.db|All files (*.*)|*.*",
                    Title = "اختر ملف قاعدة البيانات"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "تحذير: سيتم استبدال قاعدة البيانات الحالية فقط.\nهل أنت متأكد؟",
                        "تأكيد الاستعادة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        var currentDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "MedicalDevices.db");
                        File.Copy(openFileDialog.FileName, currentDbPath, true);
                        MessageBox.Show("تم استعادة قاعدة البيانات بنجاح!\nيرجى إعادة تشغيل البرنامج.",
                            "نجحت الاستعادة", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RestoreDocumentsAsync(string documentsBackupPath)
        {
            try
            {
                var deviceFolders = Directory.GetDirectories(documentsBackupPath, "Device_*");

                foreach (var deviceFolder in deviceFolders)
                {
                    var deviceIdStr = Path.GetFileName(deviceFolder).Replace("Device_", "");
                    if (int.TryParse(deviceIdStr, out int deviceId))
                    {
                        // إنشاء مجلد للجهاز في مجلد المستندات الحالي
                        var documentsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents", $"Device_{deviceId}");
                        if (!Directory.Exists(documentsPath))
                        {
                            Directory.CreateDirectory(documentsPath);
                        }

                        // نسخ جميع الملفات
                        var files = Directory.GetFiles(deviceFolder);
                        foreach (var file in files)
                        {
                            var fileName = Path.GetFileName(file);
                            var destPath = Path.Combine(documentsPath, fileName);
                            File.Copy(file, destPath, true);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استعادة المستندات: {ex.Message}");
            }
        }
        
        private void ExportDataBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                    Title = "تصدير البيانات",
                    FileName = $"MedicalDevices_Export_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };
                
                if (saveFileDialog.ShowDialog() == true)
                {
                    // هنا يمكن إضافة كود تصدير البيانات إلى CSV
                    MessageBox.Show($"تم تصدير البيانات بنجاح!\nالمسار: {saveFileDialog.FileName}", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        // دوال الوحدات المنقولة إلى الإعدادات
        private void OpenAdvancedInventoryBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var advancedInventoryWindow = new AdvancedInventorySettingsWindow();
                advancedInventoryWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إعدادات المخزون المتقدمة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenExportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var exportWindow = new ExportWindow();
                exportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenNotificationsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var notificationsWindow = new NotificationsWindow();
                notificationsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الإشعارات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RunDiagnosticBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تشغيل تشخيص النظام
                var result = MessageBox.Show("هل تريد تشغيل فحص شامل للنظام؟\n\nسيتم فحص:\n- قاعدة البيانات\n- الملفات المطلوبة\n- الأداء العام\n- التحقق من التحديثات",
                    "تشخيص النظام", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // هنا يمكن إضافة منطق التشخيص الفعلي
                    MessageBox.Show("تم فحص النظام بنجاح!\n\n✅ قاعدة البيانات: سليمة\n✅ الملفات: موجودة\n✅ الأداء: ممتاز\n✅ النظام محدث",
                        "نتائج التشخيص", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشخيص النظام: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenSmartSystemBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var smartSystemWindow = new SmartSystemWindow();
                smartSystemWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح النظام الذكي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task BackupDocumentsAsync(string backupPath)
        {
            try
            {
                var documentsPath = Path.Combine(backupPath, "Documents");
                Directory.CreateDirectory(documentsPath);

                // البحث عن جميع الملفات المرتبطة بالأجهزة
                var devices = await App.DatabaseContext.MedicalDevices.ToListAsync();

                foreach (var device in devices)
                {
                    var deviceFolder = Path.Combine(documentsPath, $"Device_{device.Id}");
                    Directory.CreateDirectory(deviceFolder);

                    // نسخ ملفات المستندات
                    var documentPaths = new[]
                    {
                        device.UserManualPath,
                        device.MaintenanceManualPath,
                        device.OriginCertificatePath,
                        device.QualityCertificatePath,
                        device.OfficialCertificationsPath,
                        device.TechnicalInfoBookletPath,
                        device.ImportPapersPath,
                        device.ContractPapersPath
                    };

                    foreach (var docPath in documentPaths)
                    {
                        if (!string.IsNullOrEmpty(docPath) && File.Exists(docPath))
                        {
                            var fileName = Path.GetFileName(docPath);
                            var destPath = Path.Combine(deviceFolder, fileName);
                            File.Copy(docPath, destPath, true);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في نسخ المستندات: {ex.Message}");
            }
        }

        private async Task CreateBackupInfoFileAsync(string backupPath)
        {
            try
            {
                var infoPath = Path.Combine(backupPath, "backup_info.txt");
                var info = new System.Text.StringBuilder();

                info.AppendLine("=== معلومات النسخة الاحتياطية ===");
                info.AppendLine($"تاريخ الإنشاء: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                info.AppendLine($"إصدار النظام: 6.1");
                info.AppendLine();

                // إحصائيات قاعدة البيانات
                var devicesCount = await App.DatabaseContext.MedicalDevices.CountAsync();
                var salesCount = await App.DatabaseContext.Sales.CountAsync();
                var maintenanceCount = await App.DatabaseContext.MaintenanceRecords.CountAsync();
                var customersCount = await App.DatabaseContext.Customers.CountAsync();

                info.AppendLine("=== إحصائيات البيانات ===");
                info.AppendLine($"عدد الأجهزة الطبية: {devicesCount}");
                info.AppendLine($"عدد المبيعات: {salesCount}");
                info.AppendLine($"عدد سجلات الصيانة: {maintenanceCount}");
                info.AppendLine($"عدد العملاء: {customersCount}");
                info.AppendLine();

                info.AppendLine("=== ملاحظات ===");
                info.AppendLine("- تحتوي هذه النسخة على قاعدة البيانات الكاملة");
                info.AppendLine("- تم نسخ جميع المستندات المرتبطة");
                info.AppendLine("- يمكن استعادة هذه النسخة من خلال نافذة الإعدادات");

                await File.WriteAllTextAsync(infoPath, info.ToString());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ملف معلومات النسخة: {ex.Message}");
            }
        }

        private async Task CleanOldBackupsAsync(string backupPath)
        {
            try
            {
                if (!int.TryParse(BackupRetentionTextBox.Text, out int retentionDays))
                    retentionDays = 7;

                var directories = Directory.GetDirectories(backupPath, "MedicalDevices_Backup_*");
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);

                foreach (var dir in directories)
                {
                    var dirInfo = new DirectoryInfo(dir);
                    if (dirInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            Directory.Delete(dir, true);
                        }
                        catch
                        {
                            // تجاهل أخطاء الحذف
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف النسخ القديمة: {ex.Message}");
            }
        }

        private void UpdateBackupStatus()
        {
            try
            {
                if (AutoBackupCheckBox.IsChecked == true)
                {
                    BackupStatusTextBlock.Text = "✅ النسخ الاحتياطي التلقائي مفعل";
                    BackupStatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                }
                else
                {
                    BackupStatusTextBlock.Text = "❌ النسخ الاحتياطي التلقائي معطل";
                    BackupStatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                }

                if (_lastBackupTime != DateTime.MinValue)
                {
                    LastBackupTextBlock.Text = $"آخر نسخة احتياطية: {_lastBackupTime:yyyy-MM-dd HH:mm:ss}";

                    // حساب موعد النسخة التالية
                    var frequency = BackupFrequencyComboBox.Text;
                    DateTime nextBackup = _lastBackupTime;

                    switch (frequency)
                    {
                        case "يومياً":
                            nextBackup = _lastBackupTime.AddDays(1);
                            break;
                        case "أسبوعياً":
                            nextBackup = _lastBackupTime.AddDays(7);
                            break;
                        case "شهرياً":
                            nextBackup = _lastBackupTime.AddDays(30);
                            break;
                    }

                    if (AutoBackupCheckBox.IsChecked == true)
                    {
                        NextBackupTextBlock.Text = $"النسخة التالية: {nextBackup:yyyy-MM-dd HH:mm:ss}";

                        if (DateTime.Now >= nextBackup)
                        {
                            NextBackupTextBlock.Text += " (مستحقة الآن)";
                            NextBackupTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                        }
                        else
                        {
                            NextBackupTextBlock.Foreground = System.Windows.Media.Brushes.Blue;
                        }
                    }
                    else
                    {
                        NextBackupTextBlock.Text = "النسخة التالية: معطلة";
                        NextBackupTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    }
                }
                else
                {
                    LastBackupTextBlock.Text = "آخر نسخة احتياطية: لم يتم إنشاء نسخ احتياطية بعد";
                    NextBackupTextBlock.Text = "النسخة التالية: غير محدد";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة النسخ الاحتياطي: {ex.Message}");
            }
        }

        private void RefreshBackupStatusBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadLastBackupTime();
                UpdateBackupStatus();
                MessageBox.Show("تم تحديث حالة النسخ الاحتياطي", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الحالة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AutoBackupCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            UpdateBackupStatus();
        }

        private void AutoBackupCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateBackupStatus();
        }

        private void BackupFrequencyComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            UpdateBackupStatus();
        }

        private void BrowseBackupPathBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folderDialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "اختر مجلد حفظ النسخ الاحتياطية",
                    ShowNewFolderButton = true,
                    SelectedPath = BackupPathTextBox.Text
                };

                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    BackupPathTextBox.Text = folderDialog.SelectedPath;

                    // حفظ المسار الجديد
                    SaveBackupPath();

                    // تحديث حالة النسخ الاحتياطي
                    UpdateBackupStatus();

                    // إظهار رسالة تأكيد
                    MessageBox.Show($"تم تحديد مسار النسخ الاحتياطي:\n{folderDialog.SelectedPath}\n\nسيتم حفظ جميع النسخ الاحتياطية في هذا المجلد.",
                        "تم تحديث المسار", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار مسار النسخ الاحتياطي: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BackupPathTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            try
            {
                // حفظ المسار عند تغييره (مع تأخير بسيط لتجنب الحفظ المتكرر)
                if (!string.IsNullOrEmpty(BackupPathTextBox.Text))
                {
                    SaveBackupPath();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ مسار النسخ الاحتياطي: {ex.Message}");
            }
        }

        private string FindDatabaseFile()
        {
            try
            {
                // قائمة المواقع المحتملة لملف قاعدة البيانات
                var searchLocations = new[]
                {
                    // في مجلد التطبيق الحالي
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "MedicalDevices.db"),

                    // في مجلد العمل الحالي
                    Path.Combine(Directory.GetCurrentDirectory(), "MedicalDevices.db"),

                    // في مجلد البيانات المحلية للتطبيق
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "MedicalDevicesManager", "MedicalDevices.db"),

                    // في مجلد المستندات
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "MedicalDevicesManager", "MedicalDevices.db"),

                    // في مجلد البيانات المشتركة
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "MedicalDevicesManager", "MedicalDevices.db"),

                    // مسار مباشر
                    "MedicalDevices.db"
                };

                foreach (var location in searchLocations)
                {
                    if (File.Exists(location))
                    {
                        System.Diagnostics.Debug.WriteLine($"تم العثور على قاعدة البيانات في: {location}");
                        return location;
                    }
                }

                // البحث في مجلدات فرعية
                var currentDir = AppDomain.CurrentDomain.BaseDirectory;
                var dbFiles = Directory.GetFiles(currentDir, "*.db", SearchOption.AllDirectories);

                foreach (var dbFile in dbFiles)
                {
                    if (Path.GetFileName(dbFile).ToLower().Contains("medical"))
                    {
                        System.Diagnostics.Debug.WriteLine($"تم العثور على ملف قاعدة بيانات محتمل: {dbFile}");
                        return dbFile;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن قاعدة البيانات: {ex.Message}");
                return null;
            }
        }

        private string GetSearchLocationsInfo()
        {
            var locations = new[]
            {
                $"• {Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "MedicalDevices.db")}",
                $"• {Path.Combine(Directory.GetCurrentDirectory(), "MedicalDevices.db")}",
                $"• {Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "MedicalDevicesManager", "MedicalDevices.db")}",
                $"• {Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "MedicalDevicesManager", "MedicalDevices.db")}",
                $"• MedicalDevices.db (في المجلد الحالي)"
            };

            return string.Join("\n", locations);
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
